// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: 2023 Axelar Network
// SPDX-FileCopyrightText: 2023 Snowfork <<EMAIL>>

pragma solidity 0.8.28;

interface IERC20Permit {
    error PermitExpired();
    error InvalidSignature();

    function DOMAIN_SEPARATOR() external view returns (bytes32);

    function nonces(address account) external view returns (uint256);

    function permit(
        address issuer,
        address spender,
        uint256 value,
        uint256 deadline,
        uint8 v,
        bytes32 r,
        bytes32 s
    ) external;
}
