// SPDX-License-Identifier: Apache-2.0
pragma solidity 0.8.28;

contract HelloWorld {
    event Said<PERSON><PERSON>(string indexed message);

    error Unauthorized();

    function say<PERSON><PERSON>(string memory _text) public payable {
        string memory fullMessage = string(abi.encodePacked("Hello there, ", _text));
        emit <PERSON><PERSON><PERSON>(fullMessage);
    }

    function revertUnauthorized() public pure {
        revert Unauthorized();
    }

    function retBomb() public pure returns (bytes memory) {
        assembly {
            return(1, 3000000)
        }
    }
}
