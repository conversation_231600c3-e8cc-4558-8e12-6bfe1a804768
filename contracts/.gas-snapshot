AgentTest:testInvoke() (gas: 20110)
AgentTest:testInvokeFail() (gas: 13333)
AgentTest:testInvokeUnauthorized() (gas: 15554)
BeefyClientTest:testCreateFinalBitfield() (gas: 482042)
BeefyClientTest:testCreateFinalBitfieldInvalid() (gas: 453566)
BeefyClientTest:testCreateInitialBitfield() (gas: 811378)
BeefyClientTest:testCreateInitialBitfieldInvalid() (gas: 743279)
BeefyClientTest:testFuzzComputeValidatorSetQuorum(uint128) (runs: 256, μ: 10528, ~: 10528)
BeefyClientTest:testFuzzInitializationValidation(uint128,uint128) (runs: 256, μ: 46109, ~: 46109)
BeefyClientTest:testFuzzSignatureSamplingRanges(uint128,uint16,uint16) (runs: 256, μ: 13021, ~: 13022)
BeefyClientTest:testRegenerateBitField() (gas: 206288)
BeefyClientTest:testScaleEncodeCommit() (gas: 29659)
BeefyClientTest:testSignatureSamplingCases() (gas: 8496)
BeefyClientTest:testStorageToStorageCopies() (gas: 4002706)
BeefyClientTest:testSubmit() (gas: 1595534)
BeefyClientTest:testSubmitFailForPrevRandaoCapturedMoreThanOnce() (gas: 453419)
BeefyClientTest:testSubmitFailForPrevRandaoTooEarlyOrTooLate() (gas: 394536)
BeefyClientTest:testSubmitFailInvalidSignature() (gas: 1408612)
BeefyClientTest:testSubmitFailValidatorNotInBitfield() (gas: 1396121)
BeefyClientTest:testSubmitFailWithInvalidBitfield() (gas: 1368379)
BeefyClientTest:testSubmitFailWithInvalidMMRLeaf() (gas: 1631530)
BeefyClientTest:testSubmitFailWithInvalidMMRLeafProof() (gas: 1635999)
BeefyClientTest:testSubmitFailWithInvalidTicket() (gas: 1398463)
BeefyClientTest:testSubmitFailWithInvalidValidatorProofWhenNotProvidingSignatureCount() (gas: 1444073)
BeefyClientTest:testSubmitFailWithInvalidValidatorSet() (gas: 1393492)
BeefyClientTest:testSubmitFailWithNotEnoughClaims() (gas: 380579)
BeefyClientTest:testSubmitFailWithStaleCommitment() (gas: 1390957)
BeefyClientTest:testSubmitFailWithoutPrevRandao() (gas: 1249298)
BeefyClientTest:testSubmitWith3SignatureCount() (gas: 1924422)
BeefyClientTest:testSubmitWithHandover() (gas: 1644136)
BeefyClientTest:testSubmitWithHandoverAnd3SignatureCount() (gas: 1970604)
BeefyClientTest:testSubmitWithHandoverAndOldBlockFailsWithStaleCommitment() (gas: 364742)
BeefyClientTest:testSubmitWithHandoverCountersAreCopiedCorrectly() (gas: 1671087)
BeefyClientTest:testSubmitWithHandoverFailStaleCommitment() (gas: 1391533)
BeefyClientTest:testSubmitWithHandoverFailWithInvalidValidatorProofWhenNotProvidingSignatureCount() (gas: 1446237)
BeefyClientTest:testSubmitWithHandoverFailWithInvalidValidatorSet() (gas: 1393826)
BeefyClientTest:testSubmitWithHandoverFailWithoutPrevRandao() (gas: 1276129)
BeefyClientTest:testSubmitWithOldBlockFailsWithStaleCommitment() (gas: 364308)
GatewayTest:testAgentExecution() (gas: 95570)
GatewayTest:testAgentExecutionBadOrigin() (gas: 23796)
GatewayTest:testAgentExecutionBadPayload() (gas: 22582)
GatewayTest:testCreateAgent() (gas: 255762)
GatewayTest:testCreateAgentAlreadyCreated() (gas: 257485)
GatewayTest:testCreateAgentWithNotEnoughGas() (gas: 88080)
GatewayTest:testCreateChannel() (gas: 285241)
GatewayTest:testCreateChannelFailsAgentDoesNotExist() (gas: 20220)
GatewayTest:testCreateChannelFailsChannelAlreadyExists() (gas: 289563)
GatewayTest:testDeriveChannelID() (gas: 3507)
GatewayTest:testDisableOutboundMessaging() (gas: 68930)
GatewayTest:testDisableOutboundMessagingForChannel() (gas: 242421)
GatewayTest:testGetters() (gas: 40174)
GatewayTest:testHandlersNotExternallyCallable() (gas: 53345)
GatewayTest:testInitializeNotExternallyCallable() (gas: 16720)
GatewayTest:testRegisterToken() (gas: 132103)
GatewayTest:testRegisterTokenReimbursesExcessFees() (gas: 144342)
GatewayTest:testRelayerNotRewarded() (gas: 315261)
GatewayTest:testRelayerRewardedFromAgent() (gas: 327113)
GatewayTest:testSendTokenAddress20() (gas: 236521)
GatewayTest:testSendTokenAddress20FailsInvalidDestination() (gas: 202673)
GatewayTest:testSendTokenAddress32() (gas: 239503)
GatewayTest:testSendTokenAddress32ToAssetHub() (gas: 237901)
GatewayTest:testSetOperatingMode() (gas: 41322)
GatewayTest:testSetPricingParameters() (gas: 58096)
GatewayTest:testSetTokenFees() (gas: 58412)
GatewayTest:testSubmitFailInvalidChannel() (gas: 36620)
GatewayTest:testSubmitFailInvalidNonce() (gas: 332578)
GatewayTest:testSubmitFailInvalidProof() (gas: 66338)
GatewayTest:testSubmitHappyPath() (gas: 327587)
GatewayTest:testUpdateChannel() (gas: 48709)
GatewayTest:testUpdateChannelFailDoesNotExist() (gas: 20538)
GatewayTest:testUpdateChannelSanityChecksForPrimaryGovernanceChannel() (gas: 19988)
GatewayTest:testUpgrade() (gas: 209061)
GatewayTest:testUpgradeFailCodeHashMismatch() (gas: 179525)
GatewayTest:testUpgradeFailOnInitializationFailure() (gas: 183235)
GatewayTest:testUpgradeGatewayMock() (gas: 299680)
GatewayTest:testUserDoesNotProvideEnoughFees() (gas: 314225)
GatewayTest:testUserPaysFees() (gas: 330698)
GatewayTest:testWithdrawAgentFunds() (gas: 66660)
MMRProofTest:testVerifyLeafProof() (gas: 371032)
MMRProofTest:testVerifyLeafProofFailsExceededProofSize() (gas: 359747)
MathTest:testFuzzMax(uint256,uint256) (runs: 256, μ: 3411, ~: 3411)
MathTest:testFuzzMin(uint256,uint256) (runs: 256, μ: 3340, ~: 3340)
MathTest:testFuzzSaturatingAdd(uint16,uint16) (runs: 256, μ: 673, ~: 667)
MathTest:testFuzzSaturatingSub(uint256,uint256) (runs: 256, μ: 670, ~: 692)
MathTest:testLog2WithWellKnownValues() (gas: 34294)
ScaleCodecTest:testCheckedEncodeCompactU32() (gas: 5010)
ScaleCodecTest:testEncodeCompactU32() (gas: 9227)
ScaleCodecTest:testEncodeU128() (gas: 192)
ScaleCodecTest:testEncodeU16() (gas: 434)
ScaleCodecTest:testEncodeU256() (gas: 478)
ScaleCodecTest:testEncodeU32() (gas: 170)
ScaleCodecTest:testEncodeU64() (gas: 280)
Uint16ArrayTest:testCounterCreatedAsZeroed() (gas: 53920)
Uint16ArrayTest:testCounterCreatedInitializationRoundsUp() (gas: 53994)
Uint16ArrayTest:testCounterGet() (gas: 70075)
Uint16ArrayTest:testCounterGetAndSetAlongEntireRange() (gas: 322196)
Uint16ArrayTest:testCounterGetAndSetWithTwoIterations() (gas: 114757)
Uint16ArrayTest:testCounterGetOutOfBounds() (gas: 52902)
Uint16ArrayTest:testCounterSet() (gas: 72117)
Uint16ArrayTest:testCounterSetOutOfBounds() (gas: 52805)
Uint16ArrayTest:testCounterWithLengthNotMultipleOf16() (gas: 75318)
VerificationTest:testCreateParachainHeaderMerkleFailInvalidHeader() (gas: 11372)
VerificationTest:testCreateParachainHeaderMerkleLeaf() (gas: 20743)
VerificationTest:testIsCommitmentInHeaderDigest() (gas: 21551)