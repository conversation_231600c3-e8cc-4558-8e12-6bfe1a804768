# Ethereum Contracts

## Overview

### Gateway

The Ethereum side of the bridge is organised around a central `Gateway` contract, responsible for the following:
* Receiving, verifying, and dispatching inbound messages from Polkadot
* Accepting outbound messages for delivery to Polkadot
* Higher-level application features such as token transfers

The gateway is defined by the following contracts
* [IGateway.sol](src/interfaces/IGateway.sol): Public API of the gateway
* [GatewayProxy.sol](src/Gateway.sol): Proxy contract for the gateway
* [Gateway.sol](src/Gateway.sol): Implementation contract of the gateway

The gateway is modular in design, being composed of libraries implementing specific functionality:

* [Verification.sol](src/Verification.sol): Verification of inbound messages from Polkadot
* [Assets.sol](src/Assets.sol): ERC-20 token transfers to Polkadot

#### Governance

Using cross-chain messaging, the gateway is governed remotely by the governance of the Polkadot relay chain.

#### Upgrades

The gateway consists of an upgradable proxy, and an implementation contract, loosely following the [ERC-1967](https://eips.ethereum.org/EIPS/eip-1967) standard.

### Agents

Agents are proxy contracts for arbitrary consensus systems on Polkadot. Logically, one can think of them as the sovereign accounts of remote consensus systems.

They have a number of uses:
* When an Ethereum user sends ERC-20 tokens to Polkadot (Specifically the AssetHub parachain), these tokens are actually locked into the agent contract corresponding to the AssetHub parachain. Then finally, on the AssetHub parachain, wrapped tokens are minted into an account controlled by the sender.
* When a Polkadot parachain wishes to call a function on an Ethereum contract, it will appear to the destination contract that the message sender is the agent contract for the origin parachain.
* Agents control the funds for receiving fees from users and disbursing rewards to message relayers

Agents are defined by the following contracts:
* [Agent.sol](src/Agent.sol): Agent contract
* [AgentExecutor.sol](src/AgentExecutor.sol): Code executed within an agent using `delegatecall`

The creation of new agents can be initiated by calling `EthereumSystem::create_agent` extrinsic on the BridgeHub parachain.

### BeefyClient

The [BeefyClient.sol](src/BeefyClient.sol) contract implements a light client for following a subset of Polkadot consensus. In conjunction with the [Verification.sol](src/Verification.sol) library, it used to verify inbound messages from the BridgeHub parachain.

## Testing

Run all tests:

```bash
forge test
```

Run coverage reports:

```bash
forge coverage
```

## Test Fixtures

Some of the unit tests require fixture data generated by a live deployment. For this purpose, we use logging artifacts from the offchain relayers running in the [E2E](../web/packages/test) stack.
```

BEEFY commitments & proofs extracted from `/tmp/snowbridge/beefy-relay.log`.

1. Search for `Sent SubmitFinal transaction` in relayer log file
2. Copy into `test/data/beefy-commitment.json`
```

## Deploy to Testnet

To deploy to a testnet, run script `deploy-test.sh`. Replace `--with-gas-price` with standard gas value (found at https://sepolia.beaconcha.in/gasnow). Make sure to add all the necessary envs in .envrc.
