[profile.default]
solc_version = "0.8.28"
optimizer = true
optimizer_runs = 20000
via_ir = false
test = 'test'
script = 'scripts'
fs_permissions = [
    { access = "read-write", path = "test/data" },
    { access = "read", path = "./" },
]

ignored_error_codes = [
    # DeployLocal.sol is never deployed
    5574,
    # tstore
    2394,
]

evm_version = 'Cancun'

[profile.production]
via_ir = true

[profile.production.etherscan]
mainnet = { key = "${ETHERSCAN_API_KEY}" }

[fmt]
number_underscore = "thousands"
line_length = 99
