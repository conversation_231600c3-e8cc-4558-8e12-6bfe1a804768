exp2.t.sol
├── when x is zero
│  └── it should return the unit number
└── when x is not zero
   ├── when x is negative
   │  ├── when x is less than a particular threshold
   │  │  └── it should return zero
   │  └── when x is greater than or equal to a particular threshold
   │     └── it should return the correct value
   └── when x is positive
      ├── when x is greater than the maximum permitted
      │  └── it should revert
      └── when x is less than the maximum permitted
         └── it should return the correct value
