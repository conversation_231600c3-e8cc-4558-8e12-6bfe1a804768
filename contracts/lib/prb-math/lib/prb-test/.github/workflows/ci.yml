name: "CI"

env:
  FOUNDRY_PROFILE: "ci"

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - "main"

jobs:
  lint:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v3"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Install Pnpm"
        uses: "pnpm/action-setup@v2"
        with:
          version: "8"

      - name: "Install Node.js"
        uses: "actions/setup-node@v3"
        with:
          cache: "pnpm"
          node-version: "lts/*"

      - name: "Install the Node.js dependencies"
        run: "pnpm install"

      - name: "Lint the contracts"
        run: "pnpm lint"

      - name: "Add lint summary"
        run: |
          echo "## Lint result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  build:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v3"
        with:
          submodules: "recursive"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Build the contracts and print their size"
        run: "forge build --sizes"

      - name: "Add build summary"
        run: |
          echo "## Build result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  test:
    needs: ["lint", "build"]
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v3"
        with:
          submodules: "recursive"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Run the tests"
        run: "forge test"

      - name: "Add test summary"
        run: |
          echo "## Tests result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  coverage:
    needs: ["lint", "build"]
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v3"
        with:
          submodules: "recursive"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Generate the coverage report"
        run: "forge coverage --report lcov"

      - name: "Upload coverage report to Codecov"
        uses: "codecov/codecov-action@v3"
        with:
          files: "./lcov.info"

      - name: "Add coverage summary"
        run: |
          echo "## Coverage result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Uploaded to Codecov" >> $GITHUB_STEP_SUMMARY
