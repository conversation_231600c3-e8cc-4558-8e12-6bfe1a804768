# Full reference https://github.com/foundry-rs/foundry/tree/master/config

[profile.default]
  auto_detect_solc = false
  evm_version = "paris"
  libs = ["lib"]
  gas_reports = ["*"]
  optimizer = false
  out = "out"
  solc = "0.8.19"
  src = "src"
  test = "test"
  verbosity = 2

[profile.default.fuzz]
  max_test_rejects = 100_000 # Number of times `vm.assume` can fail
  runs = 100

[profile.lite.fuzz]
  runs = 50

[profile.ci]
  fuzz = { runs = 1_000 }
  verbosity = 4

[fmt]
  bracket_spacing = true
  int_types = "long"
  line_length = 120
  multiline_func_header = "all"
  number_underscore = "thousands"
  quote_style = "double"
  tab_width = 4
  wrap_comments = true
