{"extends": "solhint:recommended", "rules": {"code-complexity": "off", "compiler-version": ["error", ">=0.8.19"], "const-name-snakecase": "off", "contract-name-camelcase": "off", "func-name-mixedcase": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "max-line-length": ["error", 132], "no-console": "off", "no-global-import": "off", "no-inline-assembly": "off", "not-rely-on-time": "off", "var-name-mixedcase": "off"}}