# Introduction

Snowbridge is a general-purpose, trustless, and decentralized bridge between Polkadot and Ethereum. It is part of the [Polkadot SDK](https://app.gitbook.com/o/bDGMcdShFBeGc3v6VzHf/s/tC80IPpnYgEJmgOYIpqZ/) and exists as a common-good bridge on BridgeHub. Ethereum contracts and other off-chain infrastructure (e.g. relayers, end-to-end tests) can be found in our [Github repository](https://github.com/snowfork/snowbridge).&#x20;

Snowbridge launched on the Polkadot mainnet in June 2024 ([https://app.snowbridge.network](https://app.snowbridge.network/)) and has been running smoothly ever since. The bridge's total locked value (TVL) exceeds [$70 million USD](https://etherscan.io/address/******************************************#internaltx).

Key features that set Snowbridge apart from other bridges:

* Owned by the Polkadot community
* Trustless, no multisigs
* Integrates seamlessly into AssetHub&#x20;
* Uses XCM as message format

The Snowbridge team continues to develop new features and enhance the bridge. For updates on Snowbridge, follow our [account on X](https://x.com/_snowbridge).

## Contact Us

To get in contact, please create a discussion thread [here](https://github.com/Snowfork/snowbridge/discussions).

## Disclaimer <a href="#block-058ec48ad6a34a669ee9fb6b7a60b7a1" id="block-058ec48ad6a34a669ee9fb6b7a60b7a1"></a>

There is no guarantee of functionality or safety. You use Snowbridge entirely at your own risk.
