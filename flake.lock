{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1710146030, "narHash": "sha256-SZ5L6eA7HJ/nmkzGG7/ISclqe6oZdOZTNoesiInkXPQ=", "owner": "numtide", "repo": "flake-utils", "rev": "b1d9ab70662946ef0850d488da1c9019f3a9752a", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_2": {"locked": {"lastModified": 1644229661, "narHash": "sha256-1YdnJAsNy69bpcjuoKdOYQX0YxZBiCYZo4Twxerqv7k=", "owner": "numtide", "repo": "flake-utils", "rev": "3cecb5b042f7f209c56ffd8371b2711a290ec797", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "foundry": {"inputs": {"flake-utils": "flake-utils_2", "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1754212248, "narHash": "sha256-sbmTD2L72nbZPFMT/GTHFt9N8/9AFi23FBY25wrt7zM=", "owner": "shazow", "repo": "foundry.nix", "rev": "b070c157f5dfcb4d67de1d1969668d6a633da652", "type": "github"}, "original": {"owner": "shazow", "ref": "monthly", "repo": "foundry.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1666753130, "narHash": "sha256-Wff1dGPFSneXJLI2c0kkdWTgxnQ416KE6X4KnFkgPYQ=", "owner": "NixOS", "repo": "nixpkgs", "rev": "f540aeda6f677354f1e7144ab04352f61aaa0118", "type": "github"}, "original": {"id": "nixpkgs", "type": "indirect"}}, "nixpkgs_2": {"locked": {"lastModified": 1723991338, "narHash": "sha256-Grh5PF0+gootJfOJFenTTxDTYPidA3V28dqJ/WV7iis=", "owner": "NixOS", "repo": "nixpkgs", "rev": "8a3354191c0d7144db9756a74755672387b702ba", "type": "github"}, "original": {"id": "nixpkgs", "ref": "nixos-unstable", "type": "indirect"}}, "root": {"inputs": {"flake-utils": "flake-utils", "foundry": "foundry", "nixpkgs": "nixpkgs_2"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}