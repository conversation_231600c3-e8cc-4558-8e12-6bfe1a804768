[package]
name = "snowbridge-smoketest"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
futures = "0.3.31"
tokio = { version = "1.44.2", features = ["macros", "rt-multi-thread", "time"] }
codec = { package = "parity-scale-codec", version = "3.7.4", default-features = false, features = ["bit-vec", "derive", "full"] }
scale-info = { features = ["derive"], version = "2.11.6", default-features = false }
hex = "0.4.3"
hex-literal = "0.4.1"
serde = { version = "1.0.210", features = ["derive"] }
subxt = { version = "0.43.0" }
subxt-signer = { version = "0.43.0" }
alloy = { version = "1.0.9", features = ["full"] }
lazy_static = "1.4.0"
sp-core = { version = "36.1.0" }
sp-crypto-hashing = { version = "0.1.0" }
sp-runtime = { version = "41.1.0" }

[features]
legacy-v1 = []
