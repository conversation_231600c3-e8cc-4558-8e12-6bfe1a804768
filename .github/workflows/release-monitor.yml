name: release-monitor

on:
  release:
    types: [published]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: snowfork/snowbridge-monitor

jobs:
  release-monitor:
    runs-on: snowbridge-runner
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v1
        with:
          fetch-depth: 2

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Build contracts
        working-directory: contracts
        run: forge build

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "22"

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 9

      - name: Build
        working-directory: web
        run: |
          pnpm install
          pnpm build

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./web
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:1.0
