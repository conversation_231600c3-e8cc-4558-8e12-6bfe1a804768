name: release-relayer

on:
  push:
    paths:
      - "relayer/**"
    branches:
      - main
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: snowfork/snowbridge-relay

jobs:
  release-relayer:
    runs-on: snowbridge-runner
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v1
        with:
          fetch-depth: 2

      - name: Setup go
        uses: actions/checkout@v4
        with:
          go-version: "^1.23"

      - name: Install Go tools
        run: >
          go install github.com/magefile/mage@v1.15.0 && 
          go install github.com/ferranbt/fastssz/sszgen@v0.1.3 &&
          go install github.com/ethereum/go-ethereum/cmd/abigen@v1.14.8

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Build contracts
        working-directory: contracts
        run: forge build

      - name: Install dependencies
        working-directory: relayer
        run: go mod download

      - name: Add gopath to bin
        run: echo "$HOME/go/bin" >> $GITHUB_PATH

      - name: Build
        working-directory: relayer
        run: CGO_ENABLED=1 GOOS=linux GOARCH=amd64 mage build

      - name: Configure Git
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'

      - name: Determine new version
        id: new_version
        run: |
          # Get the most recent tag in the format relayer-v<version>
          current_tag=$(git tag --list "relayer-v*" --sort=-v:refname | head -n 1)
          current_version=$(git tag --list "relayer-v*" --sort=-v:refname | head -n 1 | sed -E 's/relayer-v//')
          echo "Current version: $current_version"

          # If there is no current version, set it to 1.0.0
          if [ -z "$current_version" ]; then
          new_version="1.0.0"
          else
          new_version=$(npx semver $current_version -i patch)
          fi
            
          echo "New version: $new_version"
          echo "version=$new_version" >> $GITHUB_OUTPUT
          echo "from_tag=$current_tag" >> $GITHUB_OUTPUT

      - name: Create new tag
        id: create_tag
        run: |
          tag_name="relayer-v${{ steps.new_version.outputs.version }}"
          echo "Tag name: $tag_name"
          echo "tag=$tag_name" >> $GITHUB_OUTPUT
          git tag $tag_name

      - name: Push new tag
        run: |
          git push origin --tags

      - name: "Build Changelog"
        id: build_changelog
        uses: mikepenz/release-changelog-builder-action@v4
        with:
          configurationJson: |
            {
              "template": "#{{CHANGELOG}}\n\n<details>\n</details>",
              "categories": [
                {
                    "title": "## Relayer Changes",
                    "labels": ["Component: Relayer"]
                }
              ]
            }
          fromTag: ${{ steps.new_version.outputs.from_tag }}
          toTag: ${{ steps.create_tag.outputs.tag }}

      - name: Create a GitHub Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.create_tag.outputs.tag }}
          release_name: ${{ steps.create_tag.outputs.tag }}
          body: |
            ${{steps.build_changelog.outputs.changelog}}
          draft: false
          prerelease: false

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./relayer/build/snowbridge-relay
          asset_name: snowbridge-relay
          asset_content_type: application/octet-stream

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: ./relayer
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.create_tag.outputs.tag }}
