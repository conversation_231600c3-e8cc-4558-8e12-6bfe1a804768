[files]
extend-exclude = [
    "contracts/lib",
    "**/*.yaml",
    "**/*.json",
    "**/fixtures/*.js",
    "**/*ffi*.js",
    "**/*mock*.rs",
    "**/*data*.rs",
    "polkadot-sdk/bridges/bin",
    "polkadot-sdk/bridges/docs",
    "polkadot-sdk/bridges/modules",
    "polkadot-sdk/bridges/primitives",
    "polkadot-sdk/bridges/scripts",
    "polkadot-sdk/bridges/zombienet",
    "polkadot-sdk/cumulus",
    "polkadot-sdk/docker",
    "polkadot-sdk/docs",
    "polkadot-sdk/polkadot",
    "polkadot-sdk/prdoc",
    "polkadot-sdk/scripts",
    "polkadot-sdk/substrate",
    "smoketest/src/parachains",
    "smoketest/src/contracts",
    "lodestar/**",
    "go-ethereum/**",
    "go.work.sum",
]
