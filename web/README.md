# Core Typescript Packages

Packages:

- [Solidity Contracts](packages/contracts/README.md)
- [API Client](packages/api/README.md)
- [Integration tests](packages/test/README.md)

See the [main README's Development section](../README.md#Development) for setup instructions.

## Getting Started

Install all dependencies:

```bash
pnpm install
```

## Useful commands

Build all artifacts:

```bash
pnpm build
```

Run all unit tests:

```bash
pnpm test
```

Lint all the code:

```bash
pnpm lint
```
