{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "@snowbridge/contract-types": {"dependsOn": ["^build", "contracts#build"]}, "test": {"dependsOn": ["build"], "outputs": [], "inputs": ["src/**/*.ts", "test/**/*.ts"]}, "lint": {"outputs": []}, "format": {"outputs": []}, "size": {"outputs": []}, "coverage": {"dependsOn": ["test"], "outputs": []}}}