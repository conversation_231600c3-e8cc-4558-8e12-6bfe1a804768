{"name": "@snowbridge/web", "private": true, "engines": {"node": ">=20", "pnpm": ">=8"}, "scripts": {"preinstall": "npx only-allow pnpm", "build": "turbo run build --concurrency=1 --no-cache", "test": "turbo run test", "lint": "turbo run lint", "format": "turbo run format", "size": "turbo run size", "coverage": "turbo run coverage", "remixd": "./node_modules/.bin/remixd"}, "devDependencies": {"@remix-project/remixd": "^0.6.14", "turbo": "^1.9.4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}}