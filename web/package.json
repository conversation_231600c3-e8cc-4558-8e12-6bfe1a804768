{"name": "@snowbridge/web", "private": true, "engines": {"node": ">=20", "pnpm": ">=8"}, "scripts": {"preinstall": "npx only-allow pnpm", "build": "turbo run build --concurrency=1 --no-cache", "test": "turbo run test", "lint": "turbo run lint", "format": "turbo run format", "size": "turbo run size", "coverage": "turbo run coverage", "remixd": "./node_modules/.bin/remixd"}, "devDependencies": {"@remix-project/remixd": "^0.6.14", "turbo": "^1.9.4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}