{"timestamp": "2025-08-13T06:09:22.989Z", "environment": "local_e2e", "ethChainId": ********, "gatewayAddress": "******************************************", "assetHubParaId": 1000, "bridgeHubParaId": 1002, "relaychain": {"tokenSymbols": "WND", "tokenDecimals": 12, "ss58Format": 42, "isEthereum": false, "accountType": "AccountId32", "name": "Westend Local Testnet", "specName": "westend", "specVersion": 1019002}, "bridgeHub": {"tokenSymbols": "WND", "tokenDecimals": 12, "ss58Format": 42, "isEthereum": false, "accountType": "AccountId32", "name": "Westend BridgeHub Local", "specName": "bridge-hub-westend", "specVersion": 1019002}, "ethereumChains": {"********": {"chainId": ********, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON>", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "roc", "symbol": "roc", "decimals": 12, "foreignId": "0xbcd4282ca0c30cbd9c578b5c790e88c803d80cd9cc91f28686f24ac25a61e06e", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "wnd", "symbol": "wnd", "decimals": 12, "foreignId": "0x9441dceeeffa7e032eedaccf9b7632e60e86711551a82ffbbb0dda8afd9e4ef7", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "pal-2", "symbol": "pal-2", "decimals": 12, "foreignId": "0x17444ededa61bdbfcb1e5c39b2aed47f73b8970b65bbb0574c0a0ab1b0c99279", "deliveryGas": "bigint:80000"}}, "id": "sepolia", "baseDeliveryGas": "bigint:120000"}}, "parachains": {"1000": {"parachainId": 1000, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "WND", "tokenDecimals": 12, "ss58Format": 42, "isEthereum": false, "accountType": "AccountId32", "name": "Westend Asset Hub Local", "specName": "westmint", "specVersion": 1019002}, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:1", "symbol": "<PERSON><PERSON>", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "WETH", "minimumBalance": "bigint:1", "symbol": "WETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Roc", "symbol": "Roc", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"byGenesis": "0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781063e"}}]}}, "location": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"byGenesis": "0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781063e"}}]}}, "locationOnAH": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"byGenesis": "0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781063e"}}]}}, "foreignId": "0xbcd4282ca0c30cbd9c578b5c790e88c803d80cd9cc91f28686f24ac25a61e06e", "minimumBalance": "bigint:1", "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "symbol": "WND", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"byGenesis": "0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e"}}]}}, "location": {"parents": 1, "interior": "Here"}, "locationOnAH": {"parents": 1, "interior": "Here"}, "foreignId": "0x9441dceeeffa7e032eedaccf9b7632e60e86711551a82ffbbb0dda8afd9e4ef7", "minimumBalance": "bigint:1000000000", "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "pal-2", "symbol": "pal-2", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"byGenesis": "0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e"}}, {"parachain": 2000}, {"palletInstance": 50}, {"generalIndex": 2}]}}, "location": {"parents": 1, "interior": {"x3": [{"parachain": 2000}, {"palletInstance": 50}, {"generalIndex": 2}]}}, "locationOnAH": {"parents": 1, "interior": {"x3": [{"parachain": 2000}, {"palletInstance": 50}, {"generalIndex": 2}]}}, "foreignId": "0x17444ededa61bdbfcb1e5c39b2aed47f73b8970b65bbb0574c0a0ab1b0c99279", "minimumBalance": "bigint:1", "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:0", "estimatedDeliveryFeeDOT": "bigint:0"}, "2000": {"parachainId": 2000, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "undefined", "tokenDecimals": null, "ss58Format": 42, "isEthereum": false, "accountType": "AccountId32", "name": "<PERSON><PERSON>", "specName": "penpal-parachain", "specVersion": 1}, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:1", "symbol": "<PERSON><PERSON>", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "WETH", "minimumBalance": "bigint:1", "symbol": "WETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "pal-2", "symbol": "pal-2", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"byGenesis": "0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e"}}, {"parachain": 2000}, {"palletInstance": 50}, {"generalIndex": 2}]}}, "location": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 2}]}}, "locationOnAH": {"parents": 1, "interior": {"x3": [{"parachain": 2000}, {"palletInstance": 50}, {"generalIndex": 2}]}}, "foreignId": "0x17444ededa61bdbfcb1e5c39b2aed47f73b8970b65bbb0574c0a0ab1b0c99279", "minimumBalance": "bigint:1000000000", "isSufficient": false, "assetId": "2"}}, "estimatedExecutionFeeDOT": "bigint:3276800000", "estimatedDeliveryFeeDOT": "bigint:31450000000"}}}