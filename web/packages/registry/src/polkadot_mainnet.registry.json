{"timestamp": "2025-08-15T03:20:22.213Z", "environment": "polkadot_mainnet", "ethChainId": 1, "gatewayAddress": "******************************************", "assetHubParaId": 1000, "bridgeHubParaId": 1002, "relaychain": {"tokenSymbols": "DOT", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "<PERSON><PERSON>t", "specName": "polkadot", "specVersion": 1006001}, "bridgeHub": {"tokenSymbols": "DOT", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "Polkadot BridgeHub", "specName": "bridge-hub-polkadot", "specVersion": 1005001}, "ethereumChains": {"1": {"chainId": 1, "assets": {"******************************************": {"token": "******************************************", "name": "Staked USDe", "symbol": "sUSDe", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Lido DAO Token", "symbol": "LDO", "decimals": 18, "deliveryGas": "bigint:150000"}, "******************************************": {"token": "******************************************", "name": "Savings USDS", "symbol": "sUSDS", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Lombard Staked Bitcoin", "symbol": "LBTC", "decimals": 8, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Euro Coin", "symbol": "EURC", "decimals": 6, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "SKY Governance Token", "symbol": "SKY", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "MYTH", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Curio Gas Token", "symbol": "CGT2.0", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "OriginTrail TRAC", "symbol": "TRAC", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "tBTC v2", "symbol": "tBTC", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "symbol": "wstETH", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Wrapped TON Coin", "symbol": "TONCOIN", "decimals": 9, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Dai Stablecoin", "symbol": "DAI", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "SHIBA INU", "symbol": "SHIB", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Robonomics", "symbol": "XRT", "decimals": 9, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": 8, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "tBTC", "symbol": "TBTC", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "KILT", "symbol": "KILT", "decimals": 15, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Tether USD", "symbol": "USDT", "decimals": 6, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "ChainLink Token", "symbol": "LINK", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "symbol": "AAVE", "decimals": 18, "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>t", "symbol": "DOT", "decimals": 10, "foreignId": "0x4e241583d94b5d48a27a22064cd49b2ed6f5231d2d950e432f9b7c2e0ade52b2", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Kolkadot", "symbol": "KOL", "decimals": 12, "foreignId": "0xad050334b66c8d3abaac7ef6667e97e3e6f4a25d9b7b4765133290f0dc19aa6e", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "KSM", "decimals": 12, "foreignId": "0x03b6054d0c576dd8391e34e1609cf398f68050c23009d19ce93c000922bcd852", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "GAVUN WUD", "symbol": "WUD", "decimals": 10, "foreignId": "0x7ca757304cac2ff0881de18dc6a1dfa7f10e51b0cba0297e0e762f8072049c98", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "PINK", "symbol": "PINK", "decimals": 10, "foreignId": "0xbc8785969587ef3d22739d3385cb519a9e0133dd5da8d320c376772468c19be6", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "Integritee TEER", "symbol": "TEER", "decimals": 12, "foreignId": "0x3b7f577715347bdcde4739a1bf1a7f1dec71e8ff4dbe23a6a49348ebf920c658", "deliveryGas": "bigint:80000"}, "******************************************": {"token": "******************************************", "name": "DED", "symbol": "DED", "decimals": 10, "foreignId": "0x536917d1276896038c09bb6499bd0d7197e609983ec22e9ca4e75b394b23752b", "deliveryGas": "bigint:80000"}}, "id": "mainnet", "baseDeliveryGas": "bigint:120000"}, "1284": {"chainId": 1284, "evmParachainId": 2004, "assets": {"******************************************": {"token": "******************************************", "name": "Snowbridge WETH", "symbol": "WETH.e", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Snowbridge wstETH", "symbol": "wstETH.e", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Snowbridge WBTC", "symbol": "WBTC.e", "decimals": 8}, "******************************************": {"token": "******************************************", "name": "Snowbridge DAI", "symbol": "DAI.e", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Snowbridge USDC", "symbol": "USDC.e", "decimals": 6}, "******************************************": {"token": "******************************************", "name": "Snowbridge ETH", "symbol": "ETH.e", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Snowbridge USDT", "symbol": "USDT.e", "decimals": 6}, "******************************************": {"token": "******************************************", "name": "xcDOT", "symbol": "xcDOT", "decimals": 10}}, "precompile": "******************************************", "xcDOT": "******************************************", "xcTokenMap": {"******************************************": "******************************************", "******************************************": "******************************************", "******************************************": "******************************************", "******************************************": "******************************************", "******************************************": "******************************************", "******************************************": "******************************************", "******************************************": "******************************************"}, "id": "evm_moonbeam"}}, "parachains": {"1000": {"parachainId": 1000, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "DOT", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "<PERSON><PERSON><PERSON>", "specName": "statemint", "specVersion": 1005001}, "assets": {"******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "minimumBalance": "bigint:**************", "symbol": "WETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "USDC (Snowbridge)", "minimumBalance": "bigint:10000", "symbol": "USDC", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Curio Gas Token", "symbol": "CGT2.0", "decimals": 18, "minimumBalance": "bigint:1", "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:**************", "symbol": "ETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Tether USD (Snowbridge)", "minimumBalance": "bigint:10000", "symbol": "USDT", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "symbol": "DOT", "decimals": 10, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"polkadot": null}}]}}, "location": {"parents": 1, "interior": "Here"}, "locationOnAH": {"parents": 1, "interior": "Here"}, "foreignId": "0x4e241583d94b5d48a27a22064cd49b2ed6f5231d2d950e432f9b7c2e0ade52b2", "minimumBalance": "bigint:100000000", "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Kolkadot", "symbol": "KOL", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"polkadot": null}}, {"parachain": 1000}, {"palletInstance": 50}, {"generalIndex": 86}]}}, "location": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 86}]}}, "locationOnAH": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 86}]}}, "foreignId": "0xad050334b66c8d3abaac7ef6667e97e3e6f4a25d9b7b4765133290f0dc19aa6e", "minimumBalance": "bigint:**********000", "isSufficient": false, "assetId": "86"}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "KSM", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"kusama": null}}]}}, "location": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"kusama": null}}]}}, "locationOnAH": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"kusama": null}}]}}, "foreignId": "0x03b6054d0c576dd8391e34e1609cf398f68050c23009d19ce93c000922bcd852", "minimumBalance": "bigint:**********", "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "GAVUN WUD", "symbol": "WUD", "decimals": 10, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"polkadot": null}}, {"parachain": 1000}, {"palletInstance": 50}, {"generalIndex": 31337}]}}, "location": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 31337}]}}, "locationOnAH": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 31337}]}}, "foreignId": "0x7ca757304cac2ff0881de18dc6a1dfa7f10e51b0cba0297e0e762f8072049c98", "minimumBalance": "bigint:10000000", "isSufficient": false, "assetId": "31337"}, "******************************************": {"token": "******************************************", "name": "PINK", "symbol": "PINK", "decimals": 10, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"polkadot": null}}, {"parachain": 1000}, {"palletInstance": 50}, {"generalIndex": 23}]}}, "location": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 23}]}}, "locationOnAH": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 23}]}}, "foreignId": "0xbc8785969587ef3d22739d3385cb519a9e0133dd5da8d320c376772468c19be6", "minimumBalance": "bigint:1", "isSufficient": false, "assetId": "23"}, "******************************************": {"token": "******************************************", "name": "Integritee TEER", "symbol": "TEER", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x2": [{"globalConsensus": {"polkadot": null}}, {"parachain": 2039}]}}, "location": {"parents": 1, "interior": {"x1": [{"parachain": 2039}]}}, "locationOnAH": {"parents": 1, "interior": {"x1": [{"parachain": 2039}]}}, "foreignId": "0x3b7f577715347bdcde4739a1bf1a7f1dec71e8ff4dbe23a6a49348ebf920c658", "minimumBalance": "bigint:**********", "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "DED", "symbol": "DED", "decimals": 10, "locationOnEthereum": {"parents": 1, "interior": {"x4": [{"globalConsensus": {"polkadot": null}}, {"parachain": 1000}, {"palletInstance": 50}, {"generalIndex": 30}]}}, "location": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 30}]}}, "locationOnAH": {"parents": 0, "interior": {"x2": [{"palletInstance": 50}, {"generalIndex": 30}]}}, "foreignId": "0x536917d1276896038c09bb6499bd0d7197e609983ec22e9ca4e75b394b23752b", "minimumBalance": "bigint:1", "isSufficient": false, "assetId": "30"}}, "estimatedExecutionFeeDOT": "bigint:0", "estimatedDeliveryFeeDOT": "bigint:0"}, "2000": {"parachainId": 2000, "features": {"hasPalletXcm": true, "hasDryRunApi": false, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "ACA", "tokenDecimals": 12, "ss58Format": 10, "isEthereum": false, "accountType": "AccountId32", "name": "Acala", "specName": "acala", "specVersion": 2300}, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:**************", "symbol": "ETH", "decimals": 18, "isSufficient": false}}, "estimatedExecutionFeeDOT": "bigint:*********", "estimatedDeliveryFeeDOT": "bigint:*********"}, "2004": {"parachainId": 2004, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "GLMR", "tokenDecimals": 18, "ss58Format": 1284, "isEthereum": false, "accountType": "AccountId20", "evmChainId": 1284, "name": "Moonbeam", "specName": "moonbeam", "specVersion": 3800}, "xcDOT": "******************************************", "assets": {"******************************************": {"token": "******************************************", "name": "Snowbridge WETH", "minimumBalance": "bigint:1", "symbol": "WETH.e", "decimals": 18, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge wstETH", "minimumBalance": "bigint:1", "symbol": "wstETH.e", "decimals": 18, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge WBTC", "minimumBalance": "bigint:1", "symbol": "WBTC.e", "decimals": 8, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge DAI", "minimumBalance": "bigint:1", "symbol": "DAI.e", "decimals": 18, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge USDC", "minimumBalance": "bigint:1", "symbol": "USDC.e", "decimals": 6, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge ETH", "minimumBalance": "bigint:1", "symbol": "ETH.e", "decimals": 18, "isSufficient": true, "xc20": "******************************************"}, "******************************************": {"token": "******************************************", "name": "Snowbridge USDT", "minimumBalance": "bigint:1", "symbol": "USDT.e", "decimals": 6, "isSufficient": true, "xc20": "******************************************"}}, "estimatedExecutionFeeDOT": "bigint:*********", "estimatedDeliveryFeeDOT": "bigint:*********"}, "2030": {"parachainId": 2030, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "BNC", "tokenDecimals": 12, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "evmChainId": 996, "name": "<PERSON><PERSON><PERSON><PERSON>", "specName": "bifrost_polkadot", "specVersion": 20002}, "assets": {"******************************************": {"token": "******************************************", "name": "Wrapped ETH", "minimumBalance": "bigint:**************", "symbol": "WETH", "decimals": 18, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Native ETH", "minimumBalance": "bigint:**************", "symbol": "ETH", "decimals": 18, "isSufficient": false}}, "estimatedExecutionFeeDOT": "bigint:********", "estimatedDeliveryFeeDOT": "bigint:*********"}, "2034": {"parachainId": 2034, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "HDX", "tokenDecimals": 12, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "evmChainId": 222222, "name": "Hydration", "specName": "hydradx", "specVersion": 335}, "assets": {"******************************************": {"token": "******************************************", "name": "Ethereum", "minimumBalance": "bigint:*************", "symbol": "ETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "USDC (Ethereum native)", "minimumBalance": "bigint:10000", "symbol": "USDC", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Lido", "minimumBalance": "bigint:****************", "symbol": "LDO", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "SKY", "minimumBalance": "bigint:211685012701101000", "symbol": "SKY", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "OriginTrail", "minimumBalance": "bigint:27777777777777800", "symbol": "TRAC", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Chainlink", "minimumBalance": "bigint:436681222707424", "symbol": "LINK", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "minimumBalance": "bigint:8928571428571430", "symbol": "sUSDe", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "AAVE", "minimumBalance": "bigint:59084194977843", "symbol": "AAVE", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Wrapped Bitcoin", "minimumBalance": "bigint:23", "symbol": "WBTC", "decimals": 8, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Savings USDS", "minimumBalance": "bigint:9910802775024780", "symbol": "sUSDS", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Wrapped staked ETH", "minimumBalance": "bigint:3244646333550", "symbol": "wstETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON> (Ethereum native)", "minimumBalance": "bigint:10000", "symbol": "USDT", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Threshold BTC", "minimumBalance": "bigint:106803374987", "symbol": "tBTC", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Lombard Staked BTC", "minimumBalance": "bigint:11", "symbol": "LBTC", "decimals": 8, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Wrapped Ethereum", "minimumBalance": "bigint:*************", "symbol": "WETH", "decimals": 18, "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:1695406", "estimatedDeliveryFeeDOT": "bigint:*********"}, "3369": {"parachainId": 3369, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": false}, "info": {"tokenSymbols": "MYTH", "tokenDecimals": 18, "ss58Format": 29972, "isEthereum": true, "accountType": "AccountId20", "name": "<PERSON><PERSON><PERSON>", "specName": "mythos", "specVersion": 1016}, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON><PERSON>", "minimumBalance": "bigint:**************000", "symbol": "MYTH", "decimals": 18, "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:**********", "estimatedDeliveryFeeDOT": "bigint:*********"}}, "kusama": {"parachains": {"1000": {"parachainId": 1000, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "KSM", "tokenDecimals": 12, "ss58Format": 2, "isEthereum": false, "accountType": "AccountId32", "name": "<PERSON><PERSON><PERSON>", "specName": "statemine", "specVersion": 1006001}, "assets": {"******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "minimumBalance": "bigint:**************", "symbol": "WETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "USDC (Snowbridge)", "minimumBalance": "bigint:10000", "symbol": "USDC", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "Curio Gas Token", "symbol": "CGT2.0", "decimals": 18, "minimumBalance": "bigint:1", "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:**************", "symbol": "ETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "USDT (Snowbridge)", "minimumBalance": "bigint:10000", "symbol": "USDT", "decimals": 6, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>t", "symbol": "DOT", "decimals": 10, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"polkadot": null}}]}}, "location": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"Polkadot": null}}]}}, "locationOnAH": {"parents": 2, "interior": {"x1": [{"globalConsensus": {"Polkadot": null}}]}}, "foreignId": "0x4e241583d94b5d48a27a22064cd49b2ed6f5231d2d950e432f9b7c2e0ade52b2", "minimumBalance": "bigint:10000000", "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "symbol": "KSM", "decimals": 12, "locationOnEthereum": {"parents": 1, "interior": {"x1": [{"globalConsensus": {"kusama": null}}]}}, "location": {"parents": 1, "interior": "Here"}, "locationOnAH": {"parents": 1, "interior": "Here"}, "foreignId": "0x03b6054d0c576dd8391e34e1609cf398f68050c23009d19ce93c000922bcd852", "minimumBalance": "bigint:3333333", "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:0", "estimatedDeliveryFeeDOT": "bigint:0"}}, "assetHubParaId": 1000, "bridgeHubParaId": 1002}}