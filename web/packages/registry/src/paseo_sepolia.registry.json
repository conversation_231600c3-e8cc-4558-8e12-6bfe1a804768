{"timestamp": "2025-08-28T06:45:53.537Z", "environment": "paseo_sepolia", "ethChainId": ********, "gatewayAddress": "******************************************", "assetHubParaId": 1000, "bridgeHubParaId": 1002, "relaychain": {"tokenSymbols": "PAS", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "Paseo Testnet", "specName": "paseo", "specVersion": 1006001}, "bridgeHub": {"tokenSymbols": "PAS", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "Paseo Bridge Hub", "specName": "bridge-hub-paseo", "specVersion": 1006000}, "ethereumChains": {"********": {"chainId": ********, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "Wrapped PILT", "symbol": "wPILT", "decimals": 15}, "******************************************": {"token": "******************************************", "name": "Trace token", "symbol": "TRACE", "decimals": 18}, "******************************************": {"token": "******************************************", "name": "KILT", "symbol": "KILT", "decimals": 15}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18}}, "id": "sepolia"}}, "parachains": {"1000": {"parachainId": 1000, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "PAS", "tokenDecimals": 10, "ss58Format": 0, "isEthereum": false, "accountType": "AccountId32", "name": "<PERSON><PERSON><PERSON>", "specName": "asset-hub-paseo", "specVersion": 1006002}, "assets": {"******************************************": {"token": "******************************************", "name": "<PERSON><PERSON>", "minimumBalance": "bigint:**************", "symbol": "ETH", "decimals": 18, "isSufficient": true}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "", "minimumBalance": "bigint:1", "symbol": "", "decimals": 0, "isSufficient": false}, "******************************************": {"token": "******************************************", "name": "Wrapped Ether", "minimumBalance": "bigint:**************", "symbol": "WETH", "decimals": 18, "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:0", "estimatedDeliveryFeeDOT": "bigint:0"}, "2043": {"parachainId": 2043, "features": {"hasPalletXcm": true, "hasDryRunApi": false, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": true}, "info": {"tokenSymbols": "NEURO", "tokenDecimals": 12, "ss58Format": 101, "isEthereum": false, "accountType": "AccountId32", "name": "Neuro Testnet", "specName": "origintrail-parachain", "specVersion": 142}, "assets": {"******************************************": {"token": "******************************************", "name": "Trac", "minimumBalance": "bigint:****************", "symbol": "TRAC", "decimals": 18, "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:************", "estimatedDeliveryFeeDOT": "bigint:*********"}, "3369": {"parachainId": 3369, "features": {"hasPalletXcm": true, "hasDryRunApi": true, "hasTxPaymentApi": true, "hasDryRunRpc": true, "hasDotBalance": false}, "info": {"tokenSymbols": "MUSE", "tokenDecimals": 18, "ss58Format": 29972, "isEthereum": true, "accountType": "AccountId20", "name": "Muse Testnet", "specName": "muse", "specVersion": 1029}, "assets": {"******************************************": {"token": "******************************************", "name": "Muse", "minimumBalance": "bigint:****************0", "symbol": "MUSE", "decimals": 18, "isSufficient": true}}, "estimatedExecutionFeeDOT": "bigint:************", "estimatedDeliveryFeeDOT": "bigint:*********"}}}