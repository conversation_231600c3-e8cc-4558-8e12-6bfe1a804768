import { DOT_LOCATION, erc20Location } from "../xcmBuilder"
import { PNAMap } from "../assets_v2"
import { AssetMap } from "@snowbridge/base-types"
import { ParachainBase } from "./parachainBase"

export const MUSE_CHAIN_ID = ******** // Sepolia
export const MUSE_TOKEN_ID = "******************************************"
export const MYTHOS_CHAIN_ID = 1 // Ethereum Mainnet
export const MYTHOS_TOKEN_ID = "******************************************"

export class MythosParachain extends ParachainBase {
    getXC20DOT() {
        return undefined
    }

    async getLocationBalance(location: any, account: string, _pnaAssetId?: any): Promise<bigint> {
        if (
            this.specName === "muse" &&
            JSON.stringify(location) == JSON.stringify(erc20Location(MUSE_CHAIN_ID, MUSE_TOKEN_ID))
        ) {
            return await this.getNativeBalance(account)
        } else if (
            this.specName === "mythos" &&
            JSON.stringify(location) ==
                JSON.stringify(erc20Location(MYTHOS_CHAIN_ID, MYTHOS_TOKEN_ID))
        ) {
            return await this.getNativeBalance(account)
        } else {
            throw Error(
                `Cannot get balance for spec ${this.specName}. Location = ${JSON.stringify(
                    location
                )}`
            )
        }
    }

    getDotBalance(_account: string): Promise<bigint> {
        throw Error(`Cannot get DOT balance for spec ${this.specName}.`)
    }

    async getAssets(_ethChainId: number, _pnas: PNAMap): Promise<AssetMap> {
        const assets: AssetMap = {}
        if (this.specName === "muse") {
            assets[MUSE_TOKEN_ID.toLowerCase()] = {
                token: MUSE_TOKEN_ID.toLowerCase(),
                name: "Muse",
                minimumBalance: 10_000_000_000_000_000n,
                symbol: "MUSE",
                decimals: 18,
                isSufficient: true,
            }
        } else if (this.specName === "mythos") {
            assets[MYTHOS_TOKEN_ID.toLowerCase()] = {
                token: MYTHOS_TOKEN_ID.toLowerCase(),
                name: "Mythos",
                minimumBalance: 10_000_000_000_000_000n,
                symbol: "MYTH",
                decimals: 18,
                isSufficient: true,
            }
        } else {
            throw Error(
                `Cannot get balance for spec ${this.specName}. Location = ${JSON.stringify(
                    location
                )}`
            )
        }
        return assets
    }

    async calculateXcmFee(destinationXcm: any, asset: any): Promise<bigint> {
        if (JSON.stringify(asset) == JSON.stringify(DOT_LOCATION)) {
            console.warn(
                `${this.specName} does not support calculating fee for asset '${JSON.stringify(
                    asset
                )}'. Using default.`
            )
            return this.specName === "muse" ? 200_000_000_000n : 1_000_000_000n
        }
        return await this.calculateXcmFee(destinationXcm, asset)
    }
}
