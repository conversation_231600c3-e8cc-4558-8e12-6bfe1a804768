import { MUSE_TOKEN_ID, MY<PERSON><PERSON>_TOKEN_ID } from "./parachains/mythos"

export type Config = {
    BEACON_HTTP_API: string
    ETHEREUM_CHAINS: { [chain: string]: string }
    RELAY_CHAIN_URL: string
    GATEWAY_CONTRACT: string
    BEEFY_CONTRACT: string
    ASSET_HUB_PARAID: number
    BRIDGE_HUB_PARAID: number
    PRIMARY_GOVERNANCE_CHANNEL_ID: string
    SECONDARY_GOVERNANCE_CHANNEL_ID: string
    RELAYERS: Relayer[]
    PARACHAINS: { [paraId: string]: string }
    GRAPHQL_API_URL: string
}

export type KusamaConfig = {
    ASSET_HUB_PARAID: number
    BRIDGE_HUB_PARAID: number
    PARACHAINS: { [paraId: string]: string }
}

export type AddressType = "20byte" | "32byte" | "both"
export type SourceType = "substrate" | "ethereum"
export type Relayer = { name: string; account: string; type: SourceType; balance?: bigint }
export type ParachainInfo = {
    paraId: number
    destinationFeeDOT: bigint
    skipExistentialDepositCheck: boolean
    addressType: AddressType
    decimals: number
    maxConsumers: number
}
export type TransferToken = {
    id: string
    address: string
    minimumTransferAmount: bigint
}
export type TransferLocation = {
    id: string
    name: string
    type: SourceType
    destinationIds: string[]
    paraInfo?: ParachainInfo
    erc20tokensReceivable: TransferToken[]
}

export type SnowbridgeEnvironment = {
    config: Config
    kusamaConfig?: KusamaConfig
    name: string
    ethChainId: number
    /** @deprecated Use `assetsV2.getTransferLocations` instead */
    locations: TransferLocation[]
}

export const SNOWBRIDGE_ENV: { [id: string]: SnowbridgeEnvironment } = {
    local_e2e: {
        name: "local_e2e",
        ethChainId: ********,
        locations: [
            {
                id: "ethereum",
                name: "Ethereum",
                type: "ethereum",
                destinationIds: ["assethub", "penpal"],
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                ],
            },
            {
                id: "assethub",
                name: "Asset Hub",
                type: "substrate",
                destinationIds: ["ethereum"],
                paraInfo: {
                    paraId: 1000,
                    destinationFeeDOT: 0n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 12,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                ],
            },
            {
                id: "penpal",
                name: "Penpal",
                type: "substrate",
                destinationIds: ["ethereum"],
                paraInfo: {
                    paraId: 2000,
                    destinationFeeDOT: 4_000_000_000n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 12,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                ],
            },
        ],
        config: {
            BEACON_HTTP_API: "http://127.0.0.1:9596",
            ETHEREUM_CHAINS: {
                "********": "ws://127.0.0.1:8546",
            },
            RELAY_CHAIN_URL: "ws://127.0.0.1:9944",
            PARACHAINS: {
                "1000": "ws://127.0.0.1:12144",
                "1002": "ws://127.0.0.1:11144",
                "2000": "ws://127.0.0.1:13144",
            },
            GATEWAY_CONTRACT: "******************************************",
            BEEFY_CONTRACT: "******************************************",
            ASSET_HUB_PARAID: 1000,
            BRIDGE_HUB_PARAID: 1002,
            PRIMARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000001",
            SECONDARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000002",
            RELAYERS: [
                {
                    name: "beacon",
                    account: "5GWFwdZb6JyU46e6ZiLxjGxogAHe8SenX76btfq8vGNAaq8c",
                    type: "substrate",
                },
                {
                    name: "beefy",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-primary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-secondary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "execution-assethub",
                    account: "5DF6KbMTBPGQN6ScjqXzdB2ngk5wi3wXvubpQVUZezNfM6aV",
                    type: "substrate",
                },
                {
                    name: "parachain-assethub",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "execution-penpal",
                    account: "5HgmfVcc8xBUcReNJsUaJMhFWGkdYpEw4RiCX4SeZPdKXR6H",
                    type: "substrate",
                },
                {
                    name: "parachain-penpal",
                    account: "******************************************",
                    type: "ethereum",
                },
            ],
            GRAPHQL_API_URL: "http://127.0.0.1/does/not/exist",
        },
    },
    paseo_sepolia: {
        name: "paseo_sepolia",
        ethChainId: ********,
        locations: [
            {
                id: "ethereum",
                name: "Ethereum",
                type: "ethereum",
                destinationIds: ["assethub", "muse"],
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                    {
                        id: "PILT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                ],
            },
            {
                id: "assethub",
                name: "Asset Hub",
                type: "substrate",
                destinationIds: ["ethereum"],
                paraInfo: {
                    paraId: 1000,
                    destinationFeeDOT: 0n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 10,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                    {
                        id: "PILT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                ],
            },
            {
                id: "muse",
                name: "Muse",
                type: "substrate",
                destinationIds: [],
                paraInfo: {
                    paraId: 3369,
                    destinationFeeDOT: 200_000_000_000n,
                    skipExistentialDepositCheck: true,
                    addressType: "20byte",
                    decimals: 18,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "MUSE",
                        address: MUSE_TOKEN_ID,
                        minimumTransferAmount: 10_000_000_000_000_000n,
                    },
                ],
            },
        ],
        config: {
            BEACON_HTTP_API: "https://lodestar-sepolia.chainsafe.io",
            ETHEREUM_CHAINS: {
                "********": "https://ethereum-sepolia-rpc.publicnode.com",
            },
            RELAY_CHAIN_URL: "wss://paseo-rpc.dwellir.com",
            PARACHAINS: {
                "1000": "wss://asset-hub-paseo-rpc.dwellir.com",
                "1002": "wss://bridge-hub-paseo.dotters.network",
                "3369": "wss://paseo-muse-rpc.polkadot.io",
                "2043": `wss://parachain-testnet-rpc.origin-trail.network`,
            },
            GATEWAY_CONTRACT: "******************************************",
            BEEFY_CONTRACT: "******************************************",
            ASSET_HUB_PARAID: 1000,
            BRIDGE_HUB_PARAID: 1002,
            PRIMARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000001",
            SECONDARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000002",
            RELAYERS: [
                {
                    name: "beacon",
                    account: "5E4Hf7LzHE4W3jabjLWSP8p8RzEa9ednwRivFEwYAprzpgwc",
                    type: "substrate",
                },
                {
                    name: "beefy",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-primary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-secondary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "execution-assethub",
                    account: "5HT2ysqEg6SXghQ3NGXp1VWT22hhj48Um8UAwk6Udg8ZCEv8",
                    type: "substrate",
                },
                {
                    name: "parachain-assethub",
                    account: "******************************************",
                    type: "ethereum",
                },
            ],
            GRAPHQL_API_URL:
                "https://snowbridge.squids.live/snowbridge-subsquid-paseo@v1/api/graphql",
        },
    },
    polkadot_mainnet: {
        name: "polkadot_mainnet",
        ethChainId: 1,
        locations: [
            {
                id: "ethereum",
                name: "Ethereum",
                type: "ethereum",
                destinationIds: ["assethub", "mythos", "bifrost"],
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                    {
                        id: "WBTC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "SHIB",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "PEPE",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "TON",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "wstETH",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "tBTC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "USDC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "USDT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "DAI",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "KILT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "AAVE",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                ],
            },
            {
                id: "assethub",
                name: "Asset Hub",
                type: "substrate",
                destinationIds: ["ethereum"],
                paraInfo: {
                    paraId: 1000,
                    destinationFeeDOT: 0n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 10,
                    maxConsumers: 64,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                    {
                        id: "WBTC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "SHIB",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "PEPE",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "TON",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "wstETH",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "tBTC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "USDC",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "USDT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "DAI",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "KILT",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                    {
                        id: "AAVE",
                        address: "******************************************",
                        minimumTransferAmount: 1n,
                    },
                ],
            },
            {
                id: "mythos",
                name: "Mythos",
                type: "substrate",
                destinationIds: [],
                paraInfo: {
                    paraId: 3369,
                    destinationFeeDOT: 500_000_000n,
                    skipExistentialDepositCheck: true,
                    addressType: "20byte",
                    decimals: 18,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "MYTH",
                        address: MYTHOS_TOKEN_ID,
                        minimumTransferAmount: 10_000_000_000_000_000n,
                    },
                ],
            },
            {
                id: "bifrost",
                name: "Bifrost",
                type: "substrate",
                destinationIds: [],
                paraInfo: {
                    paraId: 2030,
                    destinationFeeDOT: 20_000_000n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 12,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                ],
            },
        ],
        config: {
            BEACON_HTTP_API: "https://lodestar-mainnet.chainsafe.io",
            ETHEREUM_CHAINS: {
                "1": "https://ethereum-rpc.publicnode.com",
                "1284": "https://rpc.api.moonbeam.network",
            },
            RELAY_CHAIN_URL: "https://polkadot-rpc.dwellir.com",
            PARACHAINS: {
                "1000": "wss://asset-hub-polkadot-rpc.dwellir.com",
                "1002": "https://bridge-hub-polkadot-rpc.dwellir.com",
                "3369": "wss://polkadot-mythos-rpc.polkadot.io",
                "2034": "wss://hydration-rpc.n.dwellir.com",
                "2030": "wss://bifrost-polkadot.ibp.network",
                "2004": "wss://moonbeam-rpc.n.dwellir.com",
                "2000": "wss://acala-rpc.dwellir.com",
                // TODO: Add back in jampton once we have an indexer in place.
                //"3397": "wss://rpc.jamton.network",
            },
            GATEWAY_CONTRACT: "******************************************",
            BEEFY_CONTRACT: "******************************************",
            ASSET_HUB_PARAID: 1000,
            BRIDGE_HUB_PARAID: 1002,
            PRIMARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000001",
            SECONDARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000002",
            RELAYERS: [
                {
                    name: "beacon",
                    account: "************************************************",
                    type: "substrate",
                },
                {
                    name: "beefy",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-primary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-secondary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "execution-assethub",
                    account: "************************************************",
                    type: "substrate",
                },
                {
                    name: "parachain-assethub",
                    account: "******************************************",
                    type: "ethereum",
                },
            ],
            GRAPHQL_API_URL:
                "https://snowbridge.squids.live/snowbridge-subsquid-polkadot:production/api/graphql",
        },
        kusamaConfig: {
            ASSET_HUB_PARAID: 1000,
            BRIDGE_HUB_PARAID: 1002,
            PARACHAINS: {
                "1000": "wss://asset-hub-kusama-rpc.dwellir.com",
                "1002": "https://bridge-hub-kusama-rpc.dwellir.com",
            },
        },
    },
    westend_sepolia: {
        name: "westend_sepolia",
        ethChainId: ********,
        locations: [
            {
                id: "ethereum",
                name: "Ethereum",
                type: "ethereum",
                destinationIds: ["assethub"],
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 1_000_000_000_000n,
                    },
                ],
            },
            {
                id: "assethub",
                name: "Asset Hub",
                type: "substrate",
                destinationIds: ["ethereum"],
                paraInfo: {
                    paraId: 1000,
                    destinationFeeDOT: 0n,
                    skipExistentialDepositCheck: false,
                    addressType: "32byte",
                    decimals: 12,
                    maxConsumers: 16,
                },
                erc20tokensReceivable: [
                    {
                        id: "WETH",
                        address: "******************************************",
                        minimumTransferAmount: 15_000_000_000_000n,
                    },
                ],
            },
        ],
        config: {
            BEACON_HTTP_API: "https://lodestar-sepolia.chainsafe.io",
            ETHEREUM_CHAINS: {
                "********": "https://ethereum-sepolia-rpc.publicnode.com",
            },
            RELAY_CHAIN_URL: "wss://westend-rpc.dwellir.com",
            PARACHAINS: {
                "1000": "wss://asset-hub-westend-rpc.dwellir.com",
                "1002": "wss://bridge-hub-westend-rpc.dwellir.com",
                "2313": `wss://node-7330371704012918784.nv.onfinality.io/ws?apikey=${
                    process.env["FREQUENCY_NODE_KEY"] ||
                    process.env["NEXT_PUBLIC_FREQUENCY_NODE_KEY"]
                }`,
            },
            GATEWAY_CONTRACT: "******************************************",
            BEEFY_CONTRACT: "******************************************",
            ASSET_HUB_PARAID: 1000,
            BRIDGE_HUB_PARAID: 1002,
            PRIMARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000001",
            SECONDARY_GOVERNANCE_CHANNEL_ID:
                "0x0000000000000000000000000000000000000000000000000000000000000002",
            RELAYERS: [
                {
                    name: "beacon",
                    account: "5E4Hf7LzHE4W3jabjLWSP8p8RzEa9ednwRivFEwYAprzpgwc",
                    type: "substrate",
                },
                {
                    name: "beefy",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-primary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "parachain-secondary-gov",
                    account: "******************************************",
                    type: "ethereum",
                },
                {
                    name: "execution-assethub",
                    account: "5E4Hf7LzHE4W3jabjLWSP8p8RzEa9ednwRivFEwYAprzpgwc",
                    type: "substrate",
                },
                {
                    name: "parachain-assethub",
                    account: "******************************************",
                    type: "ethereum",
                },
            ],
            GRAPHQL_API_URL:
                "https://snowbridge.squids.live/snowbridge-subsquid-westend@v1/api/graphql",
        },
    },
}
