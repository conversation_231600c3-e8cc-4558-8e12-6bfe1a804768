[settings]
node_spawn_timeout = 120
provider = "native"
timeout = 600

## Westend
[relaychain]
chain = "westend-local"
default_command = "{{output_bin_dir}}/polkadot"
default_args = [
    "-lbeefy=trace,mmr=trace,parachain=debug,xcm=trace",
    "--enable-offchain-indexing=true",
    "--pruning",
    "archive",
]

[[relaychain.nodes]]
name = "westend01"
validator = true
rpc_port = 9944
balance = 200000000000000000

[[relaychain.nodes]]
name = "westend02"
validator = true
balance = 200000000000000000

[[relaychain.nodes]]
name = "westend03"
validator = true
balance = 200000000000000000

[[relaychain.nodes]]
name = "westend04"
validator = true
balance = 200000000000000000

## Bridge Hub
[[parachains]]
id = 1002
chain = "bridge-hub-westend-local"
cumulus_based = true

    # run alice as parachain collator
    [[parachains.collators]]
    name = "bridgehub01"
    validator = true
    command = "{{output_bin_dir}}/polkadot-parachain"
    rpc_port = 11144
    args = [
        "--enable-offchain-indexing=true",
        "--pruning=archive",
        "--force-authoring",
        "-lparachain=trace,cumulus-collator=trace,aura=trace,xcm=trace,ethereum-beacon-client=trace,snowbridge-outbound-queue=trace,snowbridge-inbound-queue=trace,runtime=debug",
    ]

## Asset Hub
[[parachains]]
id = 1000
chain = "asset-hub-westend-local"
cumulus_based = true

    # run alice as parachain collator
    [[parachains.collators]]
    name = "assethub01"
    #validator = true
    command = "{{output_bin_dir}}/polkadot-parachain"
    rpc_port = 12144
    args = [
        "--force-authoring",
        "-lparachain=debug,xcm=trace,runtime::bridge-assets-transfer=trace,runtime::assets=trace,runtime::bridge-transfer=trace",
    ]

## Penpal
[[parachains]]
id = 2000
chain = "penpal-westend-2000"
cumulus_based = true

    [[parachains.collators]]
    name = "penpal01"
    validator = true
    command = "{{output_bin_dir}}/polkadot-parachain"
    rpc_port = 13144
    args = [
        "--force-authoring",
        "-lxcm=trace,runtime::assets=trace",
    ]
