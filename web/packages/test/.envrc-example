source_up_if_exists

# Overrides for E2E stack

## Substrate config
# export BEEFY_START_BLOCK=           # Start block to configure beefy client, default value: 1
# export RELAYCHAIN_ENDPOINT=         # Relaychain endpoint, default value: ws://localhost:9944
# export PARACHAIN_RUNTIME=           # Runtime type of parachain should be one of snowbase|snowblink|snowbridge
# export BRIDGE_HUB_PALLETS_OWNER=     # Pubkey of bridge owner that can halt/resume the bridge pallets, default value: 0xd43593c715fdd31c61141abd04a99fd6822c8558854ccde39a5684e7a56da27d (Alice test account)
# export BASE_FEE=                     # BaseFee to cover the cost of processing in bridgeHub default value: 0.1 DOT
# export ETH_BRIDGE_HUB_INITIAL_DEPOSIT= # Initial deposit funding the sovereign account for the agent(in Wei)

## Eth config for production
# export INFURA_PROJECT_ID=            # Your Infura project id
# export ETH_NETWORK=sepolia
# export ETH_NETWORK_ID=********
# export FOUNDRY_PROFILE=production
# export ETHERSCAN_API_KEY=             # Your etherscan api key
# export ETH_RPC_ENDPOINT=https://sepolia.infura.io/v3
# export ETH_WS_ENDPOINT=wss://sepolia.infura.io/ws/v3
# export ETH_WRITER_ENDPOINT=https://rpc-sepolia.flashbots.net
# export BEACON_HTTP_ENDPOINT=https://lodestar-sepolia.chainsafe.io
# export ACTIVE_SPEC=mainnet            # For real network just uncomment and set as mainnet here
# export DEPLOYER_ETH_KEY=              # Your private key to deploy contracts
# export BEEFY_RELAY_ETH_KEY=           # Your Beefy relayer account private key
# export PARACHAIN_RELAY_ETH_KEY=       # Your Parachain relayer account private key
# export E2E_TEST_ETH_KEY=              # Your E2E test account private key
# export ETH_RANDAO_DELAY=128           # 4 epochs=128 slots=25.6mins
# export ETH_RANDAO_EXP=6               # 6 slots before expired
# export MINIMUM_REQUIRED_SIGNATURES=16 # The minimum number of signatures. See ~/scripts/beefy_signature_sampling.py

## For local dev setup
# export SKIP_RELAYER=false           # For local setup only, set as true to skip starting relayer so to manually debug later
# export ETH_FAST_MODE=false          # For local setup only, set as true will hack beacon client for fast slot time
# export REBUILD_LODESTAR=false
