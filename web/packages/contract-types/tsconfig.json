{"compilerOptions": {"composite": true, "target": "es2021", "module": "commonjs", "strict": true, "resolveJsonModule": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "baseUrl": ".", "rootDir": "src", "noEmitOnError": true, "skipLibCheck": true, "allowJs": true}, "ts-node": {"require": ["tsconfig-paths/register"]}, "exclude": ["node_modules", "dist"], "include": ["src/**/*.ts"], "references": []}