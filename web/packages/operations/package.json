{"name": "@snowbridge/operations", "version": "1.0.1", "description": "Snowbridge Operational Scripts", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/Snowfork/snowbridge.git", "directory": "web/packages/operations"}, "scripts": {"build": "tsc", "lint": "eslint src/*", "format": "prettier src --write", "initializeAlarms": "npx ts-node src/main.ts init", "initializeAlarmsFromContainer": "docker run -v $(pwd)/config:/config --rm snowbridge-monitor:latest init", "monitor": "npx ts-node src/main.ts start", "historyV2": "npx ts-node src/global_transfer_history_v2.ts", "buildTokenRegistry": "npx ts-node src/build_asset_registry.ts", "transferWethFromPolkadotToKusama": "npx ts-node src/transfer_for_kusama_entry.ts WEthPToK 0 WETH 200000000000000", "transferEthFromPolkadotToKusama": "npx ts-node src/transfer_for_kusama_entry.ts EthPToK 0 ETH 200000000000000", "transferDotFromPolkadotToKusama": "npx ts-node src/transfer_for_kusama_entry.ts DotPToK 0 DOT 200000000000000", "transferKsmFromPolkadotToKusama": "npx ts-node src/transfer_for_kusama_entry.ts KsmPToK 0 KSM 200000000000000", "transferWethFromKusamaToPolkadot": "npx ts-node src/transfer_for_kusama_entry.ts WEthKToP 1 WETH 200000000000000", "transferEthFromKusamaToPolkadot": "npx ts-node src/transfer_for_kusama_entry.ts EthKToP 1 ETH 200000000000000", "transferDotFromKusamaToPolkadot": "npx ts-node src/transfer_for_kusama_entry.ts DotKToP 1 DOT 200000000000000", "transferKsmFromKusamaToPolkadot": "npx ts-node src/transfer_for_kusama_entry.ts KsmKToP 1 KSM 200000000000000", "transferEtherToAH": "npx ts-node src/transfer_from_e2p.ts 1000 Eth 200000000000000", "transferEtherFromAH": "npx ts-node src/transfer_from_p2e.ts 1000 Eth 100000000000000", "transferDotToEthereum": "npx ts-node src/transfer_from_p2e.ts 1000 DOT 2000000000", "transferDotToAH": "npx ts-node src/transfer_from_e2p.ts 1000 DOT 1000000000", "transferWndToEthereum": "npx ts-node src/transfer_from_p2e.ts 1000 WND 2000000000", "transferWndToAH": "npx ts-node src/transfer_from_e2p.ts 1000 WND 1000000000", "transferPenpalTokenToEthereum": "npx ts-node src/transfer_from_p2e.ts 2000 pal 2000000000", "transferPenpalTokenToAH": "npx ts-node src/transfer_from_e2p.ts 1000 pal 1000000000", "transferXQCYToEthereum": "npx ts-node src/transfer_from_p2e.ts 2313 xrqcy 1000000", "transferXQCYFromEthereumToAH": "npx ts-node src/transfer_from_e2p.ts 1000 xrqcy 100000", "registerWeth": "npx ts-node src/register_erc20.ts ******************************************", "transferWethToAH": "npx ts-node src/transfer_from_e2p.ts 1000 WETH 200000000000000", "transferWndToEthereumV2": "npx ts-node src/transfer_from_p2e_v2.ts 1000 WND 2000000000", "transferEtherFromAHV2": "npx ts-node src/transfer_from_p2e_v2.ts 1000 Eth 100000000000000", "transferWEthFromAHV2": "npx ts-node src/transfer_from_p2e_v2.ts 1000 WETH 100000000000000", "transferRocToEthereumV2": "npx ts-node src/transfer_from_p2e_v2.ts 1000 Roc 2000000000", "transferPenpalTokenFromAHToEthereumV2": "npx ts-node src/transfer_from_p2e_v2.ts 1000 pal 2000000000", "transferPenpalTokenToEthereumV2": "npx ts-node src/transfer_from_p2e_v2.ts 2000 pal 2000000000", "transferEtherFromPenpalV2": "npx ts-node src/transfer_from_p2e_v2.ts 2000 ETH 100000000000000", "transferWEthFromPenpalV2": "npx ts-node src/transfer_from_p2e_v2.ts 2000 WETH 100000000000000", "transferWndToEthereumV2WithFee": "npx ts-node src/transfer_from_p2e_v2_with_fee.ts 1000 WND 0 2000000000", "transferWEthFromAHV2WithFee": "npx ts-node src/transfer_from_p2e_v2_with_fee.ts 1000 WETH 0 100000000000000", "transferPenpalTokenToEthereumV2WithFee": "npx ts-node src/transfer_from_p2e_v2_with_fee.ts 2000 pal 0 2000000000", "transferPenpalTokenToEthereumV2WithNativeFee": "npx ts-node src/transfer_from_p2e_v2_with_fee.ts 2000 pal 1 2000000000", "transferTRACToNeuroWeb": "npx ts-node src/transfer_from_e2p.ts 2043 TRACE 3000000000000000", "transferTRACFromNeuroWebtoEthereum": "npx ts-node src/transfer_from_p2e.ts 2043 TRACE 3000000000000000", "wrapSnowTRAC": "npx ts-node src/wrap_snow_trac.ts", "unwrapSnowTRAC": "npx ts-node src/unwrap_snow_trac.ts"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}, "dependencies": {"@aws-sdk/client-cloudwatch": "^3.574.0", "@ethersproject/abi": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/providers": "^5.7.2", "@ethersproject/units": "^5.7.0", "@fastify/rate-limit": "^9.1.0", "@polkadot/api": "^15.10.2", "@polkadot/keyring": "^13.4.4", "@polkadot/types": "^15.10.2", "@polkadot/types-codec": "^15.10.2", "@polkadot/util": "^13.4.4", "@polkadot/util-crypto": "^13.4.4", "@snowbridge/api": "workspace:*", "@snowbridge/registry": "workspace:*", "@snowbridge/contract-types": "workspace:*", "@types/keccak": "^3.0.4", "@types/lodash": "^4.17.0", "@types/node": "^18.19.31", "@types/node-cron": "^3.0.11", "@types/secp256k1": "^4.0.6", "@types/seedrandom": "^3.0.8", "axios": "^1.6.8", "bitfield": "^4.2.0", "dotenv": "^16.4.5", "ethers": "^6.13.5", "fastify": "^4.27.0", "keccak": "^3.0.4", "lodash": "^4.17.21", "merkletreejs": "^0.2.32", "node-cron": "^3.0.3", "rlp": "^2.2.7", "secp256k1": "^4.0.3", "seedrandom": "^3.0.5"}}