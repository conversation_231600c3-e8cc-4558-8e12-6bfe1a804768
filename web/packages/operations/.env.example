# Required Envs

## Network config(polkadot_mainnet|westend_sepolia|paseo_sepolia)
NODE_ENV=

## AWS keys
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

## Infura keys
REACT_APP_INFURA_KEY=


# Optional Envs with default value

## Scan interval for the monitor(in minutes)
SCAN_INTERVAL=15

## AWS config
### The region
AWS_REGION=eu-central-1
### The SNS topic for integration with PagerDuty to handle bridge/channel stale alarms
BRIDGE_STALE_SNS_TOPIC=arn:aws:sns:eu-central-1:************:PD
### The SNS topic for integration with PagerDuty to handle balance-insufficient alarms
ACCOUNT_BALANCE_SNS_TOPIC=arn:aws:sns:eu-central-1:************:PD-WALLET

## The Dashboard URL is included in the alarm description, allowing easy navigation to the dashboard for more details.
### Latency dashboard URL
LATENCY_DASHBOARD_URL=https://eu-central-1.console.aws.amazon.com/cloudwatch/home?region=eu-central-1#dashboards/dashboard/Latency
### Balance dashboard URL
BALANCE_DASHBOARD_URL=https://eu-central-1.console.aws.amazon.com/cloudwatch/home?region=eu-central-1#dashboards/dashboard/Balance

## Latency Threshold
### Beefy latency
BlockLatencyToEthereum=1800
### Beacon latency
BlockLatencyToPolkadot=120

## Alarm Evaluation Configs
ToEthereumEvaluationPeriods=18
ToEthereumDatapointsToAlarm=15
ToPolkadotEvaluationPeriods=12
ToPolkadotDatapointsToAlarm=10

## Balance threshold for sovereign account of AH
SubstrateBalanceThreshold=*************

## GRAPHQL API endpoint for the indexer
GRAPHQL_API_URL=https://snowbridge.squids.live/snowbridge-subsquid-polkadot:production/api/graphql


DRY_RUN=false
