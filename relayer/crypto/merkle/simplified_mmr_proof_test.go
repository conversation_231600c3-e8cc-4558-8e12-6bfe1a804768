package merkle

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/snowfork/go-substrate-rpc-client/v4/types"
	"github.com/stretchr/testify/assert"
)

type SimplifiedProofTestData struct {
	ReferenceSimplifiedProof   SimplifiedMMRProof
	ReferenceMMRRoot           types.H256
	LeafHash                   types.H256
	LeafIndex                  uint64
	LeafCount                  uint64
	MMRProof                   []types.H256
	SimplifiedMerkleProofItems []types.H256
	SimplifiedMerkleProofOrder uint64
}

type SimplifiedProofFixture struct {
	MerkleProofItems []string `json:"merkle_proof_items"`
	MerkleProofOrder uint64   `json:"merkle_proof_order"`
}

func convertSimplifiedProofToFixture(proof SimplifiedMMRProof) SimplifiedProofFixture {
	merkleProofItems := make([]string, len(proof.MerkleProofItems))
	for i, item := range proof.MerkleProofItems {
		merkleProofItems[i] = fmt.Sprintf("%#x", item)
	}

	return SimplifiedProofFixture{
		MerkleProofItems: merkleProofItems,
		MerkleProofOrder: proof.MerkleProofOrder,
	}
}

// Use this function when you want to import the test data generated by reference library
func sanitizeIncomingTestData(testData []SimplifiedProofTestData) {
	for i := 0; i < len(testData); i++ {
		if len(testData[i].ReferenceSimplifiedProof.MerkleProofItems) == 0 {
			testData[i].ReferenceSimplifiedProof.MerkleProofItems = nil
		}
		if len(testData[i].SimplifiedMerkleProofItems) == 0 {
			testData[i].SimplifiedMerkleProofItems = nil
		}
	}
}

func Test_SimplifiedMMRProof(t *testing.T) {
	var mmrProofs []types.H256
	mmrProofItems := []string{
		"c426c89e1c1dc353199d4b1b0005b796b21fc1cf9eb857071e888397a7316f62",
		"d5aadcbd22b9abaa88742b53092da93fdde41704493b644b7ea4a2bd87e26624",
		"735740aabe5560339612a84518300fed3ba372721fd57f2b3376114be1c4a15d",
	}
	for _, item := range mmrProofItems {
		out, err := hex.DecodeString(item)
		assert.NoError(t, err)
		mmrProofs = append(mmrProofs, types.NewH256(out))
	}
	simplifiedMMRProof, err := ConvertToSimplifiedMMRProof(types.H256{}, 1048, types.MMRLeaf{}, 1049, mmrProofs)
	assert.NoError(t, err)
	fixture := convertSimplifiedProofToFixture(simplifiedMMRProof)
	prettyOut, err := json.MarshalIndent(fixture, "", "\t")
	assert.NoError(t, err)
	fmt.Println(string(prettyOut))

	// This test data was imported from Rust and validated using nervos library reference implementation
	// Reason to test it like this is to avoid un-necessarily writing verification code in golang
	byteValue, err := os.ReadFile("fixture.json")
	assert.NoError(t, err)

	var testData []SimplifiedProofTestData
	err = json.Unmarshal(byteValue, &testData)
	if err != nil {
		t.Fail()
	}
	for i := 0; i < len(testData); i++ {
		fmt.Printf("Testing for: LeafIndex; %d, LeafCount: %d\n", testData[i].LeafIndex, testData[i].LeafCount)
		simplifiedProof, err := ConvertToSimplifiedMMRProof(testData[i].LeafHash, testData[i].LeafIndex, types.MMRLeaf{}, testData[i].LeafCount, testData[i].MMRProof)
		assert.NoError(t, err)
		assert.Equal(t, testData[i].SimplifiedMerkleProofOrder, simplifiedProof.MerkleProofOrder)
		assert.Equal(t, testData[i].SimplifiedMerkleProofItems, simplifiedProof.MerkleProofItems)
		assert.Equal(t, testData[i].ReferenceMMRRoot, CalculateMerkleRoot(&simplifiedProof, testData[i].LeafHash))

		fmt.Printf("\nRoot: %#x\n", testData[i].ReferenceMMRRoot)
		fmt.Printf("Leaf Node hash: %#x\n", testData[i].LeafHash)

		// Printing solidity fixture
		fixture := convertSimplifiedProofToFixture(simplifiedProof)
		prettyOut, err := json.MarshalIndent(fixture, "", "\t")
		assert.NoError(t, err)
		fmt.Println(string(prettyOut))
	}
}
