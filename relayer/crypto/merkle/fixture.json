[{"ReferenceSimplifiedProof": {"MerkleProofItems": null, "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 1, "MMRProof": [], "SimplifiedMerkleProofItems": null, "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 2, "MMRProof": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53]], "SimplifiedMerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53]], "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], "LeafHash": [225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], "LeafIndex": 1, "LeafCount": 2, "MMRProof": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233]], "SimplifiedMerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233]], "SimplifiedMerkleProofOrder": 1}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [241, 73, 13, 54, 133, 142, 4, 144, 199, 36, 17, 19, 233, 80, 163, 20, 56, 255, 45, 46, 110, 144, 115, 7, 57, 54, 116, 169, 19, 219, 60, 200], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 5, "MMRProof": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofOrder": 4}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [241, 73, 13, 54, 133, 142, 4, 144, 199, 36, 17, 19, 233, 80, 163, 20, 56, 255, 45, 46, 110, 144, 115, 7, 57, 54, 116, 169, 19, 219, 60, 200], "LeafHash": [225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], "LeafIndex": 1, "LeafCount": 5, "MMRProof": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofOrder": 5}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [241, 73, 13, 54, 133, 142, 4, 144, 199, 36, 17, 19, 233, 80, 163, 20, 56, 255, 45, 46, 110, 144, 115, 7, 57, 54, 116, 169, 19, 219, 60, 200], "LeafHash": [123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], "LeafIndex": 2, "LeafCount": 5, "MMRProof": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofOrder": 6}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [241, 73, 13, 54, 133, 142, 4, 144, 199, 36, 17, 19, 233, 80, 163, 20, 56, 255, 45, 46, 110, 144, 115, 7, 57, 54, 116, 169, 19, 219, 60, 200], "LeafHash": [140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], "LeafIndex": 3, "LeafCount": 5, "MMRProof": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "SimplifiedMerkleProofOrder": 7}, {"ReferenceSimplifiedProof": {"MerkleProofItems": null, "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [241, 73, 13, 54, 133, 142, 4, 144, 199, 36, 17, 19, 233, 80, 163, 20, 56, 255, 45, 46, 110, 144, 115, 7, 57, 54, 116, 169, 19, 219, 60, 200], "LeafHash": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "LeafIndex": 4, "LeafCount": 5, "MMRProof": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "SimplifiedMerkleProofItems": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 7, "MMRProof": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofOrder": 4}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], "LeafIndex": 1, "LeafCount": 7, "MMRProof": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofOrder": 5}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], "LeafIndex": 2, "LeafCount": 7, "MMRProof": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofOrder": 6}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], "LeafIndex": 3, "LeafCount": 7, "MMRProof": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [241, 167, 130, 73, 6, 66, 154, 228, 43, 178, 169, 227, 98, 88, 232, 38, 142, 238, 223, 8, 167, 173, 195, 57, 72, 65, 160, 99, 108, 238, 212, 68]], "SimplifiedMerkleProofOrder": 7}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], "MMRRestOfThePeaks": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "LeafIndex": 4, "LeafCount": 7, "MMRProof": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122]], "SimplifiedMerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "SimplifiedMerkleProofOrder": 2}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], "MMRRestOfThePeaks": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], "LeafIndex": 5, "LeafCount": 7, "MMRProof": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122]], "SimplifiedMerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "SimplifiedMerkleProofOrder": 3}, {"ReferenceSimplifiedProof": {"MerkleProofItems": null, "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [248, 95, 39, 91, 107, 6, 194, 51, 252, 98, 236, 181, 153, 44, 211, 179, 57, 105, 130, 236, 239, 156, 149, 8, 230, 21, 198, 245, 40, 200, 252, 37], "LeafHash": [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], "LeafIndex": 6, "LeafCount": 7, "MMRProof": [[181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222]], "SimplifiedMerkleProofItems": [[47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 15, "MMRProof": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 8}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], "LeafIndex": 1, "LeafCount": 15, "MMRProof": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 9}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], "LeafIndex": 2, "LeafCount": 15, "MMRProof": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 10}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], "LeafIndex": 3, "LeafCount": 15, "MMRProof": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 11}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "MerkleProofOrder": 4, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "LeafIndex": 4, "LeafCount": 15, "MMRProof": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 12}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "MerkleProofOrder": 5, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], "LeafIndex": 5, "LeafCount": 15, "MMRProof": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 13}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "MerkleProofOrder": 6, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], "LeafIndex": 6, "LeafCount": 15, "MMRProof": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 14}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87]], "MerkleProofOrder": 7, "MMRRightBaggedPeak": [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], "LeafIndex": 7, "LeafCount": 15, "MMRProof": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofItems": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [108, 236, 88, 27, 167, 46, 240, 168, 180, 140, 10, 5, 250, 157, 201, 4, 119, 80, 50, 173, 173, 186, 200, 61, 16, 178, 219, 223, 5, 162, 248, 167]], "SimplifiedMerkleProofOrder": 15}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], "LeafIndex": 8, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217]], "SimplifiedMerkleProofItems": [[47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 4}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], "LeafIndex": 9, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217]], "SimplifiedMerkleProofItems": [[157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 5}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], "LeafIndex": 10, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217]], "SimplifiedMerkleProofItems": [[216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 6}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], "LeafIndex": 11, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217]], "SimplifiedMerkleProofItems": [[44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [53, 198, 42, 0, 173, 102, 173, 85, 222, 242, 24, 114, 40, 139, 248, 22, 221, 144, 107, 200, 198, 82, 122, 228, 13, 109, 119, 35, 120, 35, 219, 217], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 7}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], "LeafIndex": 12, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217]], "SimplifiedMerkleProofItems": [[215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 2}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], "LeafIndex": 13, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217]], "SimplifiedMerkleProofItems": [[199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 3}, {"ReferenceSimplifiedProof": {"MerkleProofItems": null, "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [54, 43, 32, 18, 68, 248, 236, 49, 79, 73, 149, 145, 138, 199, 10, 25, 186, 129, 141, 77, 65, 231, 140, 150, 52, 255, 109, 40, 26, 243, 196, 193], "LeafHash": [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], "LeafIndex": 14, "LeafCount": 15, "MMRProof": [[193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90]], "SimplifiedMerkleProofItems": [[111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5]], "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], "LeafIndex": 0, "LeafCount": 60, "MMRProof": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 32}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [225, 44, 34, 212, 241, 98, 217, 160, 18, 201, 49, 146, 51, 218, 93, 62, 146, 60, 197, 225, 2, 155, 143, 144, 228, 114, 73, 201, 171, 37, 107, 53], "LeafIndex": 1, "LeafCount": 60, "MMRProof": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[17, 218, 109, 31, 118, 29, 223, 155, 219, 76, 157, 110, 83, 3, 235, 212, 31, 97, 133, 141, 10, 86, 71, 161, 167, 191, 224, 137, 191, 146, 27, 233], [81, 59, 249, 11, 230, 26, 15, 169, 9, 154, 35, 81, 15, 194, 36, 54, 207, 54, 79, 131, 125, 125, 69, 95, 198, 177, 57, 3, 135, 78, 152, 185], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 33}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], "LeafIndex": 2, "LeafCount": 60, "MMRProof": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 34}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [140, 3, 159, 247, 202, 161, 124, 206, 191, 202, 220, 68, 189, 159, 206, 106, 75, 102, 153, 196, 208, 61, 226, 227, 52, 154, 161, 220, 17, 25, 60, 215], "LeafIndex": 3, "LeafCount": 60, "MMRProof": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[123, 10, 161, 115, 94, 91, 165, 141, 50, 54, 49, 108, 103, 31, 228, 240, 14, 211, 102, 238, 114, 65, 124, 158, 208, 42, 83, 168, 1, 158, 133, 184], [172, 213, 253, 201, 67, 130, 4, 251, 48, 201, 21, 69, 135, 81, 92, 45, 18, 209, 229, 218, 102, 188, 79, 118, 107, 233, 35, 25, 80, 68, 67, 193], [197, 64, 246, 204, 141, 183, 14, 63, 55, 191, 86, 77, 32, 37, 99, 211, 211, 35, 183, 97, 249, 123, 177, 191, 68, 184, 92, 72, 248, 243, 138, 22], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 35}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 4, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], "LeafIndex": 4, "LeafCount": 60, "MMRProof": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 36}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 5, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [140, 53, 210, 47, 69, 157, 119, 202, 76, 11, 11, 80, 53, 134, 151, 102, 214, 13, 24, 43, 151, 22, 171, 62, 136, 121, 224, 102, 71, 136, 153, 168], "LeafIndex": 5, "LeafCount": 60, "MMRProof": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[38, 160, 142, 77, 12, 81, 144, 240, 24, 113, 224, 86, 155, 98, 144, 184, 103, 96, 8, 93, 153, 241, 126, 180, 231, 230, 181, 143, 235, 141, 98, 73], [96, 176, 9, 203, 188, 255, 236, 84, 50, 43, 46, 176, 71, 101, 231, 32, 156, 13, 191, 95, 203, 209, 94, 178, 6, 139, 159, 208, 51, 137, 160, 92], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 37}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 6, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], "LeafIndex": 6, "LeafCount": 60, "MMRProof": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 38}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 7, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [91, 143, 41, 219, 118, 207, 78, 103, 110, 79, 201, 177, 112, 64, 49, 45, 235, 237, 175, 205, 86, 55, 251, 60, 123, 173, 210, 205, 220, 230, 164, 69], "LeafIndex": 7, "LeafCount": 60, "MMRProof": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[244, 170, 194, 251, 227, 63, 3, 85, 75, 254, 181, 89, 234, 38, 144, 237, 133, 33, 202, 164, 190, 150, 30, 97, 201, 26, 201, 161, 83, 13, 206, 122], [47, 194, 73, 130, 111, 160, 0, 3, 121, 129, 204, 52, 70, 167, 224, 173, 52, 124, 132, 70, 82, 93, 199, 149, 135, 35, 234, 58, 252, 114, 9, 222], [181, 214, 186, 229, 67, 33, 97, 230, 206, 15, 223, 210, 142, 162, 96, 17, 245, 129, 173, 104, 51, 94, 119, 207, 104, 134, 79, 73, 17, 135, 146, 87], [0, 138, 98, 142, 159, 208, 43, 254, 144, 128, 69, 183, 18, 142, 252, 254, 53, 33, 137, 31, 238, 119, 77, 74, 164, 76, 16, 231, 62, 20, 37, 123], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 39}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 8, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], "LeafIndex": 8, "LeafCount": 60, "MMRProof": [[47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 40}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 9, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [47, 1, 107, 122, 93, 185, 48, 218, 189, 234, 3, 170, 104, 210, 115, 77, 47, 164, 122, 5, 87, 226, 13, 19, 12, 193, 224, 68, 248, 220, 87, 150], "LeafIndex": 9, "LeafCount": 60, "MMRProof": [[157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[157, 255, 135, 106, 75, 148, 45, 10, 151, 17, 209, 130, 33, 137, 143, 17, 202, 57, 117, 21, 137, 235, 244, 212, 157, 116, 159, 107, 62, 73, 50, 146], [54, 40, 24, 37, 182, 66, 26, 39, 10, 16, 241, 191, 214, 17, 140, 21, 123, 81, 76, 233, 161, 132, 109, 20, 226, 230, 251, 94, 208, 179, 179, 117], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 41}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 10, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], "LeafIndex": 10, "LeafCount": 60, "MMRProof": [[216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 42}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 11, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [216, 107, 57, 121, 1, 96, 94, 239, 2, 41, 224, 89, 135, 89, 168, 152, 79, 19, 200, 214, 43, 4, 14, 25, 79, 197, 218, 151, 95, 215, 210, 110], "LeafIndex": 11, "LeafCount": 60, "MMRProof": [[44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[44, 8, 139, 243, 180, 231, 133, 60, 153, 228, 150, 54, 217, 231, 201, 163, 81, 145, 141, 112, 189, 108, 223, 97, 72, 184, 30, 104, 245, 112, 111, 104], [128, 62, 30, 249, 117, 49, 139, 98, 46, 95, 252, 139, 58, 88, 86, 32, 30, 28, 83, 130, 121, 82, 199, 32, 227, 168, 125, 63, 252, 27, 189, 242], [8, 150, 179, 32, 254, 19, 217, 213, 206, 127, 247, 113, 30, 240, 60, 156, 241, 134, 199, 230, 240, 183, 223, 234, 78, 68, 155, 36, 208, 248, 131, 254], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 43}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 12, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], "LeafIndex": 12, "LeafCount": 60, "MMRProof": [[215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 44}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 13, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [215, 48, 185, 177, 143, 0, 14, 45, 103, 176, 158, 172, 97, 30, 218, 91, 189, 239, 114, 152, 114, 56, 123, 128, 217, 137, 164, 241, 15, 131, 60, 86], "LeafIndex": 13, "LeafCount": 60, "MMRProof": [[199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[199, 133, 108, 181, 174, 18, 162, 137, 64, 131, 219, 144, 17, 25, 238, 99, 12, 60, 211, 119, 37, 113, 106, 152, 164, 70, 163, 242, 71, 110, 205, 115], [130, 39, 113, 250, 197, 184, 42, 56, 1, 163, 55, 78, 204, 208, 61, 47, 83, 246, 197, 4, 53, 212, 179, 156, 253, 130, 7, 71, 68, 161, 66, 165], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 45}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[108, 24, 154, 12, 82, 192, 3, 32, 99, 195, 102, 23, 232, 19, 98, 236, 253, 36, 231, 165, 199, 217, 195, 226, 89, 182, 54, 14, 15, 18, 49, 15], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 14, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], "LeafIndex": 14, "LeafCount": 60, "MMRProof": [[108, 24, 154, 12, 82, 192, 3, 32, 99, 195, 102, 23, 232, 19, 98, 236, 253, 36, 231, 165, 199, 217, 195, 226, 89, 182, 54, 14, 15, 18, 49, 15], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[108, 24, 154, 12, 82, 192, 3, 32, 99, 195, 102, 23, 232, 19, 98, 236, 253, 36, 231, 165, 199, 217, 195, 226, 89, 182, 54, 14, 15, 18, 49, 15], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 46}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203]], "MerkleProofOrder": 15, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [108, 24, 154, 12, 82, 192, 3, 32, 99, 195, 102, 23, 232, 19, 98, 236, 253, 36, 231, 165, 199, 217, 195, 226, 89, 182, 54, 14, 15, 18, 49, 15], "LeafIndex": 15, "LeafCount": 60, "MMRProof": [[227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[227, 104, 161, 141, 222, 126, 182, 130, 218, 250, 136, 8, 6, 171, 168, 223, 136, 138, 134, 83, 250, 11, 11, 244, 240, 249, 12, 182, 159, 73, 241, 217], [111, 165, 60, 104, 53, 144, 22, 123, 153, 106, 188, 171, 14, 157, 145, 31, 104, 80, 94, 193, 135, 227, 41, 170, 26, 193, 222, 178, 209, 123, 121, 90], [54, 180, 98, 38, 127, 240, 17, 200, 40, 105, 93, 216, 247, 203, 128, 143, 87, 38, 13, 58, 198, 77, 224, 237, 48, 85, 68, 65, 27, 70, 3, 169], [193, 54, 30, 186, 87, 86, 52, 33, 191, 116, 101, 183, 191, 43, 174, 86, 25, 222, 221, 96, 106, 174, 115, 116, 209, 203, 213, 84, 51, 92, 16, 5], [208, 14, 149, 92, 5, 210, 127, 67, 105, 213, 8, 54, 175, 112, 108, 95, 144, 74, 149, 18, 242, 253, 113, 31, 254, 87, 5, 82, 25, 217, 26, 203], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 47}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[37, 7, 134, 96, 197, 51, 25, 94, 125, 161, 130, 53, 223, 83, 161, 159, 52, 81, 189, 47, 220, 158, 69, 4, 234, 42, 6, 125, 110, 6, 222, 56], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 16, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [181, 78, 115, 105, 253, 30, 91, 154, 127, 8, 20, 227, 32, 70, 173, 237, 60, 170, 78, 196, 90, 98, 164, 233, 138, 153, 22, 11, 145, 179, 75, 81], "LeafIndex": 16, "LeafCount": 60, "MMRProof": [[37, 7, 134, 96, 197, 51, 25, 94, 125, 161, 130, 53, 223, 83, 161, 159, 52, 81, 189, 47, 220, 158, 69, 4, 234, 42, 6, 125, 110, 6, 222, 56], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[37, 7, 134, 96, 197, 51, 25, 94, 125, 161, 130, 53, 223, 83, 161, 159, 52, 81, 189, 47, 220, 158, 69, 4, 234, 42, 6, 125, 110, 6, 222, 56], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 48}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[181, 78, 115, 105, 253, 30, 91, 154, 127, 8, 20, 227, 32, 70, 173, 237, 60, 170, 78, 196, 90, 98, 164, 233, 138, 153, 22, 11, 145, 179, 75, 81], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 17, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [37, 7, 134, 96, 197, 51, 25, 94, 125, 161, 130, 53, 223, 83, 161, 159, 52, 81, 189, 47, 220, 158, 69, 4, 234, 42, 6, 125, 110, 6, 222, 56], "LeafIndex": 17, "LeafCount": 60, "MMRProof": [[181, 78, 115, 105, 253, 30, 91, 154, 127, 8, 20, 227, 32, 70, 173, 237, 60, 170, 78, 196, 90, 98, 164, 233, 138, 153, 22, 11, 145, 179, 75, 81], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[181, 78, 115, 105, 253, 30, 91, 154, 127, 8, 20, 227, 32, 70, 173, 237, 60, 170, 78, 196, 90, 98, 164, 233, 138, 153, 22, 11, 145, 179, 75, 81], [120, 155, 61, 158, 203, 31, 99, 22, 117, 137, 212, 200, 21, 125, 138, 98, 127, 96, 125, 139, 235, 199, 70, 19, 176, 87, 29, 144, 115, 120, 107, 198], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 49}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[88, 166, 165, 193, 103, 225, 150, 221, 9, 27, 95, 83, 181, 224, 236, 188, 140, 250, 30, 174, 53, 119, 230, 164, 165, 59, 105, 215, 69, 139, 127, 78], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 18, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [237, 113, 220, 205, 123, 80, 127, 144, 189, 68, 233, 225, 233, 109, 57, 72, 18, 120, 101, 123, 164, 1, 26, 202, 144, 63, 136, 62, 98, 66, 176, 197], "LeafIndex": 18, "LeafCount": 60, "MMRProof": [[88, 166, 165, 193, 103, 225, 150, 221, 9, 27, 95, 83, 181, 224, 236, 188, 140, 250, 30, 174, 53, 119, 230, 164, 165, 59, 105, 215, 69, 139, 127, 78], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[88, 166, 165, 193, 103, 225, 150, 221, 9, 27, 95, 83, 181, 224, 236, 188, 140, 250, 30, 174, 53, 119, 230, 164, 165, 59, 105, 215, 69, 139, 127, 78], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 50}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[237, 113, 220, 205, 123, 80, 127, 144, 189, 68, 233, 225, 233, 109, 57, 72, 18, 120, 101, 123, 164, 1, 26, 202, 144, 63, 136, 62, 98, 66, 176, 197], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 19, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [88, 166, 165, 193, 103, 225, 150, 221, 9, 27, 95, 83, 181, 224, 236, 188, 140, 250, 30, 174, 53, 119, 230, 164, 165, 59, 105, 215, 69, 139, 127, 78], "LeafIndex": 19, "LeafCount": 60, "MMRProof": [[237, 113, 220, 205, 123, 80, 127, 144, 189, 68, 233, 225, 233, 109, 57, 72, 18, 120, 101, 123, 164, 1, 26, 202, 144, 63, 136, 62, 98, 66, 176, 197], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[237, 113, 220, 205, 123, 80, 127, 144, 189, 68, 233, 225, 233, 109, 57, 72, 18, 120, 101, 123, 164, 1, 26, 202, 144, 63, 136, 62, 98, 66, 176, 197], [216, 39, 195, 39, 166, 155, 155, 25, 148, 74, 95, 8, 152, 174, 191, 107, 76, 128, 156, 30, 74, 198, 59, 217, 37, 178, 114, 230, 103, 53, 13, 200], [186, 39, 94, 176, 200, 114, 140, 125, 22, 35, 157, 217, 47, 54, 45, 233, 127, 162, 194, 33, 143, 73, 251, 221, 233, 186, 186, 53, 11, 139, 39, 176], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 51}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[4, 36, 87, 144, 189, 60, 80, 164, 41, 134, 102, 28, 129, 131, 43, 239, 151, 59, 249, 254, 70, 5, 5, 164, 160, 135, 106, 132, 44, 147, 121, 78], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 20, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [52, 70, 236, 68, 129, 91, 249, 181, 58, 31, 129, 168, 27, 28, 66, 200, 55, 6, 86, 127, 182, 101, 97, 179, 36, 79, 8, 117, 92, 34, 118, 197], "LeafIndex": 20, "LeafCount": 60, "MMRProof": [[4, 36, 87, 144, 189, 60, 80, 164, 41, 134, 102, 28, 129, 131, 43, 239, 151, 59, 249, 254, 70, 5, 5, 164, 160, 135, 106, 132, 44, 147, 121, 78], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[4, 36, 87, 144, 189, 60, 80, 164, 41, 134, 102, 28, 129, 131, 43, 239, 151, 59, 249, 254, 70, 5, 5, 164, 160, 135, 106, 132, 44, 147, 121, 78], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 52}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[52, 70, 236, 68, 129, 91, 249, 181, 58, 31, 129, 168, 27, 28, 66, 200, 55, 6, 86, 127, 182, 101, 97, 179, 36, 79, 8, 117, 92, 34, 118, 197], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 21, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [4, 36, 87, 144, 189, 60, 80, 164, 41, 134, 102, 28, 129, 131, 43, 239, 151, 59, 249, 254, 70, 5, 5, 164, 160, 135, 106, 132, 44, 147, 121, 78], "LeafIndex": 21, "LeafCount": 60, "MMRProof": [[52, 70, 236, 68, 129, 91, 249, 181, 58, 31, 129, 168, 27, 28, 66, 200, 55, 6, 86, 127, 182, 101, 97, 179, 36, 79, 8, 117, 92, 34, 118, 197], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[52, 70, 236, 68, 129, 91, 249, 181, 58, 31, 129, 168, 27, 28, 66, 200, 55, 6, 86, 127, 182, 101, 97, 179, 36, 79, 8, 117, 92, 34, 118, 197], [227, 88, 100, 16, 199, 180, 207, 181, 7, 19, 249, 248, 169, 136, 2, 27, 218, 245, 252, 200, 152, 230, 140, 249, 212, 95, 86, 83, 86, 145, 173, 98], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 53}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[107, 253, 128, 223, 94, 20, 200, 52, 67, 38, 128, 36, 21, 78, 105, 35, 134, 238, 185, 65, 223, 47, 147, 68, 62, 153, 249, 172, 126, 77, 76, 230], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 22, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [163, 35, 50, 241, 53, 213, 195, 102, 112, 18, 176, 208, 10, 32, 86, 88, 59, 158, 26, 40, 25, 218, 76, 92, 76, 156, 249, 162, 154, 183, 142, 130], "LeafIndex": 22, "LeafCount": 60, "MMRProof": [[107, 253, 128, 223, 94, 20, 200, 52, 67, 38, 128, 36, 21, 78, 105, 35, 134, 238, 185, 65, 223, 47, 147, 68, 62, 153, 249, 172, 126, 77, 76, 230], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[107, 253, 128, 223, 94, 20, 200, 52, 67, 38, 128, 36, 21, 78, 105, 35, 134, 238, 185, 65, 223, 47, 147, 68, 62, 153, 249, 172, 126, 77, 76, 230], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 54}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[163, 35, 50, 241, 53, 213, 195, 102, 112, 18, 176, 208, 10, 32, 86, 88, 59, 158, 26, 40, 25, 218, 76, 92, 76, 156, 249, 162, 154, 183, 142, 130], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 23, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [107, 253, 128, 223, 94, 20, 200, 52, 67, 38, 128, 36, 21, 78, 105, 35, 134, 238, 185, 65, 223, 47, 147, 68, 62, 153, 249, 172, 126, 77, 76, 230], "LeafIndex": 23, "LeafCount": 60, "MMRProof": [[163, 35, 50, 241, 53, 213, 195, 102, 112, 18, 176, 208, 10, 32, 86, 88, 59, 158, 26, 40, 25, 218, 76, 92, 76, 156, 249, 162, 154, 183, 142, 130], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[163, 35, 50, 241, 53, 213, 195, 102, 112, 18, 176, 208, 10, 32, 86, 88, 59, 158, 26, 40, 25, 218, 76, 92, 76, 156, 249, 162, 154, 183, 142, 130], [189, 77, 69, 170, 215, 115, 33, 161, 250, 209, 6, 231, 164, 25, 107, 159, 120, 31, 203, 131, 89, 148, 159, 134, 205, 241, 151, 155, 165, 191, 56, 125], [76, 226, 8, 6, 39, 235, 164, 188, 191, 168, 184, 95, 73, 171, 129, 137, 72, 60, 228, 9, 84, 208, 239, 87, 94, 254, 134, 97, 33, 107, 252, 227], [163, 52, 219, 189, 242, 222, 34, 38, 77, 194, 215, 5, 253, 137, 224, 109, 88, 70, 153, 47, 32, 38, 58, 85, 128, 126, 186, 68, 118, 19, 94, 14], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 55}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[67, 219, 194, 196, 195, 206, 141, 20, 211, 237, 72, 62, 214, 35, 64, 49, 223, 218, 105, 45, 252, 229, 188, 143, 7, 95, 48, 91, 165, 50, 247, 139], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 24, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [220, 250, 147, 6, 105, 83, 223, 179, 155, 83, 43, 44, 207, 16, 221, 8, 218, 218, 49, 48, 168, 239, 6, 131, 123, 49, 116, 4, 120, 37, 218, 204], "LeafIndex": 24, "LeafCount": 60, "MMRProof": [[67, 219, 194, 196, 195, 206, 141, 20, 211, 237, 72, 62, 214, 35, 64, 49, 223, 218, 105, 45, 252, 229, 188, 143, 7, 95, 48, 91, 165, 50, 247, 139], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[67, 219, 194, 196, 195, 206, 141, 20, 211, 237, 72, 62, 214, 35, 64, 49, 223, 218, 105, 45, 252, 229, 188, 143, 7, 95, 48, 91, 165, 50, 247, 139], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 56}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[220, 250, 147, 6, 105, 83, 223, 179, 155, 83, 43, 44, 207, 16, 221, 8, 218, 218, 49, 48, 168, 239, 6, 131, 123, 49, 116, 4, 120, 37, 218, 204], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 25, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [67, 219, 194, 196, 195, 206, 141, 20, 211, 237, 72, 62, 214, 35, 64, 49, 223, 218, 105, 45, 252, 229, 188, 143, 7, 95, 48, 91, 165, 50, 247, 139], "LeafIndex": 25, "LeafCount": 60, "MMRProof": [[220, 250, 147, 6, 105, 83, 223, 179, 155, 83, 43, 44, 207, 16, 221, 8, 218, 218, 49, 48, 168, 239, 6, 131, 123, 49, 116, 4, 120, 37, 218, 204], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[220, 250, 147, 6, 105, 83, 223, 179, 155, 83, 43, 44, 207, 16, 221, 8, 218, 218, 49, 48, 168, 239, 6, 131, 123, 49, 116, 4, 120, 37, 218, 204], [125, 89, 153, 85, 196, 183, 25, 217, 56, 93, 157, 158, 46, 117, 120, 44, 40, 210, 228, 134, 246, 18, 15, 103, 202, 30, 184, 117, 96, 158, 184, 36], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 57}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[95, 142, 6, 171, 237, 148, 238, 116, 206, 121, 137, 70, 40, 144, 84, 54, 45, 173, 249, 87, 32, 57, 205, 27, 140, 166, 253, 228, 132, 33, 205, 73], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 26, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [252, 105, 19, 31, 169, 190, 96, 191, 244, 184, 85, 54, 64, 244, 79, 242, 3, 49, 66, 138, 137, 9, 152, 160, 213, 10, 157, 151, 211, 255, 180, 116], "LeafIndex": 26, "LeafCount": 60, "MMRProof": [[95, 142, 6, 171, 237, 148, 238, 116, 206, 121, 137, 70, 40, 144, 84, 54, 45, 173, 249, 87, 32, 57, 205, 27, 140, 166, 253, 228, 132, 33, 205, 73], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[95, 142, 6, 171, 237, 148, 238, 116, 206, 121, 137, 70, 40, 144, 84, 54, 45, 173, 249, 87, 32, 57, 205, 27, 140, 166, 253, 228, 132, 33, 205, 73], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 58}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[252, 105, 19, 31, 169, 190, 96, 191, 244, 184, 85, 54, 64, 244, 79, 242, 3, 49, 66, 138, 137, 9, 152, 160, 213, 10, 157, 151, 211, 255, 180, 116], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 27, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [95, 142, 6, 171, 237, 148, 238, 116, 206, 121, 137, 70, 40, 144, 84, 54, 45, 173, 249, 87, 32, 57, 205, 27, 140, 166, 253, 228, 132, 33, 205, 73], "LeafIndex": 27, "LeafCount": 60, "MMRProof": [[252, 105, 19, 31, 169, 190, 96, 191, 244, 184, 85, 54, 64, 244, 79, 242, 3, 49, 66, 138, 137, 9, 152, 160, 213, 10, 157, 151, 211, 255, 180, 116], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[252, 105, 19, 31, 169, 190, 96, 191, 244, 184, 85, 54, 64, 244, 79, 242, 3, 49, 66, 138, 137, 9, 152, 160, 213, 10, 157, 151, 211, 255, 180, 116], [8, 227, 35, 49, 49, 21, 75, 254, 108, 128, 61, 3, 184, 33, 27, 92, 52, 85, 236, 50, 196, 236, 53, 120, 70, 182, 114, 144, 44, 18, 231, 75], [55, 0, 1, 250, 25, 136, 169, 87, 0, 90, 150, 168, 95, 53, 253, 20, 221, 225, 60, 136, 14, 73, 31, 165, 138, 136, 228, 234, 20, 26, 140, 88], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 59}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[96, 235, 182, 111, 9, 188, 127, 221, 33, 119, 42, 177, 237, 14, 251, 31, 209, 32, 142, 63, 92, 210, 13, 45, 154, 41, 162, 167, 155, 111, 149, 63], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 28, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [121, 57, 172, 103, 147, 21, 8, 243, 86, 48, 117, 173, 212, 148, 244, 49, 121, 237, 248, 46, 6, 90, 75, 68, 91, 53, 253, 34, 68, 93, 111, 222], "LeafIndex": 28, "LeafCount": 60, "MMRProof": [[96, 235, 182, 111, 9, 188, 127, 221, 33, 119, 42, 177, 237, 14, 251, 31, 209, 32, 142, 63, 92, 210, 13, 45, 154, 41, 162, 167, 155, 111, 149, 63], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[96, 235, 182, 111, 9, 188, 127, 221, 33, 119, 42, 177, 237, 14, 251, 31, 209, 32, 142, 63, 92, 210, 13, 45, 154, 41, 162, 167, 155, 111, 149, 63], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 60}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[121, 57, 172, 103, 147, 21, 8, 243, 86, 48, 117, 173, 212, 148, 244, 49, 121, 237, 248, 46, 6, 90, 75, 68, 91, 53, 253, 34, 68, 93, 111, 222], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 29, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [96, 235, 182, 111, 9, 188, 127, 221, 33, 119, 42, 177, 237, 14, 251, 31, 209, 32, 142, 63, 92, 210, 13, 45, 154, 41, 162, 167, 155, 111, 149, 63], "LeafIndex": 29, "LeafCount": 60, "MMRProof": [[121, 57, 172, 103, 147, 21, 8, 243, 86, 48, 117, 173, 212, 148, 244, 49, 121, 237, 248, 46, 6, 90, 75, 68, 91, 53, 253, 34, 68, 93, 111, 222], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[121, 57, 172, 103, 147, 21, 8, 243, 86, 48, 117, 173, 212, 148, 244, 49, 121, 237, 248, 46, 6, 90, 75, 68, 91, 53, 253, 34, 68, 93, 111, 222], [105, 100, 51, 208, 82, 85, 226, 189, 170, 223, 102, 193, 245, 250, 187, 105, 209, 205, 8, 143, 177, 253, 22, 160, 185, 199, 109, 35, 105, 33, 141, 243], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 61}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[25, 162, 90, 208, 231, 239, 247, 89, 241, 174, 245, 66, 145, 50, 7, 221, 196, 128, 127, 195, 46, 219, 139, 153, 38, 191, 150, 79, 63, 139, 209, 252], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 30, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [165, 250, 210, 238, 88, 36, 200, 91, 167, 234, 130, 209, 71, 134, 210, 232, 152, 223, 185, 19, 19, 164, 108, 235, 71, 52, 77, 49, 77, 148, 170, 39], "LeafIndex": 30, "LeafCount": 60, "MMRProof": [[25, 162, 90, 208, 231, 239, 247, 89, 241, 174, 245, 66, 145, 50, 7, 221, 196, 128, 127, 195, 46, 219, 139, 153, 38, 191, 150, 79, 63, 139, 209, 252], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[25, 162, 90, 208, 231, 239, 247, 89, 241, 174, 245, 66, 145, 50, 7, 221, 196, 128, 127, 195, 46, 219, 139, 153, 38, 191, 150, 79, 63, 139, 209, 252], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 62}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[165, 250, 210, 238, 88, 36, 200, 91, 167, 234, 130, 209, 71, 134, 210, 232, 152, 223, 185, 19, 19, 164, 108, 235, 71, 52, 77, 49, 77, 148, 170, 39], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7]], "MerkleProofOrder": 31, "MMRRightBaggedPeak": [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161], "MMRRestOfThePeaks": null, "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [25, 162, 90, 208, 231, 239, 247, 89, 241, 174, 245, 66, 145, 50, 7, 221, 196, 128, 127, 195, 46, 219, 139, 153, 38, 191, 150, 79, 63, 139, 209, 252], "LeafIndex": 31, "LeafCount": 60, "MMRProof": [[165, 250, 210, 238, 88, 36, 200, 91, 167, 234, 130, 209, 71, 134, 210, 232, 152, 223, 185, 19, 19, 164, 108, 235, 71, 52, 77, 49, 77, 148, 170, 39], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofItems": [[165, 250, 210, 238, 88, 36, 200, 91, 167, 234, 130, 209, 71, 134, 210, 232, 152, 223, 185, 19, 19, 164, 108, 235, 71, 52, 77, 49, 77, 148, 170, 39], [97, 122, 80, 178, 6, 228, 101, 77, 130, 132, 251, 69, 246, 32, 228, 81, 171, 8, 115, 108, 34, 171, 53, 55, 193, 48, 177, 113, 133, 199, 152, 133], [74, 118, 26, 4, 172, 67, 166, 100, 173, 205, 236, 121, 157, 25, 81, 104, 236, 253, 176, 207, 184, 22, 148, 197, 152, 67, 76, 212, 227, 131, 8, 149], [118, 158, 111, 158, 12, 200, 206, 72, 141, 123, 47, 166, 235, 223, 49, 173, 60, 137, 25, 221, 64, 0, 72, 234, 162, 201, 53, 38, 130, 204, 247, 36], [61, 238, 219, 188, 22, 17, 201, 34, 143, 68, 227, 96, 255, 227, 184, 14, 105, 165, 248, 185, 107, 196, 241, 153, 149, 141, 58, 14, 26, 232, 2, 7], [1, 178, 228, 163, 175, 97, 171, 75, 93, 234, 205, 21, 208, 93, 217, 52, 149, 118, 115, 116, 63, 53, 137, 123, 106, 96, 71, 150, 229, 60, 20, 161]], "SimplifiedMerkleProofOrder": 63}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[143, 161, 180, 179, 9, 77, 222, 141, 142, 208, 34, 160, 180, 229, 147, 29, 117, 111, 180, 5, 92, 173, 39, 104, 224, 119, 245, 237, 113, 141, 224, 131], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [84, 117, 120, 164, 92, 197, 212, 166, 230, 173, 11, 89, 144, 90, 133, 224, 133, 39, 194, 177, 66, 6, 4, 225, 87, 119, 46, 123, 240, 44, 38, 114], "LeafIndex": 32, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [143, 161, 180, 179, 9, 77, 222, 141, 142, 208, 34, 160, 180, 229, 147, 29, 117, 111, 180, 5, 92, 173, 39, 104, 224, 119, 245, 237, 113, 141, 224, 131], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[143, 161, 180, 179, 9, 77, 222, 141, 142, 208, 34, 160, 180, 229, 147, 29, 117, 111, 180, 5, 92, 173, 39, 104, 224, 119, 245, 237, 113, 141, 224, 131], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 16}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[84, 117, 120, 164, 92, 197, 212, 166, 230, 173, 11, 89, 144, 90, 133, 224, 133, 39, 194, 177, 66, 6, 4, 225, 87, 119, 46, 123, 240, 44, 38, 114], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [143, 161, 180, 179, 9, 77, 222, 141, 142, 208, 34, 160, 180, 229, 147, 29, 117, 111, 180, 5, 92, 173, 39, 104, 224, 119, 245, 237, 113, 141, 224, 131], "LeafIndex": 33, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [84, 117, 120, 164, 92, 197, 212, 166, 230, 173, 11, 89, 144, 90, 133, 224, 133, 39, 194, 177, 66, 6, 4, 225, 87, 119, 46, 123, 240, 44, 38, 114], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[84, 117, 120, 164, 92, 197, 212, 166, 230, 173, 11, 89, 144, 90, 133, 224, 133, 39, 194, 177, 66, 6, 4, 225, 87, 119, 46, 123, 240, 44, 38, 114], [127, 223, 164, 227, 154, 190, 141, 195, 99, 133, 141, 7, 217, 113, 4, 128, 3, 186, 228, 128, 190, 151, 104, 218, 202, 61, 0, 221, 13, 49, 153, 80], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 17}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[1, 186, 182, 111, 240, 26, 36, 56, 225, 200, 196, 129, 90, 122, 89, 20, 18, 9, 127, 160, 33, 178, 227, 219, 45, 32, 71, 225, 82, 111, 48, 56], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [154, 237, 196, 41, 10, 193, 244, 44, 178, 106, 78, 251, 223, 26, 32, 94, 71, 214, 126, 70, 208, 29, 50, 182, 254, 98, 20, 79, 9, 179, 0, 146], "LeafIndex": 34, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [1, 186, 182, 111, 240, 26, 36, 56, 225, 200, 196, 129, 90, 122, 89, 20, 18, 9, 127, 160, 33, 178, 227, 219, 45, 32, 71, 225, 82, 111, 48, 56], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[1, 186, 182, 111, 240, 26, 36, 56, 225, 200, 196, 129, 90, 122, 89, 20, 18, 9, 127, 160, 33, 178, 227, 219, 45, 32, 71, 225, 82, 111, 48, 56], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 18}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[154, 237, 196, 41, 10, 193, 244, 44, 178, 106, 78, 251, 223, 26, 32, 94, 71, 214, 126, 70, 208, 29, 50, 182, 254, 98, 20, 79, 9, 179, 0, 146], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [1, 186, 182, 111, 240, 26, 36, 56, 225, 200, 196, 129, 90, 122, 89, 20, 18, 9, 127, 160, 33, 178, 227, 219, 45, 32, 71, 225, 82, 111, 48, 56], "LeafIndex": 35, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [154, 237, 196, 41, 10, 193, 244, 44, 178, 106, 78, 251, 223, 26, 32, 94, 71, 214, 126, 70, 208, 29, 50, 182, 254, 98, 20, 79, 9, 179, 0, 146], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[154, 237, 196, 41, 10, 193, 244, 44, 178, 106, 78, 251, 223, 26, 32, 94, 71, 214, 126, 70, 208, 29, 50, 182, 254, 98, 20, 79, 9, 179, 0, 146], [28, 207, 94, 250, 25, 54, 185, 106, 11, 136, 152, 34, 47, 112, 77, 36, 134, 171, 14, 95, 51, 165, 153, 89, 190, 202, 250, 110, 193, 58, 143, 151], [62, 250, 139, 93, 242, 238, 166, 225, 177, 211, 144, 217, 65, 111, 2, 217, 88, 197, 200, 122, 198, 190, 249, 205, 202, 198, 156, 251, 157, 14, 212, 140], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 19}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[209, 116, 44, 37, 113, 133, 50, 123, 0, 181, 139, 237, 88, 11, 186, 180, 90, 223, 119, 77, 133, 179, 59, 176, 142, 152, 23, 244, 203, 115, 19, 164], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 4, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [149, 190, 66, 226, 221, 154, 141, 119, 172, 149, 82, 193, 219, 72, 154, 171, 228, 11, 100, 194, 216, 191, 31, 157, 222, 197, 74, 6, 29, 190, 58, 122], "LeafIndex": 36, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [209, 116, 44, 37, 113, 133, 50, 123, 0, 181, 139, 237, 88, 11, 186, 180, 90, 223, 119, 77, 133, 179, 59, 176, 142, 152, 23, 244, 203, 115, 19, 164], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[209, 116, 44, 37, 113, 133, 50, 123, 0, 181, 139, 237, 88, 11, 186, 180, 90, 223, 119, 77, 133, 179, 59, 176, 142, 152, 23, 244, 203, 115, 19, 164], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 20}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[149, 190, 66, 226, 221, 154, 141, 119, 172, 149, 82, 193, 219, 72, 154, 171, 228, 11, 100, 194, 216, 191, 31, 157, 222, 197, 74, 6, 29, 190, 58, 122], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 5, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [209, 116, 44, 37, 113, 133, 50, 123, 0, 181, 139, 237, 88, 11, 186, 180, 90, 223, 119, 77, 133, 179, 59, 176, 142, 152, 23, 244, 203, 115, 19, 164], "LeafIndex": 37, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [149, 190, 66, 226, 221, 154, 141, 119, 172, 149, 82, 193, 219, 72, 154, 171, 228, 11, 100, 194, 216, 191, 31, 157, 222, 197, 74, 6, 29, 190, 58, 122], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[149, 190, 66, 226, 221, 154, 141, 119, 172, 149, 82, 193, 219, 72, 154, 171, 228, 11, 100, 194, 216, 191, 31, 157, 222, 197, 74, 6, 29, 190, 58, 122], [16, 175, 138, 35, 75, 234, 113, 125, 185, 115, 124, 132, 165, 209, 119, 193, 90, 122, 4, 102, 236, 205, 45, 51, 196, 226, 249, 35, 81, 45, 3, 178], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 21}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[81, 118, 194, 206, 80, 93, 4, 201, 235, 51, 24, 197, 36, 124, 185, 185, 151, 177, 189, 23, 244, 35, 206, 246, 37, 91, 208, 24, 226, 211, 218, 239], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 6, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [194, 51, 249, 127, 61, 103, 166, 163, 59, 201, 111, 93, 169, 158, 196, 188, 116, 122, 51, 24, 34, 20, 78, 240, 76, 159, 224, 36, 3, 138, 38, 96], "LeafIndex": 38, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [81, 118, 194, 206, 80, 93, 4, 201, 235, 51, 24, 197, 36, 124, 185, 185, 151, 177, 189, 23, 244, 35, 206, 246, 37, 91, 208, 24, 226, 211, 218, 239], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[81, 118, 194, 206, 80, 93, 4, 201, 235, 51, 24, 197, 36, 124, 185, 185, 151, 177, 189, 23, 244, 35, 206, 246, 37, 91, 208, 24, 226, 211, 218, 239], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 22}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[194, 51, 249, 127, 61, 103, 166, 163, 59, 201, 111, 93, 169, 158, 196, 188, 116, 122, 51, 24, 34, 20, 78, 240, 76, 159, 224, 36, 3, 138, 38, 96], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134]], "MerkleProofOrder": 7, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [81, 118, 194, 206, 80, 93, 4, 201, 235, 51, 24, 197, 36, 124, 185, 185, 151, 177, 189, 23, 244, 35, 206, 246, 37, 91, 208, 24, 226, 211, 218, 239], "LeafIndex": 39, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [194, 51, 249, 127, 61, 103, 166, 163, 59, 201, 111, 93, 169, 158, 196, 188, 116, 122, 51, 24, 34, 20, 78, 240, 76, 159, 224, 36, 3, 138, 38, 96], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[194, 51, 249, 127, 61, 103, 166, 163, 59, 201, 111, 93, 169, 158, 196, 188, 116, 122, 51, 24, 34, 20, 78, 240, 76, 159, 224, 36, 3, 138, 38, 96], [15, 191, 80, 123, 75, 36, 99, 156, 113, 151, 225, 113, 34, 50, 254, 38, 154, 132, 180, 119, 17, 216, 124, 200, 131, 207, 180, 169, 127, 103, 37, 175], [8, 185, 246, 162, 251, 165, 192, 46, 208, 58, 65, 246, 149, 141, 56, 246, 226, 38, 212, 137, 91, 231, 167, 165, 178, 199, 112, 233, 132, 114, 102, 177], [52, 140, 173, 179, 220, 158, 59, 60, 73, 138, 48, 126, 221, 181, 236, 221, 211, 122, 171, 109, 239, 156, 181, 162, 52, 28, 80, 122, 236, 245, 255, 134], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 23}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[136, 246, 201, 29, 13, 12, 200, 96, 193, 125, 229, 156, 59, 38, 11, 255, 80, 145, 179, 102, 81, 63, 108, 149, 46, 38, 96, 71, 138, 79, 12, 233], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 8, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [31, 138, 101, 14, 36, 233, 94, 122, 194, 6, 143, 133, 178, 209, 21, 190, 79, 183, 184, 41, 74, 234, 244, 246, 46, 244, 13, 231, 225, 172, 10, 192], "LeafIndex": 40, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [136, 246, 201, 29, 13, 12, 200, 96, 193, 125, 229, 156, 59, 38, 11, 255, 80, 145, 179, 102, 81, 63, 108, 149, 46, 38, 96, 71, 138, 79, 12, 233], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[136, 246, 201, 29, 13, 12, 200, 96, 193, 125, 229, 156, 59, 38, 11, 255, 80, 145, 179, 102, 81, 63, 108, 149, 46, 38, 96, 71, 138, 79, 12, 233], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 24}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[31, 138, 101, 14, 36, 233, 94, 122, 194, 6, 143, 133, 178, 209, 21, 190, 79, 183, 184, 41, 74, 234, 244, 246, 46, 244, 13, 231, 225, 172, 10, 192], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 9, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [136, 246, 201, 29, 13, 12, 200, 96, 193, 125, 229, 156, 59, 38, 11, 255, 80, 145, 179, 102, 81, 63, 108, 149, 46, 38, 96, 71, 138, 79, 12, 233], "LeafIndex": 41, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [31, 138, 101, 14, 36, 233, 94, 122, 194, 6, 143, 133, 178, 209, 21, 190, 79, 183, 184, 41, 74, 234, 244, 246, 46, 244, 13, 231, 225, 172, 10, 192], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[31, 138, 101, 14, 36, 233, 94, 122, 194, 6, 143, 133, 178, 209, 21, 190, 79, 183, 184, 41, 74, 234, 244, 246, 46, 244, 13, 231, 225, 172, 10, 192], [119, 114, 95, 145, 240, 102, 141, 155, 94, 88, 210, 126, 12, 171, 179, 183, 38, 122, 113, 225, 206, 129, 32, 149, 97, 16, 48, 114, 161, 184, 55, 252], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 25}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[230, 251, 249, 106, 25, 99, 1, 66, 138, 249, 30, 171, 71, 110, 112, 36, 2, 24, 215, 194, 247, 33, 222, 132, 19, 61, 196, 244, 204, 11, 85, 36], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 10, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [222, 3, 124, 116, 102, 39, 10, 99, 160, 246, 204, 154, 59, 90, 209, 163, 6, 122, 100, 160, 183, 230, 38, 244, 234, 158, 149, 205, 101, 51, 237, 137], "LeafIndex": 42, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [230, 251, 249, 106, 25, 99, 1, 66, 138, 249, 30, 171, 71, 110, 112, 36, 2, 24, 215, 194, 247, 33, 222, 132, 19, 61, 196, 244, 204, 11, 85, 36], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[230, 251, 249, 106, 25, 99, 1, 66, 138, 249, 30, 171, 71, 110, 112, 36, 2, 24, 215, 194, 247, 33, 222, 132, 19, 61, 196, 244, 204, 11, 85, 36], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 26}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[222, 3, 124, 116, 102, 39, 10, 99, 160, 246, 204, 154, 59, 90, 209, 163, 6, 122, 100, 160, 183, 230, 38, 244, 234, 158, 149, 205, 101, 51, 237, 137], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 11, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [230, 251, 249, 106, 25, 99, 1, 66, 138, 249, 30, 171, 71, 110, 112, 36, 2, 24, 215, 194, 247, 33, 222, 132, 19, 61, 196, 244, 204, 11, 85, 36], "LeafIndex": 43, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [222, 3, 124, 116, 102, 39, 10, 99, 160, 246, 204, 154, 59, 90, 209, 163, 6, 122, 100, 160, 183, 230, 38, 244, 234, 158, 149, 205, 101, 51, 237, 137], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[222, 3, 124, 116, 102, 39, 10, 99, 160, 246, 204, 154, 59, 90, 209, 163, 6, 122, 100, 160, 183, 230, 38, 244, 234, 158, 149, 205, 101, 51, 237, 137], [139, 186, 33, 146, 4, 38, 27, 109, 92, 13, 89, 25, 220, 243, 32, 232, 190, 235, 30, 28, 15, 202, 17, 239, 224, 234, 220, 74, 143, 220, 125, 199], [250, 102, 120, 103, 183, 38, 198, 166, 245, 189, 153, 195, 1, 246, 82, 171, 22, 255, 162, 126, 160, 68, 82, 185, 88, 35, 210, 200, 74, 102, 230, 140], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 27}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[66, 90, 195, 236, 56, 187, 22, 174, 22, 138, 95, 194, 90, 135, 2, 188, 101, 166, 217, 205, 9, 246, 137, 110, 29, 194, 132, 153, 226, 177, 141, 168], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 12, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [182, 53, 254, 144, 167, 64, 234, 248, 119, 149, 115, 93, 176, 32, 127, 20, 176, 208, 158, 46, 154, 225, 210, 149, 206, 104, 59, 187, 49, 160, 117, 182], "LeafIndex": 44, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [66, 90, 195, 236, 56, 187, 22, 174, 22, 138, 95, 194, 90, 135, 2, 188, 101, 166, 217, 205, 9, 246, 137, 110, 29, 194, 132, 153, 226, 177, 141, 168], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[66, 90, 195, 236, 56, 187, 22, 174, 22, 138, 95, 194, 90, 135, 2, 188, 101, 166, 217, 205, 9, 246, 137, 110, 29, 194, 132, 153, 226, 177, 141, 168], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 28}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[182, 53, 254, 144, 167, 64, 234, 248, 119, 149, 115, 93, 176, 32, 127, 20, 176, 208, 158, 46, 154, 225, 210, 149, 206, 104, 59, 187, 49, 160, 117, 182], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 13, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [66, 90, 195, 236, 56, 187, 22, 174, 22, 138, 95, 194, 90, 135, 2, 188, 101, 166, 217, 205, 9, 246, 137, 110, 29, 194, 132, 153, 226, 177, 141, 168], "LeafIndex": 45, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [182, 53, 254, 144, 167, 64, 234, 248, 119, 149, 115, 93, 176, 32, 127, 20, 176, 208, 158, 46, 154, 225, 210, 149, 206, 104, 59, 187, 49, 160, 117, 182], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[182, 53, 254, 144, 167, 64, 234, 248, 119, 149, 115, 93, 176, 32, 127, 20, 176, 208, 158, 46, 154, 225, 210, 149, 206, 104, 59, 187, 49, 160, 117, 182], [25, 106, 192, 99, 248, 76, 118, 190, 5, 207, 12, 131, 116, 114, 203, 62, 182, 28, 221, 91, 83, 59, 254, 233, 228, 211, 100, 165, 165, 115, 72, 15], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 29}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[3, 104, 31, 142, 226, 185, 189, 40, 223, 122, 247, 68, 90, 88, 144, 82, 150, 91, 96, 196, 216, 157, 19, 38, 155, 72, 58, 175, 208, 234, 157, 182], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 14, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [137, 34, 5, 152, 224, 161, 39, 248, 135, 207, 22, 179, 124, 98, 242, 109, 156, 172, 232, 90, 68, 130, 67, 82, 47, 158, 65, 180, 8, 173, 226, 100], "LeafIndex": 46, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [3, 104, 31, 142, 226, 185, 189, 40, 223, 122, 247, 68, 90, 88, 144, 82, 150, 91, 96, 196, 216, 157, 19, 38, 155, 72, 58, 175, 208, 234, 157, 182], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[3, 104, 31, 142, 226, 185, 189, 40, 223, 122, 247, 68, 90, 88, 144, 82, 150, 91, 96, 196, 216, 157, 19, 38, 155, 72, 58, 175, 208, 234, 157, 182], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 30}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[137, 34, 5, 152, 224, 161, 39, 248, 135, 207, 22, 179, 124, 98, 242, 109, 156, 172, 232, 90, 68, 130, 67, 82, 47, 158, 65, 180, 8, 173, 226, 100], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87]], "MerkleProofOrder": 15, "MMRRightBaggedPeak": [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [3, 104, 31, 142, 226, 185, 189, 40, 223, 122, 247, 68, 90, 88, 144, 82, 150, 91, 96, 196, 216, 157, 19, 38, 155, 72, 58, 175, 208, 234, 157, 182], "LeafIndex": 47, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [137, 34, 5, 152, 224, 161, 39, 248, 135, 207, 22, 179, 124, 98, 242, 109, 156, 172, 232, 90, 68, 130, 67, 82, 47, 158, 65, 180, 8, 173, 226, 100], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106]], "SimplifiedMerkleProofItems": [[137, 34, 5, 152, 224, 161, 39, 248, 135, 207, 22, 179, 124, 98, 242, 109, 156, 172, 232, 90, 68, 130, 67, 82, 47, 158, 65, 180, 8, 173, 226, 100], [175, 74, 136, 155, 243, 27, 90, 242, 53, 178, 245, 49, 159, 34, 100, 50, 226, 106, 4, 32, 114, 202, 21, 180, 117, 211, 254, 44, 60, 254, 146, 3], [243, 246, 219, 199, 218, 136, 17, 166, 96, 228, 76, 111, 237, 225, 19, 105, 191, 178, 66, 60, 123, 56, 106, 234, 142, 166, 161, 36, 240, 255, 252, 194], [161, 50, 199, 242, 232, 109, 90, 148, 75, 37, 86, 105, 169, 146, 68, 57, 32, 142, 206, 213, 247, 24, 35, 110, 61, 84, 171, 98, 149, 87, 89, 87], [146, 88, 171, 103, 206, 167, 207, 96, 83, 1, 34, 162, 74, 35, 106, 23, 182, 102, 226, 107, 92, 185, 234, 184, 95, 91, 90, 43, 1, 60, 123, 106], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 31}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[182, 9, 246, 81, 90, 61, 205, 243, 43, 137, 102, 120, 168, 155, 29, 134, 127, 27, 217, 59, 116, 133, 248, 45, 30, 144, 28, 81, 232, 117, 178, 194], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [126, 144, 89, 32, 55, 30, 197, 249, 145, 118, 241, 60, 93, 80, 16, 117, 132, 147, 200, 48, 254, 82, 14, 151, 204, 79, 157, 102, 216, 11, 62, 254], "LeafIndex": 48, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [182, 9, 246, 81, 90, 61, 205, 243, 43, 137, 102, 120, 168, 155, 29, 134, 127, 27, 217, 59, 116, 133, 248, 45, 30, 144, 28, 81, 232, 117, 178, 194], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[182, 9, 246, 81, 90, 61, 205, 243, 43, 137, 102, 120, 168, 155, 29, 134, 127, 27, 217, 59, 116, 133, 248, 45, 30, 144, 28, 81, 232, 117, 178, 194], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 8}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[126, 144, 89, 32, 55, 30, 197, 249, 145, 118, 241, 60, 93, 80, 16, 117, 132, 147, 200, 48, 254, 82, 14, 151, 204, 79, 157, 102, 216, 11, 62, 254], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [182, 9, 246, 81, 90, 61, 205, 243, 43, 137, 102, 120, 168, 155, 29, 134, 127, 27, 217, 59, 116, 133, 248, 45, 30, 144, 28, 81, 232, 117, 178, 194], "LeafIndex": 49, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [126, 144, 89, 32, 55, 30, 197, 249, 145, 118, 241, 60, 93, 80, 16, 117, 132, 147, 200, 48, 254, 82, 14, 151, 204, 79, 157, 102, 216, 11, 62, 254], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[126, 144, 89, 32, 55, 30, 197, 249, 145, 118, 241, 60, 93, 80, 16, 117, 132, 147, 200, 48, 254, 82, 14, 151, 204, 79, 157, 102, 216, 11, 62, 254], [235, 205, 126, 90, 16, 129, 46, 234, 188, 146, 49, 104, 70, 64, 121, 3, 157, 165, 199, 224, 115, 103, 10, 216, 168, 182, 241, 112, 169, 135, 248, 176], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 9}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[168, 84, 165, 177, 105, 211, 129, 247, 33, 9, 4, 139, 165, 107, 65, 221, 84, 123, 162, 172, 107, 11, 131, 141, 96, 214, 117, 253, 205, 245, 49, 7], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [64, 102, 12, 181, 9, 18, 216, 106, 78, 197, 39, 205, 90, 249, 115, 35, 61, 127, 155, 222, 231, 54, 27, 247, 250, 223, 223, 18, 67, 191, 228, 56], "LeafIndex": 50, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [168, 84, 165, 177, 105, 211, 129, 247, 33, 9, 4, 139, 165, 107, 65, 221, 84, 123, 162, 172, 107, 11, 131, 141, 96, 214, 117, 253, 205, 245, 49, 7], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[168, 84, 165, 177, 105, 211, 129, 247, 33, 9, 4, 139, 165, 107, 65, 221, 84, 123, 162, 172, 107, 11, 131, 141, 96, 214, 117, 253, 205, 245, 49, 7], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 10}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[64, 102, 12, 181, 9, 18, 216, 106, 78, 197, 39, 205, 90, 249, 115, 35, 61, 127, 155, 222, 231, 54, 27, 247, 250, 223, 223, 18, 67, 191, 228, 56], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [168, 84, 165, 177, 105, 211, 129, 247, 33, 9, 4, 139, 165, 107, 65, 221, 84, 123, 162, 172, 107, 11, 131, 141, 96, 214, 117, 253, 205, 245, 49, 7], "LeafIndex": 51, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [64, 102, 12, 181, 9, 18, 216, 106, 78, 197, 39, 205, 90, 249, 115, 35, 61, 127, 155, 222, 231, 54, 27, 247, 250, 223, 223, 18, 67, 191, 228, 56], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[64, 102, 12, 181, 9, 18, 216, 106, 78, 197, 39, 205, 90, 249, 115, 35, 61, 127, 155, 222, 231, 54, 27, 247, 250, 223, 223, 18, 67, 191, 228, 56], [176, 144, 62, 28, 97, 160, 150, 5, 120, 61, 222, 85, 122, 26, 158, 231, 153, 62, 249, 215, 141, 79, 47, 185, 176, 205, 100, 27, 79, 203, 0, 3], [48, 94, 156, 174, 174, 209, 49, 37, 188, 225, 35, 49, 170, 153, 156, 251, 220, 173, 181, 52, 81, 126, 89, 197, 186, 103, 83, 50, 183, 14, 100, 136], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 11}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[55, 6, 109, 77, 77, 36, 95, 197, 188, 1, 254, 140, 85, 0, 92, 155, 146, 215, 169, 248, 222, 180, 67, 169, 54, 157, 68, 35, 172, 217, 87, 0], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148]], "MerkleProofOrder": 4, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [232, 153, 66, 89, 18, 113, 125, 224, 121, 168, 155, 149, 130, 116, 148, 140, 112, 86, 48, 138, 233, 32, 200, 153, 102, 110, 12, 243, 207, 192, 255, 161], "LeafIndex": 52, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [55, 6, 109, 77, 77, 36, 95, 197, 188, 1, 254, 140, 85, 0, 92, 155, 146, 215, 169, 248, 222, 180, 67, 169, 54, 157, 68, 35, 172, 217, 87, 0], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[55, 6, 109, 77, 77, 36, 95, 197, 188, 1, 254, 140, 85, 0, 92, 155, 146, 215, 169, 248, 222, 180, 67, 169, 54, 157, 68, 35, 172, 217, 87, 0], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 12}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[232, 153, 66, 89, 18, 113, 125, 224, 121, 168, 155, 149, 130, 116, 148, 140, 112, 86, 48, 138, 233, 32, 200, 153, 102, 110, 12, 243, 207, 192, 255, 161], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148]], "MerkleProofOrder": 5, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [55, 6, 109, 77, 77, 36, 95, 197, 188, 1, 254, 140, 85, 0, 92, 155, 146, 215, 169, 248, 222, 180, 67, 169, 54, 157, 68, 35, 172, 217, 87, 0], "LeafIndex": 53, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [232, 153, 66, 89, 18, 113, 125, 224, 121, 168, 155, 149, 130, 116, 148, 140, 112, 86, 48, 138, 233, 32, 200, 153, 102, 110, 12, 243, 207, 192, 255, 161], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[232, 153, 66, 89, 18, 113, 125, 224, 121, 168, 155, 149, 130, 116, 148, 140, 112, 86, 48, 138, 233, 32, 200, 153, 102, 110, 12, 243, 207, 192, 255, 161], [115, 93, 26, 222, 6, 195, 234, 178, 134, 187, 174, 246, 28, 44, 98, 91, 26, 125, 87, 85, 131, 13, 229, 217, 120, 46, 190, 235, 202, 181, 39, 118], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 13}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[46, 28, 106, 187, 15, 110, 213, 180, 252, 197, 59, 110, 188, 202, 245, 241, 85, 224, 250, 125, 118, 58, 18, 163, 26, 230, 138, 201, 64, 141, 2, 253], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148]], "MerkleProofOrder": 6, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [58, 217, 237, 43, 167, 41, 166, 65, 153, 7, 229, 34, 197, 94, 36, 111, 167, 14, 235, 170, 227, 160, 26, 55, 148, 178, 223, 66, 252, 54, 239, 181], "LeafIndex": 54, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [46, 28, 106, 187, 15, 110, 213, 180, 252, 197, 59, 110, 188, 202, 245, 241, 85, 224, 250, 125, 118, 58, 18, 163, 26, 230, 138, 201, 64, 141, 2, 253], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[46, 28, 106, 187, 15, 110, 213, 180, 252, 197, 59, 110, 188, 202, 245, 241, 85, 224, 250, 125, 118, 58, 18, 163, 26, 230, 138, 201, 64, 141, 2, 253], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 14}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[58, 217, 237, 43, 167, 41, 166, 65, 153, 7, 229, 34, 197, 94, 36, 111, 167, 14, 235, 170, 227, 160, 26, 55, 148, 178, 223, 66, 252, 54, 239, 181], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148]], "MerkleProofOrder": 7, "MMRRightBaggedPeak": [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [46, 28, 106, 187, 15, 110, 213, 180, 252, 197, 59, 110, 188, 202, 245, 241, 85, 224, 250, 125, 118, 58, 18, 163, 26, 230, 138, 201, 64, 141, 2, 253], "LeafIndex": 55, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [58, 217, 237, 43, 167, 41, 166, 65, 153, 7, 229, 34, 197, 94, 36, 111, 167, 14, 235, 170, 227, 160, 26, 55, 148, 178, 223, 66, 252, 54, 239, 181], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198]], "SimplifiedMerkleProofItems": [[58, 217, 237, 43, 167, 41, 166, 65, 153, 7, 229, 34, 197, 94, 36, 111, 167, 14, 235, 170, 227, 160, 26, 55, 148, 178, 223, 66, 252, 54, 239, 181], [193, 7, 222, 157, 54, 209, 179, 214, 151, 44, 130, 11, 100, 227, 18, 87, 158, 32, 125, 98, 37, 158, 204, 181, 14, 30, 140, 225, 134, 98, 105, 6], [170, 122, 24, 46, 18, 50, 223, 155, 70, 119, 52, 21, 74, 95, 238, 5, 59, 89, 193, 111, 104, 123, 3, 144, 137, 230, 16, 194, 236, 20, 247, 148], [173, 11, 245, 106, 122, 186, 2, 11, 189, 200, 125, 137, 216, 47, 146, 86, 68, 27, 225, 244, 89, 200, 155, 145, 252, 23, 5, 253, 90, 80, 7, 198], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 15}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[141, 187, 62, 176, 44, 143, 140, 246, 207, 3, 52, 106, 114, 215, 51, 212, 202, 155, 126, 126, 93, 172, 193, 175, 237, 223, 181, 105, 115, 118, 195, 8], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55]], "MerkleProofOrder": 0, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [83, 203, 8, 40, 227, 215, 105, 166, 201, 151, 49, 156, 119, 94, 56, 124, 65, 40, 1, 87, 214, 95, 218, 44, 155, 153, 122, 248, 77, 127, 144, 219], "LeafIndex": 56, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [141, 187, 62, 176, 44, 143, 140, 246, 207, 3, 52, 106, 114, 215, 51, 212, 202, 155, 126, 126, 93, 172, 193, 175, 237, 223, 181, 105, 115, 118, 195, 8], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55]], "SimplifiedMerkleProofItems": [[141, 187, 62, 176, 44, 143, 140, 246, 207, 3, 52, 106, 114, 215, 51, 212, 202, 155, 126, 126, 93, 172, 193, 175, 237, 223, 181, 105, 115, 118, 195, 8], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 0}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[83, 203, 8, 40, 227, 215, 105, 166, 201, 151, 49, 156, 119, 94, 56, 124, 65, 40, 1, 87, 214, 95, 218, 44, 155, 153, 122, 248, 77, 127, 144, 219], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55]], "MerkleProofOrder": 1, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [141, 187, 62, 176, 44, 143, 140, 246, 207, 3, 52, 106, 114, 215, 51, 212, 202, 155, 126, 126, 93, 172, 193, 175, 237, 223, 181, 105, 115, 118, 195, 8], "LeafIndex": 57, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [83, 203, 8, 40, 227, 215, 105, 166, 201, 151, 49, 156, 119, 94, 56, 124, 65, 40, 1, 87, 214, 95, 218, 44, 155, 153, 122, 248, 77, 127, 144, 219], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55]], "SimplifiedMerkleProofItems": [[83, 203, 8, 40, 227, 215, 105, 166, 201, 151, 49, 156, 119, 94, 56, 124, 65, 40, 1, 87, 214, 95, 218, 44, 155, 153, 122, 248, 77, 127, 144, 219], [98, 168, 246, 0, 11, 78, 215, 255, 106, 97, 107, 238, 102, 55, 48, 141, 186, 154, 250, 73, 118, 189, 117, 51, 63, 50, 193, 130, 112, 142, 199, 55], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 1}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[54, 185, 6, 37, 103, 201, 119, 162, 184, 151, 248, 203, 213, 71, 206, 120, 250, 59, 213, 152, 239, 28, 107, 84, 153, 231, 18, 41, 81, 79, 227, 237], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185]], "MerkleProofOrder": 2, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [127, 22, 26, 155, 215, 199, 129, 236, 124, 37, 224, 148, 160, 18, 189, 25, 49, 222, 193, 143, 170, 46, 170, 95, 213, 107, 32, 238, 25, 177, 68, 113], "LeafIndex": 58, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [54, 185, 6, 37, 103, 201, 119, 162, 184, 151, 248, 203, 213, 71, 206, 120, 250, 59, 213, 152, 239, 28, 107, 84, 153, 231, 18, 41, 81, 79, 227, 237], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185]], "SimplifiedMerkleProofItems": [[54, 185, 6, 37, 103, 201, 119, 162, 184, 151, 248, 203, 213, 71, 206, 120, 250, 59, 213, 152, 239, 28, 107, 84, 153, 231, 18, 41, 81, 79, 227, 237], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 2}, {"ReferenceSimplifiedProof": {"MerkleProofItems": [[127, 22, 26, 155, 215, 199, 129, 236, 124, 37, 224, 148, 160, 18, 189, 25, 49, 222, 193, 143, 170, 46, 170, 95, 213, 107, 32, 238, 25, 177, 68, 113], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185]], "MerkleProofOrder": 3, "MMRRightBaggedPeak": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "MMRRestOfThePeaks": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73]], "Blockhash": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Leaf": {"Version": 0, "ParentNumberAndHash": {"ParentNumber": 0, "Hash": "0x0000000000000000000000000000000000000000000000000000000000000000"}, "BeefyNextAuthoritySet": {"ID": 0, "Len": 0, "Root": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "ParachainHeads": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "ReferenceMMRRoot": [222, 1, 211, 27, 44, 75, 147, 193, 91, 134, 16, 83, 218, 149, 18, 204, 168, 50, 218, 81, 230, 176, 204, 1, 7, 80, 236, 48, 133, 212, 219, 180], "LeafHash": [54, 185, 6, 37, 103, 201, 119, 162, 184, 151, 248, 203, 213, 71, 206, 120, 250, 59, 213, 152, 239, 28, 107, 84, 153, 231, 18, 41, 81, 79, 227, 237], "LeafIndex": 59, "LeafCount": 60, "MMRProof": [[62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [127, 22, 26, 155, 215, 199, 129, 236, 124, 37, 224, 148, 160, 18, 189, 25, 49, 222, 193, 143, 170, 46, 170, 95, 213, 107, 32, 238, 25, 177, 68, 113], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185]], "SimplifiedMerkleProofItems": [[127, 22, 26, 155, 215, 199, 129, 236, 124, 37, 224, 148, 160, 18, 189, 25, 49, 222, 193, 143, 170, 46, 170, 95, 213, 107, 32, 238, 25, 177, 68, 113], [109, 255, 99, 253, 224, 232, 104, 235, 105, 51, 167, 11, 53, 164, 224, 130, 149, 183, 160, 98, 194, 116, 59, 172, 189, 146, 121, 5, 1, 153, 109, 185], [77, 1, 117, 238, 241, 131, 179, 39, 203, 97, 107, 124, 18, 78, 173, 195, 74, 78, 138, 109, 222, 176, 165, 237, 50, 116, 51, 190, 10, 142, 235, 73], [180, 189, 98, 245, 135, 184, 134, 94, 48, 172, 157, 132, 31, 249, 15, 16, 194, 175, 115, 22, 193, 163, 160, 189, 51, 36, 126, 137, 148, 43, 133, 202], [62, 189, 242, 250, 132, 223, 215, 169, 165, 162, 93, 144, 4, 176, 4, 18, 35, 175, 67, 113, 171, 218, 193, 72, 68, 53, 105, 10, 189, 234, 9, 194]], "SimplifiedMerkleProofOrder": 3}]