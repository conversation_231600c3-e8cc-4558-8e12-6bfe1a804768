// Copyright 2020 Snowfork
// SPDX-License-Identifier: LGPL-3.0-only

package sr25519

// Keypairs for use in tests

import (
	"github.com/snowfork/go-substrate-rpc-client/v4/signature"
)

func Alice() *Keypair {
	return NewKeypairFromKRP(signature.KeyringPair{
		URI:       "//Alice",
		Address:   "5GrwvaEF5zXb26Fz9rcQpDWS57CtERHpNehXCPcNoHGKutQY",
		PublicKey: []byte{0xd4, 0x35, 0x93, 0xc7, 0x15, 0xfd, 0xd3, 0x1c, 0x61, 0x14, 0x1a, 0xbd, 0x4, 0xa9, 0x9f, 0xd6, 0x82, 0x2c, 0x85, 0x58, 0x85, 0x4c, 0xcd, 0xe3, 0x9a, 0x56, 0x84, 0xe7, 0xa5, 0x6d, 0xa2, 0x7d},
	})
}

func Bob() *Keypair {
	return NewKeypairFromKRP(signature.KeyringPair{
		URI:       "//Bob",
		Address:   "5FHneW46xGXgs5mUiveU4sbTyGBzmstUspZC92UhjJM694ty",
		PublicKey: []byte{0x8e, 0xaf, 0x4, 0x15, 0x16, 0x87, 0x73, 0x63, 0x26, 0xc9, 0xfe, 0xa1, 0x7e, 0x25, 0xfc, 0x52, 0x87, 0x61, 0x36, 0x93, 0xc9, 0x12, 0x90, 0x9c, 0xb2, 0x26, 0xaa, 0x47, 0x94, 0xf2, 0x6a, 0x48},
	})
}
