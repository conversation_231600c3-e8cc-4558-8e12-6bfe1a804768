{"difficulty": "0xcd25fca770402", "extraData": "0x626565706f6f6c2e6f72675f3634c1", "gasLimit": "0xbebb30", "gasUsed": "0xbe6f4a", "hash": "0xb8aa96a4a573f149aeddca0e22b06c9dca082301c2fea7eb65996243313eec2a", "logsBloom": "0x54f050f560109a481184600b98035218b8923485a410c0e601410d300f1909252703afb32a4be3020081b2a3a84b4ba09a0410800d0e0a938008c2a2103e980438295a668f0067706982016e084010ea31d6708e22e0301050938f00dbe2622114038006a2a2815008810385999ebaf0ce1452c9aaed14861b0462108d43c0221936955d33c322a40340384c884238324bc005531350d01c112601c520950235820671796111aede3f90c18100d82d126a0d840c2842408212436db13a9d903a34ac00822a6080c08d20482014db0294204aa6202143c0363111b2d6410aa680a27d648073109a2c1910400014289fec7e812c2200cc9645a010884696081808", "miner": "0x99c85bb64564d9ef9a99621301f22c9993cb89e3", "mixHash": "0xfd3a67c095917bbf94e71db2c15b852441741b8e96a49bf9f437d7d94bcc745a", "nonce": "0x61708593eff9d4cf", "number": "0xae1436", "parentHash": "0x44679ae6dd71d65d510b3277b07ab107d3d15c3c7b8316b104dbf55123bb8abe", "receiptsRoot": "0xfd5e397a84884641f53c496804f24b5276cbb8c5c9cfc2342246be8e3ce5ad02", "sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347", "size": "0x91df", "stateRoot": "0x41ccad06b3502fe231d2e4e541a877509d1f998fc1696279fed4b61027582a72", "timestamp": "0x5fceb100", "totalDifficulty": "0x413028e5fa54ef6ccbe", "transactions": [], "transactionsRoot": "0x84d7fb20eff54c19387510fd646e16c3bc0879545799595b78bb2129242d770c", "uncles": []}