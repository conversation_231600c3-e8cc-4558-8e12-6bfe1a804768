// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: 2023 Snowfork <<EMAIL>>
// Generated, do not edit!
// See README.md for instructions to generate
#![cfg_attr(not(feature = "std"), no_std)]

use hex_literal::hex;
use snowbridge_beacon_primitives::{
	types::deneb, AncestryProof, BeaconHeader, ExecutionProof, NextSyncCommitteeUpdate,
	SyncAggregate, SyncCommittee, VersionedExecutionPayloadHeader,
};
use snowbridge_verification_primitives::{EventFixture, EventProof, Log, Proof};
use sp_core::U256;
use sp_std::{boxed::Box, vec};

const SC_SIZE: usize = 512;
const SC_BITS_SIZE: usize = 64;
type CheckpointUpdate = snowbridge_beacon_primitives::CheckpointUpdate<SC_SIZE>;
type Update = snowbridge_beacon_primitives::Update<SC_SIZE, SC_BITS_SIZE>;

pub fn make_checkpoint() -> Box<CheckpointUpdate> {
    Box::new(CheckpointUpdate {
        header: BeaconHeader {
            slot: {{CheckpointUpdate.Header.Slot}},
            proposer_index: {{CheckpointUpdate.Header.ProposerIndex}},
            parent_root: hex!("{{CheckpointUpdate.Header.ParentRoot}}").into(),
            state_root: hex!("{{CheckpointUpdate.Header.StateRoot}}").into(),
            body_root: hex!("{{CheckpointUpdate.Header.BodyRoot}}").into(),
        },
        current_sync_committee: SyncCommittee {
            pubkeys: [
            {{#CheckpointUpdate.CurrentSyncCommittee.Pubkeys}}
                hex!("{{.}}").into(),
            {{/CheckpointUpdate.CurrentSyncCommittee.Pubkeys}}
            ],
            aggregate_pubkey: hex!("{{CheckpointUpdate.CurrentSyncCommittee.AggregatePubkey}}").into(),
        },
        current_sync_committee_branch: vec![
            {{#CheckpointUpdate.CurrentSyncCommitteeBranch}}
                hex!("{{.}}").into(),
            {{/CheckpointUpdate.CurrentSyncCommitteeBranch}}
        ],
        validators_root: hex!("{{CheckpointUpdate.ValidatorsRoot}}").into(),
        block_roots_root: hex!("{{CheckpointUpdate.BlockRootsRoot}}").into(),
        block_roots_branch: vec![
        {{#CheckpointUpdate.BlockRootsBranch}}
            hex!("{{.}}").into(),
        {{/CheckpointUpdate.BlockRootsBranch}}
        ],
    })
}

pub fn make_sync_committee_update() -> Box<Update> {
    Box::new(Update {
        attested_header: BeaconHeader {
            slot: {{SyncCommitteeUpdate.AttestedHeader.Slot}},
            proposer_index: {{SyncCommitteeUpdate.AttestedHeader.ProposerIndex}},
            parent_root: hex!("{{SyncCommitteeUpdate.AttestedHeader.ParentRoot}}").into(),
            state_root: hex!("{{SyncCommitteeUpdate.AttestedHeader.StateRoot}}").into(),
            body_root: hex!("{{SyncCommitteeUpdate.AttestedHeader.BodyRoot}}").into(),
        },
        sync_aggregate: SyncAggregate{
            sync_committee_bits: hex!("{{SyncCommitteeUpdate.SyncAggregate.SyncCommitteeBits}}"),
            sync_committee_signature: hex!("{{SyncCommitteeUpdate.SyncAggregate.SyncCommitteeSignature}}").into(),
        },
        signature_slot: {{SyncCommitteeUpdate.SignatureSlot}},
        next_sync_committee_update: Some(NextSyncCommitteeUpdate {
            next_sync_committee: SyncCommittee {
                pubkeys: [
                {{#SyncCommitteeUpdate.NextSyncCommitteeUpdate.NextSyncCommittee.Pubkeys}}
                    hex!("{{.}}").into(),
                {{/SyncCommitteeUpdate.NextSyncCommitteeUpdate.NextSyncCommittee.Pubkeys}}
                ],
                aggregate_pubkey: hex!("{{SyncCommitteeUpdate.NextSyncCommitteeUpdate.NextSyncCommittee.AggregatePubkey}}").into(),
            },
            next_sync_committee_branch: vec![
            {{#SyncCommitteeUpdate.NextSyncCommitteeUpdate.NextSyncCommitteeBranch}}
                hex!("{{.}}").into(),
            {{/SyncCommitteeUpdate.NextSyncCommitteeUpdate.NextSyncCommitteeBranch}}
            ],
        }),
        finalized_header: BeaconHeader{
            slot: {{SyncCommitteeUpdate.FinalizedHeader.Slot}},
            proposer_index: {{SyncCommitteeUpdate.FinalizedHeader.ProposerIndex}},
            parent_root: hex!("{{SyncCommitteeUpdate.FinalizedHeader.ParentRoot}}").into(),
            state_root: hex!("{{SyncCommitteeUpdate.FinalizedHeader.StateRoot}}").into(),
            body_root: hex!("{{SyncCommitteeUpdate.FinalizedHeader.BodyRoot}}").into(),
        },
        finality_branch: vec![
        {{#SyncCommitteeUpdate.FinalityBranch}}
            hex!("{{.}}").into(),
        {{/SyncCommitteeUpdate.FinalityBranch}}
        ],
        block_roots_root: hex!("{{SyncCommitteeUpdate.BlockRootsRoot}}").into(),
        block_roots_branch: vec![
        {{#SyncCommitteeUpdate.BlockRootsBranch}}
            hex!("{{.}}").into(),
        {{/SyncCommitteeUpdate.BlockRootsBranch}}
        ],
    })
}

pub fn make_finalized_header_update() -> Box<Update> {
    Box::new(Update {
        attested_header: BeaconHeader {
            slot: {{FinalizedHeaderUpdate.AttestedHeader.Slot}},
            proposer_index: {{FinalizedHeaderUpdate.AttestedHeader.ProposerIndex}},
            parent_root: hex!("{{FinalizedHeaderUpdate.AttestedHeader.ParentRoot}}").into(),
            state_root: hex!("{{FinalizedHeaderUpdate.AttestedHeader.StateRoot}}").into(),
            body_root: hex!("{{FinalizedHeaderUpdate.AttestedHeader.BodyRoot}}").into(),
        },
        sync_aggregate: SyncAggregate{
            sync_committee_bits: hex!("{{FinalizedHeaderUpdate.SyncAggregate.SyncCommitteeBits}}"),
            sync_committee_signature: hex!("{{FinalizedHeaderUpdate.SyncAggregate.SyncCommitteeSignature}}").into(),
        },
        signature_slot: {{FinalizedHeaderUpdate.SignatureSlot}},
        next_sync_committee_update: None,
        finalized_header: BeaconHeader {
            slot: {{FinalizedHeaderUpdate.FinalizedHeader.Slot}},
            proposer_index: {{FinalizedHeaderUpdate.FinalizedHeader.ProposerIndex}},
            parent_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.ParentRoot}}").into(),
            state_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.StateRoot}}").into(),
            body_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.BodyRoot}}").into(),
        },
        finality_branch: vec![
        {{#FinalizedHeaderUpdate.FinalityBranch}}
            hex!("{{.}}").into(),
        {{/FinalizedHeaderUpdate.FinalityBranch}}
        ],
        block_roots_root: hex!("{{FinalizedHeaderUpdate.BlockRootsRoot}}").into(),
        block_roots_branch: vec![
        {{#FinalizedHeaderUpdate.BlockRootsBranch}}
            hex!("{{.}}").into(),
        {{/FinalizedHeaderUpdate.BlockRootsBranch}}
        ]
    })
}

pub fn make_execution_proof() -> Box<ExecutionProof> {
    Box::new(ExecutionProof {
        header: BeaconHeader {
            slot: {{HeaderUpdate.Header.Slot}},
            proposer_index: {{HeaderUpdate.Header.ProposerIndex}},
            parent_root: hex!("{{HeaderUpdate.Header.ParentRoot}}").into(),
            state_root: hex!("{{HeaderUpdate.Header.StateRoot}}").into(),
            body_root: hex!("{{HeaderUpdate.Header.BodyRoot}}").into(),
        },
        {{#HeaderUpdate.AncestryProof}}
        ancestry_proof: Some(AncestryProof {
            header_branch: vec![
            {{#HeaderUpdate.AncestryProof.HeaderBranch}}
                hex!("{{.}}").into(),
            {{/HeaderUpdate.AncestryProof.HeaderBranch}}
            ],
            finalized_block_root: hex!("{{HeaderUpdate.AncestryProof.FinalizedBlockRoot}}").into(),
        }),
        {{/HeaderUpdate.AncestryProof}}
        {{^HeaderUpdate.AncestryProof}}
        ancestry_proof: None,
        {{/HeaderUpdate.AncestryProof}}
        execution_header: VersionedExecutionPayloadHeader::Deneb(deneb::ExecutionPayloadHeader {
            parent_hash: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ParentHash}}").into(),
            fee_recipient: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.FeeRecipient}}").into(),
            state_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.StateRoot}}").into(),
            receipts_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ReceiptsRoot}}").into(),
            logs_bloom: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.LogsBloom}}").into(),
            prev_randao: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.PrevRandao}}").into(),
            block_number: {{HeaderUpdate.ExecutionHeader.Deneb.BlockNumber}},
            gas_limit: {{HeaderUpdate.ExecutionHeader.Deneb.GasLimit}},
            gas_used: {{HeaderUpdate.ExecutionHeader.Deneb.GasUsed}},
            timestamp: {{HeaderUpdate.ExecutionHeader.Deneb.Timestamp}},
            extra_data: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ExtraData}}").into(),
            base_fee_per_gas: U256::from({{HeaderUpdate.ExecutionHeader.Deneb.BaseFeePerGas}}u64),
            block_hash: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.BlockHash}}").into(),
            transactions_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.TransactionsRoot}}").into(),
            withdrawals_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.WithdrawalsRoot}}").into(),
            blob_gas_used: {{HeaderUpdate.ExecutionHeader.Deneb.BlobGasUsed}},
            excess_blob_gas: {{HeaderUpdate.ExecutionHeader.Deneb.ExcessBlobGas}},
        }),
        execution_branch: vec![
        {{#HeaderUpdate.ExecutionBranch}}
            hex!("{{.}}").into(),
        {{/HeaderUpdate.ExecutionBranch}}
        ],
    })
}

pub fn make_inbound_fixture() -> EventFixture {
    EventFixture {
        event: EventProof {
            event_log: 	Log {
                address: hex!("{{InboundMessage.EventLog.Address}}").into(),
                topics: vec![
                {{#InboundMessage.EventLog.Topics}}
                    hex!("{{.}}").into(),
                {{/InboundMessage.EventLog.Topics}}
                ],
                data: hex!("{{InboundMessage.EventLog.Data}}").into(),
            },
            proof: Proof {
                receipt_proof: (vec![
                {{#InboundMessage.Proof.ReceiptProof.Keys}}
                    hex!("{{.}}").to_vec(),
                {{/InboundMessage.Proof.ReceiptProof.Keys}}
                ], vec![
                {{#InboundMessage.Proof.ReceiptProof.Values}}
                    hex!("{{.}}").to_vec(),
                {{/InboundMessage.Proof.ReceiptProof.Values}}
                ]),
                execution_proof: ExecutionProof {
                    header: BeaconHeader {
                        slot: {{HeaderUpdate.Header.Slot}},
                        proposer_index: {{HeaderUpdate.Header.ProposerIndex}},
                        parent_root: hex!("{{HeaderUpdate.Header.ParentRoot}}").into(),
                        state_root: hex!("{{HeaderUpdate.Header.StateRoot}}").into(),
                        body_root: hex!("{{HeaderUpdate.Header.BodyRoot}}").into(),
                    },
                    {{#HeaderUpdate.AncestryProof}}
                        ancestry_proof: Some(AncestryProof {
                        header_branch: vec![
                        {{#HeaderUpdate.AncestryProof.HeaderBranch}}
                            hex!("{{.}}").into(),
                        {{/HeaderUpdate.AncestryProof.HeaderBranch}}
                        ],
                        finalized_block_root: hex!("{{HeaderUpdate.AncestryProof.FinalizedBlockRoot}}").into(),
                        }),
                    {{/HeaderUpdate.AncestryProof}}
                    {{^HeaderUpdate.AncestryProof}}
                        ancestry_proof: None,
                    {{/HeaderUpdate.AncestryProof}}
                    execution_header: VersionedExecutionPayloadHeader::Deneb(deneb::ExecutionPayloadHeader {
                        parent_hash: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ParentHash}}").into(),
                        fee_recipient: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.FeeRecipient}}").into(),
                        state_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.StateRoot}}").into(),
                        receipts_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ReceiptsRoot}}").into(),
                        logs_bloom: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.LogsBloom}}").into(),
                        prev_randao: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.PrevRandao}}").into(),
                        block_number: {{HeaderUpdate.ExecutionHeader.Deneb.BlockNumber}},
                        gas_limit: {{HeaderUpdate.ExecutionHeader.Deneb.GasLimit}},
                        gas_used: {{HeaderUpdate.ExecutionHeader.Deneb.GasUsed}},
                        timestamp: {{HeaderUpdate.ExecutionHeader.Deneb.Timestamp}},
                        extra_data: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.ExtraData}}").into(),
                        base_fee_per_gas: U256::from({{HeaderUpdate.ExecutionHeader.Deneb.BaseFeePerGas}}u64),
                        block_hash: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.BlockHash}}").into(),
                        transactions_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.TransactionsRoot}}").into(),
                        withdrawals_root: hex!("{{HeaderUpdate.ExecutionHeader.Deneb.WithdrawalsRoot}}").into(),
                        blob_gas_used: {{HeaderUpdate.ExecutionHeader.Deneb.BlobGasUsed}},
                        excess_blob_gas: {{HeaderUpdate.ExecutionHeader.Deneb.ExcessBlobGas}},
                    }),
                    execution_branch: vec![
                        {{#HeaderUpdate.ExecutionBranch}}
                            hex!("{{.}}").into(),
                        {{/HeaderUpdate.ExecutionBranch}}
                    ],
                }
            },
        },
        finalized_header: BeaconHeader {
            slot: {{FinalizedHeaderUpdate.FinalizedHeader.Slot}},
            proposer_index: {{FinalizedHeaderUpdate.FinalizedHeader.ProposerIndex}},
            parent_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.ParentRoot}}").into(),
            state_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.StateRoot}}").into(),
            body_root: hex!("{{FinalizedHeaderUpdate.FinalizedHeader.BodyRoot}}").into(),
        },
        block_roots_root: hex!("{{FinalizedHeaderUpdate.BlockRootsRoot}}").into(),
    }
}
