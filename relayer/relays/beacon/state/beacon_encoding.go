// Code generated by fastssz. DO NOT EDIT.
// Hash: e80ede50b28a6c8990c5466d0a25a807e20ae9e7c1bd2bcfbe6d1f8df0150bc6
// Version: 0.1.3
package state

import (
	ssz "github.com/ferranbt/fastssz"
)

// MarshalSSZ ssz marshals the Checkpoint object
func (c *Checkpoint) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(c)
}

// MarshalSSZTo ssz marshals the Checkpoint object to a target array
func (c *Checkpoint) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Epoch'
	dst = ssz.MarshalUint64(dst, c.Epoch)

	// Field (1) 'Root'
	if size := len(c.Root); size != 32 {
		err = ssz.ErrBytesLengthFn("Checkpoint.Root", size, 32)
		return
	}
	dst = append(dst, c.Root...)

	return
}

// UnmarshalSSZ ssz unmarshals the Checkpoint object
func (c *Checkpoint) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 40 {
		return ssz.ErrSize
	}

	// Field (0) 'Epoch'
	c.Epoch = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'Root'
	if cap(c.Root) == 0 {
		c.Root = make([]byte, 0, len(buf[8:40]))
	}
	c.Root = append(c.Root, buf[8:40]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Checkpoint object
func (c *Checkpoint) SizeSSZ() (size int) {
	size = 40
	return
}

// HashTreeRoot ssz hashes the Checkpoint object
func (c *Checkpoint) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(c)
}

// HashTreeRootWith ssz hashes the Checkpoint object with a hasher
func (c *Checkpoint) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Epoch'
	hh.PutUint64(c.Epoch)

	// Field (1) 'Root'
	if size := len(c.Root); size != 32 {
		err = ssz.ErrBytesLengthFn("Checkpoint.Root", size, 32)
		return
	}
	hh.PutBytes(c.Root)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Checkpoint object
func (c *Checkpoint) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(c)
}

// MarshalSSZ ssz marshals the AttestationData object
func (a *AttestationData) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(a)
}

// MarshalSSZTo ssz marshals the AttestationData object to a target array
func (a *AttestationData) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Slot'
	dst = ssz.MarshalUint64(dst, uint64(a.Slot))

	// Field (1) 'Index'
	dst = ssz.MarshalUint64(dst, a.Index)

	// Field (2) 'BeaconBlockHash'
	dst = append(dst, a.BeaconBlockHash[:]...)

	// Field (3) 'Source'
	if a.Source == nil {
		a.Source = new(Checkpoint)
	}
	if dst, err = a.Source.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (4) 'Target'
	if a.Target == nil {
		a.Target = new(Checkpoint)
	}
	if dst, err = a.Target.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the AttestationData object
func (a *AttestationData) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 128 {
		return ssz.ErrSize
	}

	// Field (0) 'Slot'
	a.Slot = Slot(ssz.UnmarshallUint64(buf[0:8]))

	// Field (1) 'Index'
	a.Index = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'BeaconBlockHash'
	copy(a.BeaconBlockHash[:], buf[16:48])

	// Field (3) 'Source'
	if a.Source == nil {
		a.Source = new(Checkpoint)
	}
	if err = a.Source.UnmarshalSSZ(buf[48:88]); err != nil {
		return err
	}

	// Field (4) 'Target'
	if a.Target == nil {
		a.Target = new(Checkpoint)
	}
	if err = a.Target.UnmarshalSSZ(buf[88:128]); err != nil {
		return err
	}

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the AttestationData object
func (a *AttestationData) SizeSSZ() (size int) {
	size = 128
	return
}

// HashTreeRoot ssz hashes the AttestationData object
func (a *AttestationData) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(a)
}

// HashTreeRootWith ssz hashes the AttestationData object with a hasher
func (a *AttestationData) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Slot'
	hh.PutUint64(uint64(a.Slot))

	// Field (1) 'Index'
	hh.PutUint64(a.Index)

	// Field (2) 'BeaconBlockHash'
	hh.PutBytes(a.BeaconBlockHash[:])

	// Field (3) 'Source'
	if a.Source == nil {
		a.Source = new(Checkpoint)
	}
	if err = a.Source.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (4) 'Target'
	if a.Target == nil {
		a.Target = new(Checkpoint)
	}
	if err = a.Target.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the AttestationData object
func (a *AttestationData) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(a)
}

// MarshalSSZ ssz marshals the Attestation object
func (a *Attestation) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(a)
}

// MarshalSSZTo ssz marshals the Attestation object to a target array
func (a *Attestation) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(228)

	// Offset (0) 'AggregationBits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(a.AggregationBits)

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if dst, err = a.Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Signature'
	dst = append(dst, a.Signature[:]...)

	// Field (0) 'AggregationBits'
	if size := len(a.AggregationBits); size > 2048 {
		err = ssz.ErrBytesLengthFn("Attestation.AggregationBits", size, 2048)
		return
	}
	dst = append(dst, a.AggregationBits...)

	return
}

// UnmarshalSSZ ssz unmarshals the Attestation object
func (a *Attestation) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 228 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'AggregationBits'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 228 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if err = a.Data.UnmarshalSSZ(buf[4:132]); err != nil {
		return err
	}

	// Field (2) 'Signature'
	copy(a.Signature[:], buf[132:228])

	// Field (0) 'AggregationBits'
	{
		buf = tail[o0:]
		if err = ssz.ValidateBitlist(buf, 2048); err != nil {
			return err
		}
		if cap(a.AggregationBits) == 0 {
			a.AggregationBits = make([]byte, 0, len(buf))
		}
		a.AggregationBits = append(a.AggregationBits, buf...)
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Attestation object
func (a *Attestation) SizeSSZ() (size int) {
	size = 228

	// Field (0) 'AggregationBits'
	size += len(a.AggregationBits)

	return
}

// HashTreeRoot ssz hashes the Attestation object
func (a *Attestation) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(a)
}

// HashTreeRootWith ssz hashes the Attestation object with a hasher
func (a *Attestation) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'AggregationBits'
	if len(a.AggregationBits) == 0 {
		err = ssz.ErrEmptyBitlist
		return
	}
	hh.PutBitlist(a.AggregationBits, 2048)

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if err = a.Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Signature'
	hh.PutBytes(a.Signature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Attestation object
func (a *Attestation) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(a)
}

// MarshalSSZ ssz marshals the DepositData object
func (d *DepositData) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(d)
}

// MarshalSSZTo ssz marshals the DepositData object to a target array
func (d *DepositData) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Pubkey'
	dst = append(dst, d.Pubkey[:]...)

	// Field (1) 'WithdrawalCredentials'
	dst = append(dst, d.WithdrawalCredentials[:]...)

	// Field (2) 'Amount'
	dst = ssz.MarshalUint64(dst, d.Amount)

	// Field (3) 'Signature'
	if size := len(d.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("DepositData.Signature", size, 96)
		return
	}
	dst = append(dst, d.Signature...)

	return
}

// UnmarshalSSZ ssz unmarshals the DepositData object
func (d *DepositData) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 184 {
		return ssz.ErrSize
	}

	// Field (0) 'Pubkey'
	copy(d.Pubkey[:], buf[0:48])

	// Field (1) 'WithdrawalCredentials'
	copy(d.WithdrawalCredentials[:], buf[48:80])

	// Field (2) 'Amount'
	d.Amount = ssz.UnmarshallUint64(buf[80:88])

	// Field (3) 'Signature'
	if cap(d.Signature) == 0 {
		d.Signature = make([]byte, 0, len(buf[88:184]))
	}
	d.Signature = append(d.Signature, buf[88:184]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the DepositData object
func (d *DepositData) SizeSSZ() (size int) {
	size = 184
	return
}

// HashTreeRoot ssz hashes the DepositData object
func (d *DepositData) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(d)
}

// HashTreeRootWith ssz hashes the DepositData object with a hasher
func (d *DepositData) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Pubkey'
	hh.PutBytes(d.Pubkey[:])

	// Field (1) 'WithdrawalCredentials'
	hh.PutBytes(d.WithdrawalCredentials[:])

	// Field (2) 'Amount'
	hh.PutUint64(d.Amount)

	// Field (3) 'Signature'
	if size := len(d.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("DepositData.Signature", size, 96)
		return
	}
	hh.PutBytes(d.Signature)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the DepositData object
func (d *DepositData) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(d)
}

// MarshalSSZ ssz marshals the Deposit object
func (d *Deposit) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(d)
}

// MarshalSSZTo ssz marshals the Deposit object to a target array
func (d *Deposit) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Proof'
	if size := len(d.Proof); size != 33 {
		err = ssz.ErrVectorLengthFn("Deposit.Proof", size, 33)
		return
	}
	for ii := 0; ii < 33; ii++ {
		if size := len(d.Proof[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("Deposit.Proof[ii]", size, 32)
			return
		}
		dst = append(dst, d.Proof[ii]...)
	}

	// Field (1) 'Data'
	if d.Data == nil {
		d.Data = new(DepositData)
	}
	if dst, err = d.Data.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the Deposit object
func (d *Deposit) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 1240 {
		return ssz.ErrSize
	}

	// Field (0) 'Proof'
	d.Proof = make([][]byte, 33)
	for ii := 0; ii < 33; ii++ {
		if cap(d.Proof[ii]) == 0 {
			d.Proof[ii] = make([]byte, 0, len(buf[0:1056][ii*32:(ii+1)*32]))
		}
		d.Proof[ii] = append(d.Proof[ii], buf[0:1056][ii*32:(ii+1)*32]...)
	}

	// Field (1) 'Data'
	if d.Data == nil {
		d.Data = new(DepositData)
	}
	if err = d.Data.UnmarshalSSZ(buf[1056:1240]); err != nil {
		return err
	}

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Deposit object
func (d *Deposit) SizeSSZ() (size int) {
	size = 1240
	return
}

// HashTreeRoot ssz hashes the Deposit object
func (d *Deposit) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(d)
}

// HashTreeRootWith ssz hashes the Deposit object with a hasher
func (d *Deposit) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Proof'
	{
		if size := len(d.Proof); size != 33 {
			err = ssz.ErrVectorLengthFn("Deposit.Proof", size, 33)
			return
		}
		subIndx := hh.Index()
		for _, i := range d.Proof {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (1) 'Data'
	if d.Data == nil {
		d.Data = new(DepositData)
	}
	if err = d.Data.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Deposit object
func (d *Deposit) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(d)
}

// MarshalSSZ ssz marshals the IndexedAttestation object
func (i *IndexedAttestation) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(i)
}

// MarshalSSZTo ssz marshals the IndexedAttestation object to a target array
func (i *IndexedAttestation) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(228)

	// Offset (0) 'AttestationIndices'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(i.AttestationIndices) * 8

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if dst, err = i.Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Signature'
	if size := len(i.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("IndexedAttestation.Signature", size, 96)
		return
	}
	dst = append(dst, i.Signature...)

	// Field (0) 'AttestationIndices'
	if size := len(i.AttestationIndices); size > 2048 {
		err = ssz.ErrListTooBigFn("IndexedAttestation.AttestationIndices", size, 2048)
		return
	}
	for ii := 0; ii < len(i.AttestationIndices); ii++ {
		dst = ssz.MarshalUint64(dst, i.AttestationIndices[ii])
	}

	return
}

// UnmarshalSSZ ssz unmarshals the IndexedAttestation object
func (i *IndexedAttestation) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 228 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'AttestationIndices'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 228 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if err = i.Data.UnmarshalSSZ(buf[4:132]); err != nil {
		return err
	}

	// Field (2) 'Signature'
	if cap(i.Signature) == 0 {
		i.Signature = make([]byte, 0, len(buf[132:228]))
	}
	i.Signature = append(i.Signature, buf[132:228]...)

	// Field (0) 'AttestationIndices'
	{
		buf = tail[o0:]
		num, err := ssz.DivideInt2(len(buf), 8, 2048)
		if err != nil {
			return err
		}
		i.AttestationIndices = ssz.ExtendUint64(i.AttestationIndices, num)
		for ii := 0; ii < num; ii++ {
			i.AttestationIndices[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the IndexedAttestation object
func (i *IndexedAttestation) SizeSSZ() (size int) {
	size = 228

	// Field (0) 'AttestationIndices'
	size += len(i.AttestationIndices) * 8

	return
}

// HashTreeRoot ssz hashes the IndexedAttestation object
func (i *IndexedAttestation) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(i)
}

// HashTreeRootWith ssz hashes the IndexedAttestation object with a hasher
func (i *IndexedAttestation) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'AttestationIndices'
	{
		if size := len(i.AttestationIndices); size > 2048 {
			err = ssz.ErrListTooBigFn("IndexedAttestation.AttestationIndices", size, 2048)
			return
		}
		subIndx := hh.Index()
		for _, i := range i.AttestationIndices {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(i.AttestationIndices))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(2048, numItems, 8))
	}

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if err = i.Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Signature'
	if size := len(i.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("IndexedAttestation.Signature", size, 96)
		return
	}
	hh.PutBytes(i.Signature)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the IndexedAttestation object
func (i *IndexedAttestation) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(i)
}

// MarshalSSZ ssz marshals the Fork object
func (f *Fork) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(f)
}

// MarshalSSZTo ssz marshals the Fork object to a target array
func (f *Fork) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'PreviousVersion'
	if size := len(f.PreviousVersion); size != 4 {
		err = ssz.ErrBytesLengthFn("Fork.PreviousVersion", size, 4)
		return
	}
	dst = append(dst, f.PreviousVersion...)

	// Field (1) 'CurrentVersion'
	if size := len(f.CurrentVersion); size != 4 {
		err = ssz.ErrBytesLengthFn("Fork.CurrentVersion", size, 4)
		return
	}
	dst = append(dst, f.CurrentVersion...)

	// Field (2) 'Epoch'
	dst = ssz.MarshalUint64(dst, f.Epoch)

	return
}

// UnmarshalSSZ ssz unmarshals the Fork object
func (f *Fork) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 16 {
		return ssz.ErrSize
	}

	// Field (0) 'PreviousVersion'
	if cap(f.PreviousVersion) == 0 {
		f.PreviousVersion = make([]byte, 0, len(buf[0:4]))
	}
	f.PreviousVersion = append(f.PreviousVersion, buf[0:4]...)

	// Field (1) 'CurrentVersion'
	if cap(f.CurrentVersion) == 0 {
		f.CurrentVersion = make([]byte, 0, len(buf[4:8]))
	}
	f.CurrentVersion = append(f.CurrentVersion, buf[4:8]...)

	// Field (2) 'Epoch'
	f.Epoch = ssz.UnmarshallUint64(buf[8:16])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Fork object
func (f *Fork) SizeSSZ() (size int) {
	size = 16
	return
}

// HashTreeRoot ssz hashes the Fork object
func (f *Fork) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(f)
}

// HashTreeRootWith ssz hashes the Fork object with a hasher
func (f *Fork) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'PreviousVersion'
	if size := len(f.PreviousVersion); size != 4 {
		err = ssz.ErrBytesLengthFn("Fork.PreviousVersion", size, 4)
		return
	}
	hh.PutBytes(f.PreviousVersion)

	// Field (1) 'CurrentVersion'
	if size := len(f.CurrentVersion); size != 4 {
		err = ssz.ErrBytesLengthFn("Fork.CurrentVersion", size, 4)
		return
	}
	hh.PutBytes(f.CurrentVersion)

	// Field (2) 'Epoch'
	hh.PutUint64(f.Epoch)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Fork object
func (f *Fork) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(f)
}

// MarshalSSZ ssz marshals the Validator object
func (v *Validator) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(v)
}

// MarshalSSZTo ssz marshals the Validator object to a target array
func (v *Validator) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Pubkey'
	if size := len(v.Pubkey); size != 48 {
		err = ssz.ErrBytesLengthFn("Validator.Pubkey", size, 48)
		return
	}
	dst = append(dst, v.Pubkey...)

	// Field (1) 'WithdrawalCredentials'
	if size := len(v.WithdrawalCredentials); size != 32 {
		err = ssz.ErrBytesLengthFn("Validator.WithdrawalCredentials", size, 32)
		return
	}
	dst = append(dst, v.WithdrawalCredentials...)

	// Field (2) 'EffectiveBalance'
	dst = ssz.MarshalUint64(dst, v.EffectiveBalance)

	// Field (3) 'Slashed'
	dst = ssz.MarshalBool(dst, v.Slashed)

	// Field (4) 'ActivationEligibilityEpoch'
	dst = ssz.MarshalUint64(dst, v.ActivationEligibilityEpoch)

	// Field (5) 'ActivationEpoch'
	dst = ssz.MarshalUint64(dst, v.ActivationEpoch)

	// Field (6) 'ExitEpoch'
	dst = ssz.MarshalUint64(dst, v.ExitEpoch)

	// Field (7) 'WithdrawableEpoch'
	dst = ssz.MarshalUint64(dst, v.WithdrawableEpoch)

	return
}

// UnmarshalSSZ ssz unmarshals the Validator object
func (v *Validator) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 121 {
		return ssz.ErrSize
	}

	// Field (0) 'Pubkey'
	if cap(v.Pubkey) == 0 {
		v.Pubkey = make([]byte, 0, len(buf[0:48]))
	}
	v.Pubkey = append(v.Pubkey, buf[0:48]...)

	// Field (1) 'WithdrawalCredentials'
	if cap(v.WithdrawalCredentials) == 0 {
		v.WithdrawalCredentials = make([]byte, 0, len(buf[48:80]))
	}
	v.WithdrawalCredentials = append(v.WithdrawalCredentials, buf[48:80]...)

	// Field (2) 'EffectiveBalance'
	v.EffectiveBalance = ssz.UnmarshallUint64(buf[80:88])

	// Field (3) 'Slashed'
	v.Slashed = ssz.UnmarshalBool(buf[88:89])

	// Field (4) 'ActivationEligibilityEpoch'
	v.ActivationEligibilityEpoch = ssz.UnmarshallUint64(buf[89:97])

	// Field (5) 'ActivationEpoch'
	v.ActivationEpoch = ssz.UnmarshallUint64(buf[97:105])

	// Field (6) 'ExitEpoch'
	v.ExitEpoch = ssz.UnmarshallUint64(buf[105:113])

	// Field (7) 'WithdrawableEpoch'
	v.WithdrawableEpoch = ssz.UnmarshallUint64(buf[113:121])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Validator object
func (v *Validator) SizeSSZ() (size int) {
	size = 121
	return
}

// HashTreeRoot ssz hashes the Validator object
func (v *Validator) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(v)
}

// HashTreeRootWith ssz hashes the Validator object with a hasher
func (v *Validator) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Pubkey'
	if size := len(v.Pubkey); size != 48 {
		err = ssz.ErrBytesLengthFn("Validator.Pubkey", size, 48)
		return
	}
	hh.PutBytes(v.Pubkey)

	// Field (1) 'WithdrawalCredentials'
	if size := len(v.WithdrawalCredentials); size != 32 {
		err = ssz.ErrBytesLengthFn("Validator.WithdrawalCredentials", size, 32)
		return
	}
	hh.PutBytes(v.WithdrawalCredentials)

	// Field (2) 'EffectiveBalance'
	hh.PutUint64(v.EffectiveBalance)

	// Field (3) 'Slashed'
	hh.PutBool(v.Slashed)

	// Field (4) 'ActivationEligibilityEpoch'
	hh.PutUint64(v.ActivationEligibilityEpoch)

	// Field (5) 'ActivationEpoch'
	hh.PutUint64(v.ActivationEpoch)

	// Field (6) 'ExitEpoch'
	hh.PutUint64(v.ExitEpoch)

	// Field (7) 'WithdrawableEpoch'
	hh.PutUint64(v.WithdrawableEpoch)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Validator object
func (v *Validator) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(v)
}

// MarshalSSZ ssz marshals the VoluntaryExit object
func (v *VoluntaryExit) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(v)
}

// MarshalSSZTo ssz marshals the VoluntaryExit object to a target array
func (v *VoluntaryExit) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Epoch'
	dst = ssz.MarshalUint64(dst, v.Epoch)

	// Field (1) 'ValidatorIndex'
	dst = ssz.MarshalUint64(dst, v.ValidatorIndex)

	return
}

// UnmarshalSSZ ssz unmarshals the VoluntaryExit object
func (v *VoluntaryExit) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 16 {
		return ssz.ErrSize
	}

	// Field (0) 'Epoch'
	v.Epoch = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'ValidatorIndex'
	v.ValidatorIndex = ssz.UnmarshallUint64(buf[8:16])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the VoluntaryExit object
func (v *VoluntaryExit) SizeSSZ() (size int) {
	size = 16
	return
}

// HashTreeRoot ssz hashes the VoluntaryExit object
func (v *VoluntaryExit) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(v)
}

// HashTreeRootWith ssz hashes the VoluntaryExit object with a hasher
func (v *VoluntaryExit) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Epoch'
	hh.PutUint64(v.Epoch)

	// Field (1) 'ValidatorIndex'
	hh.PutUint64(v.ValidatorIndex)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the VoluntaryExit object
func (v *VoluntaryExit) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(v)
}

// MarshalSSZ ssz marshals the SignedVoluntaryExit object
func (s *SignedVoluntaryExit) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SignedVoluntaryExit object to a target array
func (s *SignedVoluntaryExit) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Exit'
	if s.Exit == nil {
		s.Exit = new(VoluntaryExit)
	}
	if dst, err = s.Exit.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Signature'
	dst = append(dst, s.Signature[:]...)

	return
}

// UnmarshalSSZ ssz unmarshals the SignedVoluntaryExit object
func (s *SignedVoluntaryExit) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 112 {
		return ssz.ErrSize
	}

	// Field (0) 'Exit'
	if s.Exit == nil {
		s.Exit = new(VoluntaryExit)
	}
	if err = s.Exit.UnmarshalSSZ(buf[0:16]); err != nil {
		return err
	}

	// Field (1) 'Signature'
	copy(s.Signature[:], buf[16:112])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SignedVoluntaryExit object
func (s *SignedVoluntaryExit) SizeSSZ() (size int) {
	size = 112
	return
}

// HashTreeRoot ssz hashes the SignedVoluntaryExit object
func (s *SignedVoluntaryExit) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SignedVoluntaryExit object with a hasher
func (s *SignedVoluntaryExit) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Exit'
	if s.Exit == nil {
		s.Exit = new(VoluntaryExit)
	}
	if err = s.Exit.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Signature'
	hh.PutBytes(s.Signature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SignedVoluntaryExit object
func (s *SignedVoluntaryExit) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the Eth1Data object
func (e *Eth1Data) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(e)
}

// MarshalSSZTo ssz marshals the Eth1Data object to a target array
func (e *Eth1Data) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'DepositRoot'
	if size := len(e.DepositRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("Eth1Data.DepositRoot", size, 32)
		return
	}
	dst = append(dst, e.DepositRoot...)

	// Field (1) 'DepositCount'
	dst = ssz.MarshalUint64(dst, e.DepositCount)

	// Field (2) 'BlockHash'
	if size := len(e.BlockHash); size != 32 {
		err = ssz.ErrBytesLengthFn("Eth1Data.BlockHash", size, 32)
		return
	}
	dst = append(dst, e.BlockHash...)

	return
}

// UnmarshalSSZ ssz unmarshals the Eth1Data object
func (e *Eth1Data) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 72 {
		return ssz.ErrSize
	}

	// Field (0) 'DepositRoot'
	if cap(e.DepositRoot) == 0 {
		e.DepositRoot = make([]byte, 0, len(buf[0:32]))
	}
	e.DepositRoot = append(e.DepositRoot, buf[0:32]...)

	// Field (1) 'DepositCount'
	e.DepositCount = ssz.UnmarshallUint64(buf[32:40])

	// Field (2) 'BlockHash'
	if cap(e.BlockHash) == 0 {
		e.BlockHash = make([]byte, 0, len(buf[40:72]))
	}
	e.BlockHash = append(e.BlockHash, buf[40:72]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Eth1Data object
func (e *Eth1Data) SizeSSZ() (size int) {
	size = 72
	return
}

// HashTreeRoot ssz hashes the Eth1Data object
func (e *Eth1Data) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(e)
}

// HashTreeRootWith ssz hashes the Eth1Data object with a hasher
func (e *Eth1Data) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'DepositRoot'
	if size := len(e.DepositRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("Eth1Data.DepositRoot", size, 32)
		return
	}
	hh.PutBytes(e.DepositRoot)

	// Field (1) 'DepositCount'
	hh.PutUint64(e.DepositCount)

	// Field (2) 'BlockHash'
	if size := len(e.BlockHash); size != 32 {
		err = ssz.ErrBytesLengthFn("Eth1Data.BlockHash", size, 32)
		return
	}
	hh.PutBytes(e.BlockHash)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Eth1Data object
func (e *Eth1Data) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(e)
}

// MarshalSSZ ssz marshals the ProposerSlashing object
func (p *ProposerSlashing) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(p)
}

// MarshalSSZTo ssz marshals the ProposerSlashing object to a target array
func (p *ProposerSlashing) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Header1'
	if p.Header1 == nil {
		p.Header1 = new(SignedBeaconBlockHeader)
	}
	if dst, err = p.Header1.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Header2'
	if p.Header2 == nil {
		p.Header2 = new(SignedBeaconBlockHeader)
	}
	if dst, err = p.Header2.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the ProposerSlashing object
func (p *ProposerSlashing) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 416 {
		return ssz.ErrSize
	}

	// Field (0) 'Header1'
	if p.Header1 == nil {
		p.Header1 = new(SignedBeaconBlockHeader)
	}
	if err = p.Header1.UnmarshalSSZ(buf[0:208]); err != nil {
		return err
	}

	// Field (1) 'Header2'
	if p.Header2 == nil {
		p.Header2 = new(SignedBeaconBlockHeader)
	}
	if err = p.Header2.UnmarshalSSZ(buf[208:416]); err != nil {
		return err
	}

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the ProposerSlashing object
func (p *ProposerSlashing) SizeSSZ() (size int) {
	size = 416
	return
}

// HashTreeRoot ssz hashes the ProposerSlashing object
func (p *ProposerSlashing) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(p)
}

// HashTreeRootWith ssz hashes the ProposerSlashing object with a hasher
func (p *ProposerSlashing) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Header1'
	if p.Header1 == nil {
		p.Header1 = new(SignedBeaconBlockHeader)
	}
	if err = p.Header1.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Header2'
	if p.Header2 == nil {
		p.Header2 = new(SignedBeaconBlockHeader)
	}
	if err = p.Header2.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the ProposerSlashing object
func (p *ProposerSlashing) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(p)
}

// MarshalSSZ ssz marshals the AttesterSlashing object
func (a *AttesterSlashing) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(a)
}

// MarshalSSZTo ssz marshals the AttesterSlashing object to a target array
func (a *AttesterSlashing) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(8)

	// Offset (0) 'Attestation1'
	dst = ssz.WriteOffset(dst, offset)
	if a.Attestation1 == nil {
		a.Attestation1 = new(IndexedAttestation)
	}
	offset += a.Attestation1.SizeSSZ()

	// Offset (1) 'Attestation2'
	dst = ssz.WriteOffset(dst, offset)
	if a.Attestation2 == nil {
		a.Attestation2 = new(IndexedAttestation)
	}
	offset += a.Attestation2.SizeSSZ()

	// Field (0) 'Attestation1'
	if dst, err = a.Attestation1.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Attestation2'
	if dst, err = a.Attestation2.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the AttesterSlashing object
func (a *AttesterSlashing) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 8 {
		return ssz.ErrSize
	}

	tail := buf
	var o0, o1 uint64

	// Offset (0) 'Attestation1'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 8 {
		return ssz.ErrInvalidVariableOffset
	}

	// Offset (1) 'Attestation2'
	if o1 = ssz.ReadOffset(buf[4:8]); o1 > size || o0 > o1 {
		return ssz.ErrOffset
	}

	// Field (0) 'Attestation1'
	{
		buf = tail[o0:o1]
		if a.Attestation1 == nil {
			a.Attestation1 = new(IndexedAttestation)
		}
		if err = a.Attestation1.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (1) 'Attestation2'
	{
		buf = tail[o1:]
		if a.Attestation2 == nil {
			a.Attestation2 = new(IndexedAttestation)
		}
		if err = a.Attestation2.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the AttesterSlashing object
func (a *AttesterSlashing) SizeSSZ() (size int) {
	size = 8

	// Field (0) 'Attestation1'
	if a.Attestation1 == nil {
		a.Attestation1 = new(IndexedAttestation)
	}
	size += a.Attestation1.SizeSSZ()

	// Field (1) 'Attestation2'
	if a.Attestation2 == nil {
		a.Attestation2 = new(IndexedAttestation)
	}
	size += a.Attestation2.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the AttesterSlashing object
func (a *AttesterSlashing) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(a)
}

// HashTreeRootWith ssz hashes the AttesterSlashing object with a hasher
func (a *AttesterSlashing) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Attestation1'
	if err = a.Attestation1.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Attestation2'
	if err = a.Attestation2.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the AttesterSlashing object
func (a *AttesterSlashing) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(a)
}

// MarshalSSZ ssz marshals the BlockRootsContainerMainnet object
func (b *BlockRootsContainerMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BlockRootsContainerMainnet object to a target array
func (b *BlockRootsContainerMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'BlockRoots'
	if size := len(b.BlockRoots); size != 8192 {
		err = ssz.ErrVectorLengthFn("BlockRootsContainerMainnet.BlockRoots", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		if size := len(b.BlockRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BlockRootsContainerMainnet.BlockRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.BlockRoots[ii]...)
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BlockRootsContainerMainnet object
func (b *BlockRootsContainerMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 262144 {
		return ssz.ErrSize
	}

	// Field (0) 'BlockRoots'
	b.BlockRoots = make([][]byte, 8192)
	for ii := 0; ii < 8192; ii++ {
		if cap(b.BlockRoots[ii]) == 0 {
			b.BlockRoots[ii] = make([]byte, 0, len(buf[0:262144][ii*32:(ii+1)*32]))
		}
		b.BlockRoots[ii] = append(b.BlockRoots[ii], buf[0:262144][ii*32:(ii+1)*32]...)
	}

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BlockRootsContainerMainnet object
func (b *BlockRootsContainerMainnet) SizeSSZ() (size int) {
	size = 262144
	return
}

// HashTreeRoot ssz hashes the BlockRootsContainerMainnet object
func (b *BlockRootsContainerMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BlockRootsContainerMainnet object with a hasher
func (b *BlockRootsContainerMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'BlockRoots'
	{
		if size := len(b.BlockRoots); size != 8192 {
			err = ssz.ErrVectorLengthFn("BlockRootsContainerMainnet.BlockRoots", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.BlockRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BlockRootsContainerMainnet object
func (b *BlockRootsContainerMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the TransactionsRootContainer object
func (t *TransactionsRootContainer) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(t)
}

// MarshalSSZTo ssz marshals the TransactionsRootContainer object to a target array
func (t *TransactionsRootContainer) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(4)

	// Offset (0) 'Transactions'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(t.Transactions); ii++ {
		offset += 4
		offset += len(t.Transactions[ii])
	}

	// Field (0) 'Transactions'
	if size := len(t.Transactions); size > 1048576 {
		err = ssz.ErrListTooBigFn("TransactionsRootContainer.Transactions", size, 1048576)
		return
	}
	{
		offset = 4 * len(t.Transactions)
		for ii := 0; ii < len(t.Transactions); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += len(t.Transactions[ii])
		}
	}
	for ii := 0; ii < len(t.Transactions); ii++ {
		if size := len(t.Transactions[ii]); size > 1073741824 {
			err = ssz.ErrBytesLengthFn("TransactionsRootContainer.Transactions[ii]", size, 1073741824)
			return
		}
		dst = append(dst, t.Transactions[ii]...)
	}

	return
}

// UnmarshalSSZ ssz unmarshals the TransactionsRootContainer object
func (t *TransactionsRootContainer) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 4 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'Transactions'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 4 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (0) 'Transactions'
	{
		buf = tail[o0:]
		num, err := ssz.DecodeDynamicLength(buf, 1048576)
		if err != nil {
			return err
		}
		t.Transactions = make([][]byte, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if len(buf) > 1073741824 {
				return ssz.ErrBytesLength
			}
			if cap(t.Transactions[indx]) == 0 {
				t.Transactions[indx] = make([]byte, 0, len(buf))
			}
			t.Transactions[indx] = append(t.Transactions[indx], buf...)
			return nil
		})
		if err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the TransactionsRootContainer object
func (t *TransactionsRootContainer) SizeSSZ() (size int) {
	size = 4

	// Field (0) 'Transactions'
	for ii := 0; ii < len(t.Transactions); ii++ {
		size += 4
		size += len(t.Transactions[ii])
	}

	return
}

// HashTreeRoot ssz hashes the TransactionsRootContainer object
func (t *TransactionsRootContainer) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(t)
}

// HashTreeRootWith ssz hashes the TransactionsRootContainer object with a hasher
func (t *TransactionsRootContainer) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Transactions'
	{
		subIndx := hh.Index()
		num := uint64(len(t.Transactions))
		if num > 1048576 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range t.Transactions {
			{
				elemIndx := hh.Index()
				byteLen := uint64(len(elem))
				if byteLen > 1073741824 {
					err = ssz.ErrIncorrectListSize
					return
				}
				hh.AppendBytes32(elem)
				hh.MerkleizeWithMixin(elemIndx, byteLen, (1073741824+31)/32)
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 1048576)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the TransactionsRootContainer object
func (t *TransactionsRootContainer) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(t)
}

// MarshalSSZ ssz marshals the SignedBeaconBlockHeader object
func (s *SignedBeaconBlockHeader) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SignedBeaconBlockHeader object to a target array
func (s *SignedBeaconBlockHeader) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Header'
	if s.Header == nil {
		s.Header = new(BeaconBlockHeader)
	}
	if dst, err = s.Header.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Signature'
	if size := len(s.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("SignedBeaconBlockHeader.Signature", size, 96)
		return
	}
	dst = append(dst, s.Signature...)

	return
}

// UnmarshalSSZ ssz unmarshals the SignedBeaconBlockHeader object
func (s *SignedBeaconBlockHeader) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 208 {
		return ssz.ErrSize
	}

	// Field (0) 'Header'
	if s.Header == nil {
		s.Header = new(BeaconBlockHeader)
	}
	if err = s.Header.UnmarshalSSZ(buf[0:112]); err != nil {
		return err
	}

	// Field (1) 'Signature'
	if cap(s.Signature) == 0 {
		s.Signature = make([]byte, 0, len(buf[112:208]))
	}
	s.Signature = append(s.Signature, buf[112:208]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SignedBeaconBlockHeader object
func (s *SignedBeaconBlockHeader) SizeSSZ() (size int) {
	size = 208
	return
}

// HashTreeRoot ssz hashes the SignedBeaconBlockHeader object
func (s *SignedBeaconBlockHeader) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SignedBeaconBlockHeader object with a hasher
func (s *SignedBeaconBlockHeader) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Header'
	if s.Header == nil {
		s.Header = new(BeaconBlockHeader)
	}
	if err = s.Header.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Signature'
	if size := len(s.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("SignedBeaconBlockHeader.Signature", size, 96)
		return
	}
	hh.PutBytes(s.Signature)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SignedBeaconBlockHeader object
func (s *SignedBeaconBlockHeader) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the BeaconBlockHeader object
func (b *BeaconBlockHeader) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconBlockHeader object to a target array
func (b *BeaconBlockHeader) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Slot'
	dst = ssz.MarshalUint64(dst, b.Slot)

	// Field (1) 'ProposerIndex'
	dst = ssz.MarshalUint64(dst, b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.ParentRoot", size, 32)
		return
	}
	dst = append(dst, b.ParentRoot...)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.StateRoot", size, 32)
		return
	}
	dst = append(dst, b.StateRoot...)

	// Field (4) 'BodyRoot'
	if size := len(b.BodyRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.BodyRoot", size, 32)
		return
	}
	dst = append(dst, b.BodyRoot...)

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconBlockHeader object
func (b *BeaconBlockHeader) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 112 {
		return ssz.ErrSize
	}

	// Field (0) 'Slot'
	b.Slot = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'ProposerIndex'
	b.ProposerIndex = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'ParentRoot'
	if cap(b.ParentRoot) == 0 {
		b.ParentRoot = make([]byte, 0, len(buf[16:48]))
	}
	b.ParentRoot = append(b.ParentRoot, buf[16:48]...)

	// Field (3) 'StateRoot'
	if cap(b.StateRoot) == 0 {
		b.StateRoot = make([]byte, 0, len(buf[48:80]))
	}
	b.StateRoot = append(b.StateRoot, buf[48:80]...)

	// Field (4) 'BodyRoot'
	if cap(b.BodyRoot) == 0 {
		b.BodyRoot = make([]byte, 0, len(buf[80:112]))
	}
	b.BodyRoot = append(b.BodyRoot, buf[80:112]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconBlockHeader object
func (b *BeaconBlockHeader) SizeSSZ() (size int) {
	size = 112
	return
}

// HashTreeRoot ssz hashes the BeaconBlockHeader object
func (b *BeaconBlockHeader) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconBlockHeader object with a hasher
func (b *BeaconBlockHeader) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Slot'
	hh.PutUint64(b.Slot)

	// Field (1) 'ProposerIndex'
	hh.PutUint64(b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.ParentRoot", size, 32)
		return
	}
	hh.PutBytes(b.ParentRoot)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.StateRoot", size, 32)
		return
	}
	hh.PutBytes(b.StateRoot)

	// Field (4) 'BodyRoot'
	if size := len(b.BodyRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockHeader.BodyRoot", size, 32)
		return
	}
	hh.PutBytes(b.BodyRoot)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconBlockHeader object
func (b *BeaconBlockHeader) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the SyncCommittee object
func (s *SyncCommittee) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SyncCommittee object to a target array
func (s *SyncCommittee) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'PubKeys'
	if size := len(s.PubKeys); size != 512 {
		err = ssz.ErrVectorLengthFn("SyncCommittee.PubKeys", size, 512)
		return
	}
	for ii := 0; ii < 512; ii++ {
		if size := len(s.PubKeys[ii]); size != 48 {
			err = ssz.ErrBytesLengthFn("SyncCommittee.PubKeys[ii]", size, 48)
			return
		}
		dst = append(dst, s.PubKeys[ii]...)
	}

	// Field (1) 'AggregatePubKey'
	dst = append(dst, s.AggregatePubKey[:]...)

	return
}

// UnmarshalSSZ ssz unmarshals the SyncCommittee object
func (s *SyncCommittee) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 24624 {
		return ssz.ErrSize
	}

	// Field (0) 'PubKeys'
	s.PubKeys = make([][]byte, 512)
	for ii := 0; ii < 512; ii++ {
		if cap(s.PubKeys[ii]) == 0 {
			s.PubKeys[ii] = make([]byte, 0, len(buf[0:24576][ii*48:(ii+1)*48]))
		}
		s.PubKeys[ii] = append(s.PubKeys[ii], buf[0:24576][ii*48:(ii+1)*48]...)
	}

	// Field (1) 'AggregatePubKey'
	copy(s.AggregatePubKey[:], buf[24576:24624])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SyncCommittee object
func (s *SyncCommittee) SizeSSZ() (size int) {
	size = 24624
	return
}

// HashTreeRoot ssz hashes the SyncCommittee object
func (s *SyncCommittee) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SyncCommittee object with a hasher
func (s *SyncCommittee) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'PubKeys'
	{
		if size := len(s.PubKeys); size != 512 {
			err = ssz.ErrVectorLengthFn("SyncCommittee.PubKeys", size, 512)
			return
		}
		subIndx := hh.Index()
		for _, i := range s.PubKeys {
			if len(i) != 48 {
				err = ssz.ErrBytesLength
				return
			}
			hh.PutBytes(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (1) 'AggregatePubKey'
	hh.PutBytes(s.AggregatePubKey[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SyncCommittee object
func (s *SyncCommittee) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the SyncAggregateMainnet object
func (s *SyncAggregateMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SyncAggregateMainnet object to a target array
func (s *SyncAggregateMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'SyncCommitteeBits'
	if size := len(s.SyncCommitteeBits); size != 64 {
		err = ssz.ErrBytesLengthFn("SyncAggregateMainnet.SyncCommitteeBits", size, 64)
		return
	}
	dst = append(dst, s.SyncCommitteeBits...)

	// Field (1) 'SyncCommitteeSignature'
	dst = append(dst, s.SyncCommitteeSignature[:]...)

	return
}

// UnmarshalSSZ ssz unmarshals the SyncAggregateMainnet object
func (s *SyncAggregateMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 160 {
		return ssz.ErrSize
	}

	// Field (0) 'SyncCommitteeBits'
	if cap(s.SyncCommitteeBits) == 0 {
		s.SyncCommitteeBits = make([]byte, 0, len(buf[0:64]))
	}
	s.SyncCommitteeBits = append(s.SyncCommitteeBits, buf[0:64]...)

	// Field (1) 'SyncCommitteeSignature'
	copy(s.SyncCommitteeSignature[:], buf[64:160])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SyncAggregateMainnet object
func (s *SyncAggregateMainnet) SizeSSZ() (size int) {
	size = 160
	return
}

// HashTreeRoot ssz hashes the SyncAggregateMainnet object
func (s *SyncAggregateMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SyncAggregateMainnet object with a hasher
func (s *SyncAggregateMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'SyncCommitteeBits'
	if size := len(s.SyncCommitteeBits); size != 64 {
		err = ssz.ErrBytesLengthFn("SyncAggregateMainnet.SyncCommitteeBits", size, 64)
		return
	}
	hh.PutBytes(s.SyncCommitteeBits)

	// Field (1) 'SyncCommitteeSignature'
	hh.PutBytes(s.SyncCommitteeSignature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SyncAggregateMainnet object
func (s *SyncAggregateMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the BLSToExecutionChange object
func (b *BLSToExecutionChange) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BLSToExecutionChange object to a target array
func (b *BLSToExecutionChange) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'ValidatorIndex'
	dst = ssz.MarshalUint64(dst, b.ValidatorIndex)

	// Field (1) 'FromBlsPubkey'
	if size := len(b.FromBlsPubkey); size != 48 {
		err = ssz.ErrBytesLengthFn("BLSToExecutionChange.FromBlsPubkey", size, 48)
		return
	}
	dst = append(dst, b.FromBlsPubkey...)

	// Field (2) 'ToExecutionAddress'
	if size := len(b.ToExecutionAddress); size != 20 {
		err = ssz.ErrBytesLengthFn("BLSToExecutionChange.ToExecutionAddress", size, 20)
		return
	}
	dst = append(dst, b.ToExecutionAddress...)

	return
}

// UnmarshalSSZ ssz unmarshals the BLSToExecutionChange object
func (b *BLSToExecutionChange) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 76 {
		return ssz.ErrSize
	}

	// Field (0) 'ValidatorIndex'
	b.ValidatorIndex = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'FromBlsPubkey'
	if cap(b.FromBlsPubkey) == 0 {
		b.FromBlsPubkey = make([]byte, 0, len(buf[8:56]))
	}
	b.FromBlsPubkey = append(b.FromBlsPubkey, buf[8:56]...)

	// Field (2) 'ToExecutionAddress'
	if cap(b.ToExecutionAddress) == 0 {
		b.ToExecutionAddress = make([]byte, 0, len(buf[56:76]))
	}
	b.ToExecutionAddress = append(b.ToExecutionAddress, buf[56:76]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BLSToExecutionChange object
func (b *BLSToExecutionChange) SizeSSZ() (size int) {
	size = 76
	return
}

// HashTreeRoot ssz hashes the BLSToExecutionChange object
func (b *BLSToExecutionChange) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BLSToExecutionChange object with a hasher
func (b *BLSToExecutionChange) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'ValidatorIndex'
	hh.PutUint64(b.ValidatorIndex)

	// Field (1) 'FromBlsPubkey'
	if size := len(b.FromBlsPubkey); size != 48 {
		err = ssz.ErrBytesLengthFn("BLSToExecutionChange.FromBlsPubkey", size, 48)
		return
	}
	hh.PutBytes(b.FromBlsPubkey)

	// Field (2) 'ToExecutionAddress'
	if size := len(b.ToExecutionAddress); size != 20 {
		err = ssz.ErrBytesLengthFn("BLSToExecutionChange.ToExecutionAddress", size, 20)
		return
	}
	hh.PutBytes(b.ToExecutionAddress)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BLSToExecutionChange object
func (b *BLSToExecutionChange) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the SignedBLSToExecutionChange object
func (s *SignedBLSToExecutionChange) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SignedBLSToExecutionChange object to a target array
func (s *SignedBLSToExecutionChange) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Message'
	if s.Message == nil {
		s.Message = new(BLSToExecutionChange)
	}
	if dst, err = s.Message.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Signature'
	if size := len(s.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("SignedBLSToExecutionChange.Signature", size, 96)
		return
	}
	dst = append(dst, s.Signature...)

	return
}

// UnmarshalSSZ ssz unmarshals the SignedBLSToExecutionChange object
func (s *SignedBLSToExecutionChange) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 172 {
		return ssz.ErrSize
	}

	// Field (0) 'Message'
	if s.Message == nil {
		s.Message = new(BLSToExecutionChange)
	}
	if err = s.Message.UnmarshalSSZ(buf[0:76]); err != nil {
		return err
	}

	// Field (1) 'Signature'
	if cap(s.Signature) == 0 {
		s.Signature = make([]byte, 0, len(buf[76:172]))
	}
	s.Signature = append(s.Signature, buf[76:172]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SignedBLSToExecutionChange object
func (s *SignedBLSToExecutionChange) SizeSSZ() (size int) {
	size = 172
	return
}

// HashTreeRoot ssz hashes the SignedBLSToExecutionChange object
func (s *SignedBLSToExecutionChange) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SignedBLSToExecutionChange object with a hasher
func (s *SignedBLSToExecutionChange) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Message'
	if s.Message == nil {
		s.Message = new(BLSToExecutionChange)
	}
	if err = s.Message.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Signature'
	if size := len(s.Signature); size != 96 {
		err = ssz.ErrBytesLengthFn("SignedBLSToExecutionChange.Signature", size, 96)
		return
	}
	hh.PutBytes(s.Signature)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SignedBLSToExecutionChange object
func (s *SignedBLSToExecutionChange) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the HistoricalSummary object
func (h *HistoricalSummary) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(h)
}

// MarshalSSZTo ssz marshals the HistoricalSummary object to a target array
func (h *HistoricalSummary) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'BlockSummaryRoot'
	if size := len(h.BlockSummaryRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("HistoricalSummary.BlockSummaryRoot", size, 32)
		return
	}
	dst = append(dst, h.BlockSummaryRoot...)

	// Field (1) 'StateSummaryRoot'
	if size := len(h.StateSummaryRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("HistoricalSummary.StateSummaryRoot", size, 32)
		return
	}
	dst = append(dst, h.StateSummaryRoot...)

	return
}

// UnmarshalSSZ ssz unmarshals the HistoricalSummary object
func (h *HistoricalSummary) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 64 {
		return ssz.ErrSize
	}

	// Field (0) 'BlockSummaryRoot'
	if cap(h.BlockSummaryRoot) == 0 {
		h.BlockSummaryRoot = make([]byte, 0, len(buf[0:32]))
	}
	h.BlockSummaryRoot = append(h.BlockSummaryRoot, buf[0:32]...)

	// Field (1) 'StateSummaryRoot'
	if cap(h.StateSummaryRoot) == 0 {
		h.StateSummaryRoot = make([]byte, 0, len(buf[32:64]))
	}
	h.StateSummaryRoot = append(h.StateSummaryRoot, buf[32:64]...)

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the HistoricalSummary object
func (h *HistoricalSummary) SizeSSZ() (size int) {
	size = 64
	return
}

// HashTreeRoot ssz hashes the HistoricalSummary object
func (h *HistoricalSummary) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(h)
}

// HashTreeRootWith ssz hashes the HistoricalSummary object with a hasher
func (h *HistoricalSummary) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'BlockSummaryRoot'
	if size := len(h.BlockSummaryRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("HistoricalSummary.BlockSummaryRoot", size, 32)
		return
	}
	hh.PutBytes(h.BlockSummaryRoot)

	// Field (1) 'StateSummaryRoot'
	if size := len(h.StateSummaryRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("HistoricalSummary.StateSummaryRoot", size, 32)
		return
	}
	hh.PutBytes(h.StateSummaryRoot)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the HistoricalSummary object
func (h *HistoricalSummary) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(h)
}

// MarshalSSZ ssz marshals the Withdrawal object
func (w *Withdrawal) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(w)
}

// MarshalSSZTo ssz marshals the Withdrawal object to a target array
func (w *Withdrawal) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Index'
	dst = ssz.MarshalUint64(dst, w.Index)

	// Field (1) 'ValidatorIndex'
	dst = ssz.MarshalUint64(dst, w.ValidatorIndex)

	// Field (2) 'Address'
	dst = append(dst, w.Address[:]...)

	// Field (3) 'Amount'
	dst = ssz.MarshalUint64(dst, w.Amount)

	return
}

// UnmarshalSSZ ssz unmarshals the Withdrawal object
func (w *Withdrawal) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 44 {
		return ssz.ErrSize
	}

	// Field (0) 'Index'
	w.Index = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'ValidatorIndex'
	w.ValidatorIndex = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'Address'
	copy(w.Address[:], buf[16:36])

	// Field (3) 'Amount'
	w.Amount = ssz.UnmarshallUint64(buf[36:44])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the Withdrawal object
func (w *Withdrawal) SizeSSZ() (size int) {
	size = 44
	return
}

// HashTreeRoot ssz hashes the Withdrawal object
func (w *Withdrawal) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(w)
}

// HashTreeRootWith ssz hashes the Withdrawal object with a hasher
func (w *Withdrawal) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Index'
	hh.PutUint64(w.Index)

	// Field (1) 'ValidatorIndex'
	hh.PutUint64(w.ValidatorIndex)

	// Field (2) 'Address'
	hh.PutBytes(w.Address[:])

	// Field (3) 'Amount'
	hh.PutUint64(w.Amount)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the Withdrawal object
func (w *Withdrawal) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(w)
}

// MarshalSSZ ssz marshals the WithdrawalsRootContainerMainnet object
func (w *WithdrawalsRootContainerMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(w)
}

// MarshalSSZTo ssz marshals the WithdrawalsRootContainerMainnet object to a target array
func (w *WithdrawalsRootContainerMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(4)

	// Offset (0) 'Withdrawals'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(w.Withdrawals) * 44

	// Field (0) 'Withdrawals'
	if size := len(w.Withdrawals); size > 16 {
		err = ssz.ErrListTooBigFn("WithdrawalsRootContainerMainnet.Withdrawals", size, 16)
		return
	}
	for ii := 0; ii < len(w.Withdrawals); ii++ {
		if dst, err = w.Withdrawals[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	return
}

// UnmarshalSSZ ssz unmarshals the WithdrawalsRootContainerMainnet object
func (w *WithdrawalsRootContainerMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 4 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'Withdrawals'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 4 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (0) 'Withdrawals'
	{
		buf = tail[o0:]
		num, err := ssz.DivideInt2(len(buf), 44, 16)
		if err != nil {
			return err
		}
		w.Withdrawals = make([]*Withdrawal, num)
		for ii := 0; ii < num; ii++ {
			if w.Withdrawals[ii] == nil {
				w.Withdrawals[ii] = new(Withdrawal)
			}
			if err = w.Withdrawals[ii].UnmarshalSSZ(buf[ii*44 : (ii+1)*44]); err != nil {
				return err
			}
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the WithdrawalsRootContainerMainnet object
func (w *WithdrawalsRootContainerMainnet) SizeSSZ() (size int) {
	size = 4

	// Field (0) 'Withdrawals'
	size += len(w.Withdrawals) * 44

	return
}

// HashTreeRoot ssz hashes the WithdrawalsRootContainerMainnet object
func (w *WithdrawalsRootContainerMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(w)
}

// HashTreeRootWith ssz hashes the WithdrawalsRootContainerMainnet object with a hasher
func (w *WithdrawalsRootContainerMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Withdrawals'
	{
		subIndx := hh.Index()
		num := uint64(len(w.Withdrawals))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range w.Withdrawals {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the WithdrawalsRootContainerMainnet object
func (w *WithdrawalsRootContainerMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(w)
}
