// Code generated by fastssz. DO NOT EDIT.
// Hash: e80ede50b28a6c8990c5466d0a25a807e20ae9e7c1bd2bcfbe6d1f8df0150bc6
// Version: 0.1.3
package state

import (
	ssz "github.com/ferranbt/fastssz"
)

// MarshalSSZ ssz marshals the DepositRequest object
func (d *DepositRequest) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(d)
}

// MarshalSSZTo ssz marshals the DepositRequest object to a target array
func (d *DepositRequest) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Pubkey'
	dst = append(dst, d.Pubkey[:]...)

	// Field (1) 'WithdrawalCredentials'
	dst = append(dst, d.WithdrawalCredentials[:]...)

	// Field (2) 'Amount'
	dst = ssz.MarshalUint64(dst, d.Amount)

	// Field (3) 'Signature'
	dst = append(dst, d.Signature[:]...)

	// Field (4) 'Index'
	dst = ssz.MarshalUint64(dst, d.Index)

	return
}

// UnmarshalSSZ ssz unmarshals the DepositRequest object
func (d *DepositRequest) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 192 {
		return ssz.ErrSize
	}

	// Field (0) 'Pubkey'
	copy(d.Pubkey[:], buf[0:48])

	// Field (1) 'WithdrawalCredentials'
	copy(d.WithdrawalCredentials[:], buf[48:80])

	// Field (2) 'Amount'
	d.Amount = ssz.UnmarshallUint64(buf[80:88])

	// Field (3) 'Signature'
	copy(d.Signature[:], buf[88:184])

	// Field (4) 'Index'
	d.Index = ssz.UnmarshallUint64(buf[184:192])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the DepositRequest object
func (d *DepositRequest) SizeSSZ() (size int) {
	size = 192
	return
}

// HashTreeRoot ssz hashes the DepositRequest object
func (d *DepositRequest) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(d)
}

// HashTreeRootWith ssz hashes the DepositRequest object with a hasher
func (d *DepositRequest) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Pubkey'
	hh.PutBytes(d.Pubkey[:])

	// Field (1) 'WithdrawalCredentials'
	hh.PutBytes(d.WithdrawalCredentials[:])

	// Field (2) 'Amount'
	hh.PutUint64(d.Amount)

	// Field (3) 'Signature'
	hh.PutBytes(d.Signature[:])

	// Field (4) 'Index'
	hh.PutUint64(d.Index)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the DepositRequest object
func (d *DepositRequest) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(d)
}

// MarshalSSZ ssz marshals the PendingDeposit object
func (p *PendingDeposit) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(p)
}

// MarshalSSZTo ssz marshals the PendingDeposit object to a target array
func (p *PendingDeposit) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Pubkey'
	dst = append(dst, p.Pubkey[:]...)

	// Field (1) 'WithdrawalCredentials'
	dst = append(dst, p.WithdrawalCredentials[:]...)

	// Field (2) 'Amount'
	dst = ssz.MarshalUint64(dst, p.Amount)

	// Field (3) 'Signature'
	dst = append(dst, p.Signature[:]...)

	// Field (4) 'Index'
	dst = ssz.MarshalUint64(dst, p.Index)

	return
}

// UnmarshalSSZ ssz unmarshals the PendingDeposit object
func (p *PendingDeposit) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 192 {
		return ssz.ErrSize
	}

	// Field (0) 'Pubkey'
	copy(p.Pubkey[:], buf[0:48])

	// Field (1) 'WithdrawalCredentials'
	copy(p.WithdrawalCredentials[:], buf[48:80])

	// Field (2) 'Amount'
	p.Amount = ssz.UnmarshallUint64(buf[80:88])

	// Field (3) 'Signature'
	copy(p.Signature[:], buf[88:184])

	// Field (4) 'Index'
	p.Index = ssz.UnmarshallUint64(buf[184:192])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the PendingDeposit object
func (p *PendingDeposit) SizeSSZ() (size int) {
	size = 192
	return
}

// HashTreeRoot ssz hashes the PendingDeposit object
func (p *PendingDeposit) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(p)
}

// HashTreeRootWith ssz hashes the PendingDeposit object with a hasher
func (p *PendingDeposit) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Pubkey'
	hh.PutBytes(p.Pubkey[:])

	// Field (1) 'WithdrawalCredentials'
	hh.PutBytes(p.WithdrawalCredentials[:])

	// Field (2) 'Amount'
	hh.PutUint64(p.Amount)

	// Field (3) 'Signature'
	hh.PutBytes(p.Signature[:])

	// Field (4) 'Index'
	hh.PutUint64(p.Index)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the PendingDeposit object
func (p *PendingDeposit) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(p)
}

// MarshalSSZ ssz marshals the WithdrawalRequest object
func (w *WithdrawalRequest) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(w)
}

// MarshalSSZTo ssz marshals the WithdrawalRequest object to a target array
func (w *WithdrawalRequest) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'SourceAddress'
	dst = append(dst, w.SourceAddress[:]...)

	// Field (1) 'ValidatorPubkey'
	dst = append(dst, w.ValidatorPubkey[:]...)

	// Field (2) 'Amount'
	dst = ssz.MarshalUint64(dst, w.Amount)

	return
}

// UnmarshalSSZ ssz unmarshals the WithdrawalRequest object
func (w *WithdrawalRequest) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 76 {
		return ssz.ErrSize
	}

	// Field (0) 'SourceAddress'
	copy(w.SourceAddress[:], buf[0:20])

	// Field (1) 'ValidatorPubkey'
	copy(w.ValidatorPubkey[:], buf[20:68])

	// Field (2) 'Amount'
	w.Amount = ssz.UnmarshallUint64(buf[68:76])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the WithdrawalRequest object
func (w *WithdrawalRequest) SizeSSZ() (size int) {
	size = 76
	return
}

// HashTreeRoot ssz hashes the WithdrawalRequest object
func (w *WithdrawalRequest) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(w)
}

// HashTreeRootWith ssz hashes the WithdrawalRequest object with a hasher
func (w *WithdrawalRequest) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'SourceAddress'
	hh.PutBytes(w.SourceAddress[:])

	// Field (1) 'ValidatorPubkey'
	hh.PutBytes(w.ValidatorPubkey[:])

	// Field (2) 'Amount'
	hh.PutUint64(w.Amount)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the WithdrawalRequest object
func (w *WithdrawalRequest) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(w)
}

// MarshalSSZ ssz marshals the ConsolidationRequest object
func (c *ConsolidationRequest) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(c)
}

// MarshalSSZTo ssz marshals the ConsolidationRequest object to a target array
func (c *ConsolidationRequest) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'SourceAddress'
	dst = append(dst, c.SourceAddress[:]...)

	// Field (1) 'SourcePubkey'
	dst = append(dst, c.SourcePubkey[:]...)

	// Field (2) 'TargetPubkey'
	dst = append(dst, c.TargetPubkey[:]...)

	return
}

// UnmarshalSSZ ssz unmarshals the ConsolidationRequest object
func (c *ConsolidationRequest) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 116 {
		return ssz.ErrSize
	}

	// Field (0) 'SourceAddress'
	copy(c.SourceAddress[:], buf[0:20])

	// Field (1) 'SourcePubkey'
	copy(c.SourcePubkey[:], buf[20:68])

	// Field (2) 'TargetPubkey'
	copy(c.TargetPubkey[:], buf[68:116])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the ConsolidationRequest object
func (c *ConsolidationRequest) SizeSSZ() (size int) {
	size = 116
	return
}

// HashTreeRoot ssz hashes the ConsolidationRequest object
func (c *ConsolidationRequest) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(c)
}

// HashTreeRootWith ssz hashes the ConsolidationRequest object with a hasher
func (c *ConsolidationRequest) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'SourceAddress'
	hh.PutBytes(c.SourceAddress[:])

	// Field (1) 'SourcePubkey'
	hh.PutBytes(c.SourcePubkey[:])

	// Field (2) 'TargetPubkey'
	hh.PutBytes(c.TargetPubkey[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the ConsolidationRequest object
func (c *ConsolidationRequest) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(c)
}

// MarshalSSZ ssz marshals the SignedBeaconBlockElectra object
func (s *SignedBeaconBlockElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SignedBeaconBlockElectra object to a target array
func (s *SignedBeaconBlockElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(100)

	// Offset (0) 'Message'
	dst = ssz.WriteOffset(dst, offset)
	offset += s.Message.SizeSSZ()

	// Field (1) 'Signature'
	dst = append(dst, s.Signature[:]...)

	// Field (0) 'Message'
	if dst, err = s.Message.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the SignedBeaconBlockElectra object
func (s *SignedBeaconBlockElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 100 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'Message'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 100 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Signature'
	copy(s.Signature[:], buf[4:100])

	// Field (0) 'Message'
	{
		buf = tail[o0:]
		if err = s.Message.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SignedBeaconBlockElectra object
func (s *SignedBeaconBlockElectra) SizeSSZ() (size int) {
	size = 100

	// Field (0) 'Message'
	size += s.Message.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the SignedBeaconBlockElectra object
func (s *SignedBeaconBlockElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SignedBeaconBlockElectra object with a hasher
func (s *SignedBeaconBlockElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Message'
	if err = s.Message.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Signature'
	hh.PutBytes(s.Signature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SignedBeaconBlockElectra object
func (s *SignedBeaconBlockElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the BeaconBlockElectra object
func (b *BeaconBlockElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconBlockElectra object to a target array
func (b *BeaconBlockElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(84)

	// Field (0) 'Slot'
	dst = ssz.MarshalUint64(dst, b.Slot)

	// Field (1) 'ProposerIndex'
	dst = ssz.MarshalUint64(dst, b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockElectra.ParentRoot", size, 32)
		return
	}
	dst = append(dst, b.ParentRoot...)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockElectra.StateRoot", size, 32)
		return
	}
	dst = append(dst, b.StateRoot...)

	// Offset (4) 'Body'
	dst = ssz.WriteOffset(dst, offset)
	if b.Body == nil {
		b.Body = new(BeaconBlockBodyElectra)
	}
	offset += b.Body.SizeSSZ()

	// Field (4) 'Body'
	if dst, err = b.Body.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconBlockElectra object
func (b *BeaconBlockElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 84 {
		return ssz.ErrSize
	}

	tail := buf
	var o4 uint64

	// Field (0) 'Slot'
	b.Slot = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'ProposerIndex'
	b.ProposerIndex = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'ParentRoot'
	if cap(b.ParentRoot) == 0 {
		b.ParentRoot = make([]byte, 0, len(buf[16:48]))
	}
	b.ParentRoot = append(b.ParentRoot, buf[16:48]...)

	// Field (3) 'StateRoot'
	if cap(b.StateRoot) == 0 {
		b.StateRoot = make([]byte, 0, len(buf[48:80]))
	}
	b.StateRoot = append(b.StateRoot, buf[48:80]...)

	// Offset (4) 'Body'
	if o4 = ssz.ReadOffset(buf[80:84]); o4 > size {
		return ssz.ErrOffset
	}

	if o4 < 84 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (4) 'Body'
	{
		buf = tail[o4:]
		if b.Body == nil {
			b.Body = new(BeaconBlockBodyElectra)
		}
		if err = b.Body.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconBlockElectra object
func (b *BeaconBlockElectra) SizeSSZ() (size int) {
	size = 84

	// Field (4) 'Body'
	if b.Body == nil {
		b.Body = new(BeaconBlockBodyElectra)
	}
	size += b.Body.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the BeaconBlockElectra object
func (b *BeaconBlockElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconBlockElectra object with a hasher
func (b *BeaconBlockElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Slot'
	hh.PutUint64(b.Slot)

	// Field (1) 'ProposerIndex'
	hh.PutUint64(b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockElectra.ParentRoot", size, 32)
		return
	}
	hh.PutBytes(b.ParentRoot)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockElectra.StateRoot", size, 32)
		return
	}
	hh.PutBytes(b.StateRoot)

	// Field (4) 'Body'
	if err = b.Body.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconBlockElectra object
func (b *BeaconBlockElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the BeaconBlockBodyElectra object
func (b *BeaconBlockBodyElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconBlockBodyElectra object to a target array
func (b *BeaconBlockBodyElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(396)

	// Field (0) 'RandaoReveal'
	if size := len(b.RandaoReveal); size != 96 {
		err = ssz.ErrBytesLengthFn("BeaconBlockBodyElectra.RandaoReveal", size, 96)
		return
	}
	dst = append(dst, b.RandaoReveal...)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if dst, err = b.Eth1Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Graffiti'
	dst = append(dst, b.Graffiti[:]...)

	// Offset (3) 'ProposerSlashings'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.ProposerSlashings) * 416

	// Offset (4) 'AttesterSlashings'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		offset += 4
		offset += b.AttesterSlashings[ii].SizeSSZ()
	}

	// Offset (5) 'Attestations'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(b.Attestations); ii++ {
		offset += 4
		offset += b.Attestations[ii].SizeSSZ()
	}

	// Offset (6) 'Deposits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Deposits) * 1240

	// Offset (7) 'VoluntaryExits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.VoluntaryExits) * 112

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if dst, err = b.SyncAggregate.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (9) 'ExecutionPayload'
	dst = ssz.WriteOffset(dst, offset)
	if b.ExecutionPayload == nil {
		b.ExecutionPayload = new(ExecutionPayloadDeneb)
	}
	offset += b.ExecutionPayload.SizeSSZ()

	// Offset (10) 'BlsToExecutionChanges'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.BlsToExecutionChanges) * 172

	// Offset (11) 'BlobKzgCommitments'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.BlobKzgCommitments) * 48

	// Offset (12) 'ExecutionRequests'
	dst = ssz.WriteOffset(dst, offset)
	if b.ExecutionRequests == nil {
		b.ExecutionRequests = new(ExecutionRequests)
	}
	offset += b.ExecutionRequests.SizeSSZ()

	// Field (3) 'ProposerSlashings'
	if size := len(b.ProposerSlashings); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.ProposerSlashings", size, 16)
		return
	}
	for ii := 0; ii < len(b.ProposerSlashings); ii++ {
		if dst, err = b.ProposerSlashings[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (4) 'AttesterSlashings'
	if size := len(b.AttesterSlashings); size > 1 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.AttesterSlashings", size, 1)
		return
	}
	{
		offset = 4 * len(b.AttesterSlashings)
		for ii := 0; ii < len(b.AttesterSlashings); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += b.AttesterSlashings[ii].SizeSSZ()
		}
	}
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		if dst, err = b.AttesterSlashings[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (5) 'Attestations'
	if size := len(b.Attestations); size > 8 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.Attestations", size, 8)
		return
	}
	{
		offset = 4 * len(b.Attestations)
		for ii := 0; ii < len(b.Attestations); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += b.Attestations[ii].SizeSSZ()
		}
	}
	for ii := 0; ii < len(b.Attestations); ii++ {
		if dst, err = b.Attestations[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (6) 'Deposits'
	if size := len(b.Deposits); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.Deposits", size, 16)
		return
	}
	for ii := 0; ii < len(b.Deposits); ii++ {
		if dst, err = b.Deposits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (7) 'VoluntaryExits'
	if size := len(b.VoluntaryExits); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.VoluntaryExits", size, 16)
		return
	}
	for ii := 0; ii < len(b.VoluntaryExits); ii++ {
		if dst, err = b.VoluntaryExits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (9) 'ExecutionPayload'
	if dst, err = b.ExecutionPayload.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (10) 'BlsToExecutionChanges'
	if size := len(b.BlsToExecutionChanges); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.BlsToExecutionChanges", size, 16)
		return
	}
	for ii := 0; ii < len(b.BlsToExecutionChanges); ii++ {
		if dst, err = b.BlsToExecutionChanges[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (11) 'BlobKzgCommitments'
	if size := len(b.BlobKzgCommitments); size > 4096 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.BlobKzgCommitments", size, 4096)
		return
	}
	for ii := 0; ii < len(b.BlobKzgCommitments); ii++ {
		dst = append(dst, b.BlobKzgCommitments[ii][:]...)
	}

	// Field (12) 'ExecutionRequests'
	if dst, err = b.ExecutionRequests.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconBlockBodyElectra object
func (b *BeaconBlockBodyElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 396 {
		return ssz.ErrSize
	}

	tail := buf
	var o3, o4, o5, o6, o7, o9, o10, o11, o12 uint64

	// Field (0) 'RandaoReveal'
	if cap(b.RandaoReveal) == 0 {
		b.RandaoReveal = make([]byte, 0, len(buf[0:96]))
	}
	b.RandaoReveal = append(b.RandaoReveal, buf[0:96]...)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.UnmarshalSSZ(buf[96:168]); err != nil {
		return err
	}

	// Field (2) 'Graffiti'
	copy(b.Graffiti[:], buf[168:200])

	// Offset (3) 'ProposerSlashings'
	if o3 = ssz.ReadOffset(buf[200:204]); o3 > size {
		return ssz.ErrOffset
	}

	if o3 < 396 {
		return ssz.ErrInvalidVariableOffset
	}

	// Offset (4) 'AttesterSlashings'
	if o4 = ssz.ReadOffset(buf[204:208]); o4 > size || o3 > o4 {
		return ssz.ErrOffset
	}

	// Offset (5) 'Attestations'
	if o5 = ssz.ReadOffset(buf[208:212]); o5 > size || o4 > o5 {
		return ssz.ErrOffset
	}

	// Offset (6) 'Deposits'
	if o6 = ssz.ReadOffset(buf[212:216]); o6 > size || o5 > o6 {
		return ssz.ErrOffset
	}

	// Offset (7) 'VoluntaryExits'
	if o7 = ssz.ReadOffset(buf[216:220]); o7 > size || o6 > o7 {
		return ssz.ErrOffset
	}

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if err = b.SyncAggregate.UnmarshalSSZ(buf[220:380]); err != nil {
		return err
	}

	// Offset (9) 'ExecutionPayload'
	if o9 = ssz.ReadOffset(buf[380:384]); o9 > size || o7 > o9 {
		return ssz.ErrOffset
	}

	// Offset (10) 'BlsToExecutionChanges'
	if o10 = ssz.ReadOffset(buf[384:388]); o10 > size || o9 > o10 {
		return ssz.ErrOffset
	}

	// Offset (11) 'BlobKzgCommitments'
	if o11 = ssz.ReadOffset(buf[388:392]); o11 > size || o10 > o11 {
		return ssz.ErrOffset
	}

	// Offset (12) 'ExecutionRequests'
	if o12 = ssz.ReadOffset(buf[392:396]); o12 > size || o11 > o12 {
		return ssz.ErrOffset
	}

	// Field (3) 'ProposerSlashings'
	{
		buf = tail[o3:o4]
		num, err := ssz.DivideInt2(len(buf), 416, 16)
		if err != nil {
			return err
		}
		b.ProposerSlashings = make([]*ProposerSlashing, num)
		for ii := 0; ii < num; ii++ {
			if b.ProposerSlashings[ii] == nil {
				b.ProposerSlashings[ii] = new(ProposerSlashing)
			}
			if err = b.ProposerSlashings[ii].UnmarshalSSZ(buf[ii*416 : (ii+1)*416]); err != nil {
				return err
			}
		}
	}

	// Field (4) 'AttesterSlashings'
	{
		buf = tail[o4:o5]
		num, err := ssz.DecodeDynamicLength(buf, 1)
		if err != nil {
			return err
		}
		b.AttesterSlashings = make([]*AttesterSlashingElectra, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if b.AttesterSlashings[indx] == nil {
				b.AttesterSlashings[indx] = new(AttesterSlashingElectra)
			}
			if err = b.AttesterSlashings[indx].UnmarshalSSZ(buf); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	// Field (5) 'Attestations'
	{
		buf = tail[o5:o6]
		num, err := ssz.DecodeDynamicLength(buf, 8)
		if err != nil {
			return err
		}
		b.Attestations = make([]*AttestationElectra, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if b.Attestations[indx] == nil {
				b.Attestations[indx] = new(AttestationElectra)
			}
			if err = b.Attestations[indx].UnmarshalSSZ(buf); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	// Field (6) 'Deposits'
	{
		buf = tail[o6:o7]
		num, err := ssz.DivideInt2(len(buf), 1240, 16)
		if err != nil {
			return err
		}
		b.Deposits = make([]*Deposit, num)
		for ii := 0; ii < num; ii++ {
			if b.Deposits[ii] == nil {
				b.Deposits[ii] = new(Deposit)
			}
			if err = b.Deposits[ii].UnmarshalSSZ(buf[ii*1240 : (ii+1)*1240]); err != nil {
				return err
			}
		}
	}

	// Field (7) 'VoluntaryExits'
	{
		buf = tail[o7:o9]
		num, err := ssz.DivideInt2(len(buf), 112, 16)
		if err != nil {
			return err
		}
		b.VoluntaryExits = make([]*SignedVoluntaryExit, num)
		for ii := 0; ii < num; ii++ {
			if b.VoluntaryExits[ii] == nil {
				b.VoluntaryExits[ii] = new(SignedVoluntaryExit)
			}
			if err = b.VoluntaryExits[ii].UnmarshalSSZ(buf[ii*112 : (ii+1)*112]); err != nil {
				return err
			}
		}
	}

	// Field (9) 'ExecutionPayload'
	{
		buf = tail[o9:o10]
		if b.ExecutionPayload == nil {
			b.ExecutionPayload = new(ExecutionPayloadDeneb)
		}
		if err = b.ExecutionPayload.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (10) 'BlsToExecutionChanges'
	{
		buf = tail[o10:o11]
		num, err := ssz.DivideInt2(len(buf), 172, 16)
		if err != nil {
			return err
		}
		b.BlsToExecutionChanges = make([]*SignedBLSToExecutionChange, num)
		for ii := 0; ii < num; ii++ {
			if b.BlsToExecutionChanges[ii] == nil {
				b.BlsToExecutionChanges[ii] = new(SignedBLSToExecutionChange)
			}
			if err = b.BlsToExecutionChanges[ii].UnmarshalSSZ(buf[ii*172 : (ii+1)*172]); err != nil {
				return err
			}
		}
	}

	// Field (11) 'BlobKzgCommitments'
	{
		buf = tail[o11:o12]
		num, err := ssz.DivideInt2(len(buf), 48, 4096)
		if err != nil {
			return err
		}
		b.BlobKzgCommitments = make([][48]byte, num)
		for ii := 0; ii < num; ii++ {
			copy(b.BlobKzgCommitments[ii][:], buf[ii*48:(ii+1)*48])
		}
	}

	// Field (12) 'ExecutionRequests'
	{
		buf = tail[o12:]
		if b.ExecutionRequests == nil {
			b.ExecutionRequests = new(ExecutionRequests)
		}
		if err = b.ExecutionRequests.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconBlockBodyElectra object
func (b *BeaconBlockBodyElectra) SizeSSZ() (size int) {
	size = 396

	// Field (3) 'ProposerSlashings'
	size += len(b.ProposerSlashings) * 416

	// Field (4) 'AttesterSlashings'
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		size += 4
		size += b.AttesterSlashings[ii].SizeSSZ()
	}

	// Field (5) 'Attestations'
	for ii := 0; ii < len(b.Attestations); ii++ {
		size += 4
		size += b.Attestations[ii].SizeSSZ()
	}

	// Field (6) 'Deposits'
	size += len(b.Deposits) * 1240

	// Field (7) 'VoluntaryExits'
	size += len(b.VoluntaryExits) * 112

	// Field (9) 'ExecutionPayload'
	if b.ExecutionPayload == nil {
		b.ExecutionPayload = new(ExecutionPayloadDeneb)
	}
	size += b.ExecutionPayload.SizeSSZ()

	// Field (10) 'BlsToExecutionChanges'
	size += len(b.BlsToExecutionChanges) * 172

	// Field (11) 'BlobKzgCommitments'
	size += len(b.BlobKzgCommitments) * 48

	// Field (12) 'ExecutionRequests'
	if b.ExecutionRequests == nil {
		b.ExecutionRequests = new(ExecutionRequests)
	}
	size += b.ExecutionRequests.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the BeaconBlockBodyElectra object
func (b *BeaconBlockBodyElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconBlockBodyElectra object with a hasher
func (b *BeaconBlockBodyElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'RandaoReveal'
	if size := len(b.RandaoReveal); size != 96 {
		err = ssz.ErrBytesLengthFn("BeaconBlockBodyElectra.RandaoReveal", size, 96)
		return
	}
	hh.PutBytes(b.RandaoReveal)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Graffiti'
	hh.PutBytes(b.Graffiti[:])

	// Field (3) 'ProposerSlashings'
	{
		subIndx := hh.Index()
		num := uint64(len(b.ProposerSlashings))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.ProposerSlashings {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (4) 'AttesterSlashings'
	{
		subIndx := hh.Index()
		num := uint64(len(b.AttesterSlashings))
		if num > 1 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.AttesterSlashings {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 1)
	}

	// Field (5) 'Attestations'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Attestations))
		if num > 8 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Attestations {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 8)
	}

	// Field (6) 'Deposits'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Deposits))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Deposits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (7) 'VoluntaryExits'
	{
		subIndx := hh.Index()
		num := uint64(len(b.VoluntaryExits))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.VoluntaryExits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if err = b.SyncAggregate.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (9) 'ExecutionPayload'
	if err = b.ExecutionPayload.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (10) 'BlsToExecutionChanges'
	{
		subIndx := hh.Index()
		num := uint64(len(b.BlsToExecutionChanges))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.BlsToExecutionChanges {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (11) 'BlobKzgCommitments'
	{
		if size := len(b.BlobKzgCommitments); size > 4096 {
			err = ssz.ErrListTooBigFn("BeaconBlockBodyElectra.BlobKzgCommitments", size, 4096)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.BlobKzgCommitments {
			hh.PutBytes(i[:])
		}
		numItems := uint64(len(b.BlobKzgCommitments))
		hh.MerkleizeWithMixin(subIndx, numItems, 4096)
	}

	// Field (12) 'ExecutionRequests'
	if err = b.ExecutionRequests.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconBlockBodyElectra object
func (b *BeaconBlockBodyElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the BeaconStateElectra object
func (b *BeaconStateElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconStateElectra object to a target array
func (b *BeaconStateElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(2736713)

	// Field (0) 'GenesisTime'
	dst = ssz.MarshalUint64(dst, b.GenesisTime)

	// Field (1) 'GenesisValidatorsRoot'
	if size := len(b.GenesisValidatorsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.GenesisValidatorsRoot", size, 32)
		return
	}
	dst = append(dst, b.GenesisValidatorsRoot...)

	// Field (2) 'Slot'
	dst = ssz.MarshalUint64(dst, b.Slot)

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if dst, err = b.Fork.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if dst, err = b.LatestBlockHeader.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (5) 'BlockRoots'
	if size := len(b.BlockRoots); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateElectra.BlockRoots", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		if size := len(b.BlockRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateElectra.BlockRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.BlockRoots[ii]...)
	}

	// Field (6) 'StateRoots'
	if size := len(b.StateRoots); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateElectra.StateRoots", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		if size := len(b.StateRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateElectra.StateRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.StateRoots[ii]...)
	}

	// Offset (7) 'HistoricalRoots'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.HistoricalRoots) * 32

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if dst, err = b.Eth1Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (9) 'Eth1DataVotes'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Eth1DataVotes) * 72

	// Field (10) 'Eth1DepositIndex'
	dst = ssz.MarshalUint64(dst, b.Eth1DepositIndex)

	// Offset (11) 'Validators'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Validators) * 121

	// Offset (12) 'Balances'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Balances) * 8

	// Field (13) 'RandaoMixes'
	if size := len(b.RandaoMixes); size != 65536 {
		err = ssz.ErrVectorLengthFn("BeaconStateElectra.RandaoMixes", size, 65536)
		return
	}
	for ii := 0; ii < 65536; ii++ {
		if size := len(b.RandaoMixes[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateElectra.RandaoMixes[ii]", size, 32)
			return
		}
		dst = append(dst, b.RandaoMixes[ii]...)
	}

	// Field (14) 'Slashings'
	if size := len(b.Slashings); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateElectra.Slashings", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		dst = ssz.MarshalUint64(dst, b.Slashings[ii])
	}

	// Offset (15) 'PreviousEpochParticipation'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.PreviousEpochParticipation)

	// Offset (16) 'CurrentEpochParticipation'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.CurrentEpochParticipation)

	// Field (17) 'JustificationBits'
	if size := len(b.JustificationBits); size != 1 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.JustificationBits", size, 1)
		return
	}
	dst = append(dst, b.JustificationBits...)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.PreviousJustifiedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.CurrentJustifiedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.FinalizedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (21) 'InactivityScores'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.InactivityScores) * 8

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if dst, err = b.CurrentSyncCommittee.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if dst, err = b.NextSyncCommittee.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (24) 'LatestExecutionPayloadHeader'
	dst = ssz.WriteOffset(dst, offset)
	if b.LatestExecutionPayloadHeader == nil {
		b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
	}
	offset += b.LatestExecutionPayloadHeader.SizeSSZ()

	// Field (25) 'NextWithdrawalIndex'
	dst = ssz.MarshalUint64(dst, b.NextWithdrawalIndex)

	// Field (26) 'NextWithdrawalValidatorIndex'
	dst = ssz.MarshalUint64(dst, b.NextWithdrawalValidatorIndex)

	// Offset (27) 'HistoricalSummaries'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.HistoricalSummaries) * 64

	// Field (28) 'DepositRequestsStartIndex'
	dst = ssz.MarshalUint64(dst, b.DepositRequestsStartIndex)

	// Field (29) 'DepositBalanceToConsume'
	dst = ssz.MarshalUint64(dst, b.DepositBalanceToConsume)

	// Field (30) 'ExitBalanceToConsume'
	dst = ssz.MarshalUint64(dst, b.ExitBalanceToConsume)

	// Field (31) 'EarliestExitEpoch'
	dst = ssz.MarshalUint64(dst, b.EarliestExitEpoch)

	// Field (32) 'ConsolidationBalanceToConsume'
	dst = ssz.MarshalUint64(dst, b.ConsolidationBalanceToConsume)

	// Field (33) 'EarliestConsolidationEpoch'
	dst = ssz.MarshalUint64(dst, b.EarliestConsolidationEpoch)

	// Offset (34) 'PendingDeposits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.PendingDeposits) * 192

	// Offset (35) 'PendingPartialWithdrawals'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.PendingPartialWithdrawals) * 24

	// Offset (36) 'PendingConsolidations'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.PendingConsolidations) * 16

	// Field (7) 'HistoricalRoots'
	if size := len(b.HistoricalRoots); size > 16777216 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.HistoricalRoots", size, 16777216)
		return
	}
	for ii := 0; ii < len(b.HistoricalRoots); ii++ {
		if size := len(b.HistoricalRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateElectra.HistoricalRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.HistoricalRoots[ii]...)
	}

	// Field (9) 'Eth1DataVotes'
	if size := len(b.Eth1DataVotes); size > 2048 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.Eth1DataVotes", size, 2048)
		return
	}
	for ii := 0; ii < len(b.Eth1DataVotes); ii++ {
		if dst, err = b.Eth1DataVotes[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (11) 'Validators'
	if size := len(b.Validators); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.Validators", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.Validators); ii++ {
		if dst, err = b.Validators[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (12) 'Balances'
	if size := len(b.Balances); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.Balances", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.Balances); ii++ {
		dst = ssz.MarshalUint64(dst, b.Balances[ii])
	}

	// Field (15) 'PreviousEpochParticipation'
	if size := len(b.PreviousEpochParticipation); size > 1099511627776 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.PreviousEpochParticipation", size, 1099511627776)
		return
	}
	dst = append(dst, b.PreviousEpochParticipation...)

	// Field (16) 'CurrentEpochParticipation'
	if size := len(b.CurrentEpochParticipation); size > 1099511627776 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.CurrentEpochParticipation", size, 1099511627776)
		return
	}
	dst = append(dst, b.CurrentEpochParticipation...)

	// Field (21) 'InactivityScores'
	if size := len(b.InactivityScores); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.InactivityScores", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.InactivityScores); ii++ {
		dst = ssz.MarshalUint64(dst, b.InactivityScores[ii])
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	if dst, err = b.LatestExecutionPayloadHeader.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (27) 'HistoricalSummaries'
	if size := len(b.HistoricalSummaries); size > 16777216 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.HistoricalSummaries", size, 16777216)
		return
	}
	for ii := 0; ii < len(b.HistoricalSummaries); ii++ {
		if dst, err = b.HistoricalSummaries[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (34) 'PendingDeposits'
	if size := len(b.PendingDeposits); size > 134217728 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.PendingDeposits", size, 134217728)
		return
	}
	for ii := 0; ii < len(b.PendingDeposits); ii++ {
		if dst, err = b.PendingDeposits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (35) 'PendingPartialWithdrawals'
	if size := len(b.PendingPartialWithdrawals); size > 134217728 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.PendingPartialWithdrawals", size, 134217728)
		return
	}
	for ii := 0; ii < len(b.PendingPartialWithdrawals); ii++ {
		if dst, err = b.PendingPartialWithdrawals[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (36) 'PendingConsolidations'
	if size := len(b.PendingConsolidations); size > 262144 {
		err = ssz.ErrListTooBigFn("BeaconStateElectra.PendingConsolidations", size, 262144)
		return
	}
	for ii := 0; ii < len(b.PendingConsolidations); ii++ {
		if dst, err = b.PendingConsolidations[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconStateElectra object
func (b *BeaconStateElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 2736713 {
		return ssz.ErrSize
	}

	tail := buf
	var o7, o9, o11, o12, o15, o16, o21, o24, o27, o34, o35, o36 uint64

	// Field (0) 'GenesisTime'
	b.GenesisTime = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'GenesisValidatorsRoot'
	if cap(b.GenesisValidatorsRoot) == 0 {
		b.GenesisValidatorsRoot = make([]byte, 0, len(buf[8:40]))
	}
	b.GenesisValidatorsRoot = append(b.GenesisValidatorsRoot, buf[8:40]...)

	// Field (2) 'Slot'
	b.Slot = ssz.UnmarshallUint64(buf[40:48])

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if err = b.Fork.UnmarshalSSZ(buf[48:64]); err != nil {
		return err
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if err = b.LatestBlockHeader.UnmarshalSSZ(buf[64:176]); err != nil {
		return err
	}

	// Field (5) 'BlockRoots'
	b.BlockRoots = make([][]byte, 8192)
	for ii := 0; ii < 8192; ii++ {
		if cap(b.BlockRoots[ii]) == 0 {
			b.BlockRoots[ii] = make([]byte, 0, len(buf[176:262320][ii*32:(ii+1)*32]))
		}
		b.BlockRoots[ii] = append(b.BlockRoots[ii], buf[176:262320][ii*32:(ii+1)*32]...)
	}

	// Field (6) 'StateRoots'
	b.StateRoots = make([][]byte, 8192)
	for ii := 0; ii < 8192; ii++ {
		if cap(b.StateRoots[ii]) == 0 {
			b.StateRoots[ii] = make([]byte, 0, len(buf[262320:524464][ii*32:(ii+1)*32]))
		}
		b.StateRoots[ii] = append(b.StateRoots[ii], buf[262320:524464][ii*32:(ii+1)*32]...)
	}

	// Offset (7) 'HistoricalRoots'
	if o7 = ssz.ReadOffset(buf[524464:524468]); o7 > size {
		return ssz.ErrOffset
	}

	if o7 < 2736713 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.UnmarshalSSZ(buf[524468:524540]); err != nil {
		return err
	}

	// Offset (9) 'Eth1DataVotes'
	if o9 = ssz.ReadOffset(buf[524540:524544]); o9 > size || o7 > o9 {
		return ssz.ErrOffset
	}

	// Field (10) 'Eth1DepositIndex'
	b.Eth1DepositIndex = ssz.UnmarshallUint64(buf[524544:524552])

	// Offset (11) 'Validators'
	if o11 = ssz.ReadOffset(buf[524552:524556]); o11 > size || o9 > o11 {
		return ssz.ErrOffset
	}

	// Offset (12) 'Balances'
	if o12 = ssz.ReadOffset(buf[524556:524560]); o12 > size || o11 > o12 {
		return ssz.ErrOffset
	}

	// Field (13) 'RandaoMixes'
	b.RandaoMixes = make([][]byte, 65536)
	for ii := 0; ii < 65536; ii++ {
		if cap(b.RandaoMixes[ii]) == 0 {
			b.RandaoMixes[ii] = make([]byte, 0, len(buf[524560:2621712][ii*32:(ii+1)*32]))
		}
		b.RandaoMixes[ii] = append(b.RandaoMixes[ii], buf[524560:2621712][ii*32:(ii+1)*32]...)
	}

	// Field (14) 'Slashings'
	b.Slashings = ssz.ExtendUint64(b.Slashings, 8192)
	for ii := 0; ii < 8192; ii++ {
		b.Slashings[ii] = ssz.UnmarshallUint64(buf[2621712:2687248][ii*8 : (ii+1)*8])
	}

	// Offset (15) 'PreviousEpochParticipation'
	if o15 = ssz.ReadOffset(buf[2687248:2687252]); o15 > size || o12 > o15 {
		return ssz.ErrOffset
	}

	// Offset (16) 'CurrentEpochParticipation'
	if o16 = ssz.ReadOffset(buf[2687252:2687256]); o16 > size || o15 > o16 {
		return ssz.ErrOffset
	}

	// Field (17) 'JustificationBits'
	if cap(b.JustificationBits) == 0 {
		b.JustificationBits = make([]byte, 0, len(buf[2687256:2687257]))
	}
	b.JustificationBits = append(b.JustificationBits, buf[2687256:2687257]...)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.PreviousJustifiedCheckpoint.UnmarshalSSZ(buf[2687257:2687297]); err != nil {
		return err
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.CurrentJustifiedCheckpoint.UnmarshalSSZ(buf[2687297:2687337]); err != nil {
		return err
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if err = b.FinalizedCheckpoint.UnmarshalSSZ(buf[2687337:2687377]); err != nil {
		return err
	}

	// Offset (21) 'InactivityScores'
	if o21 = ssz.ReadOffset(buf[2687377:2687381]); o21 > size || o16 > o21 {
		return ssz.ErrOffset
	}

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if err = b.CurrentSyncCommittee.UnmarshalSSZ(buf[2687381:2712005]); err != nil {
		return err
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if err = b.NextSyncCommittee.UnmarshalSSZ(buf[2712005:2736629]); err != nil {
		return err
	}

	// Offset (24) 'LatestExecutionPayloadHeader'
	if o24 = ssz.ReadOffset(buf[2736629:2736633]); o24 > size || o21 > o24 {
		return ssz.ErrOffset
	}

	// Field (25) 'NextWithdrawalIndex'
	b.NextWithdrawalIndex = ssz.UnmarshallUint64(buf[2736633:2736641])

	// Field (26) 'NextWithdrawalValidatorIndex'
	b.NextWithdrawalValidatorIndex = ssz.UnmarshallUint64(buf[2736641:2736649])

	// Offset (27) 'HistoricalSummaries'
	if o27 = ssz.ReadOffset(buf[2736649:2736653]); o27 > size || o24 > o27 {
		return ssz.ErrOffset
	}

	// Field (28) 'DepositRequestsStartIndex'
	b.DepositRequestsStartIndex = ssz.UnmarshallUint64(buf[2736653:2736661])

	// Field (29) 'DepositBalanceToConsume'
	b.DepositBalanceToConsume = ssz.UnmarshallUint64(buf[2736661:2736669])

	// Field (30) 'ExitBalanceToConsume'
	b.ExitBalanceToConsume = ssz.UnmarshallUint64(buf[2736669:2736677])

	// Field (31) 'EarliestExitEpoch'
	b.EarliestExitEpoch = ssz.UnmarshallUint64(buf[2736677:2736685])

	// Field (32) 'ConsolidationBalanceToConsume'
	b.ConsolidationBalanceToConsume = ssz.UnmarshallUint64(buf[2736685:2736693])

	// Field (33) 'EarliestConsolidationEpoch'
	b.EarliestConsolidationEpoch = ssz.UnmarshallUint64(buf[2736693:2736701])

	// Offset (34) 'PendingDeposits'
	if o34 = ssz.ReadOffset(buf[2736701:2736705]); o34 > size || o27 > o34 {
		return ssz.ErrOffset
	}

	// Offset (35) 'PendingPartialWithdrawals'
	if o35 = ssz.ReadOffset(buf[2736705:2736709]); o35 > size || o34 > o35 {
		return ssz.ErrOffset
	}

	// Offset (36) 'PendingConsolidations'
	if o36 = ssz.ReadOffset(buf[2736709:2736713]); o36 > size || o35 > o36 {
		return ssz.ErrOffset
	}

	// Field (7) 'HistoricalRoots'
	{
		buf = tail[o7:o9]
		num, err := ssz.DivideInt2(len(buf), 32, 16777216)
		if err != nil {
			return err
		}
		b.HistoricalRoots = make([][]byte, num)
		for ii := 0; ii < num; ii++ {
			if cap(b.HistoricalRoots[ii]) == 0 {
				b.HistoricalRoots[ii] = make([]byte, 0, len(buf[ii*32:(ii+1)*32]))
			}
			b.HistoricalRoots[ii] = append(b.HistoricalRoots[ii], buf[ii*32:(ii+1)*32]...)
		}
	}

	// Field (9) 'Eth1DataVotes'
	{
		buf = tail[o9:o11]
		num, err := ssz.DivideInt2(len(buf), 72, 2048)
		if err != nil {
			return err
		}
		b.Eth1DataVotes = make([]*Eth1Data, num)
		for ii := 0; ii < num; ii++ {
			if b.Eth1DataVotes[ii] == nil {
				b.Eth1DataVotes[ii] = new(Eth1Data)
			}
			if err = b.Eth1DataVotes[ii].UnmarshalSSZ(buf[ii*72 : (ii+1)*72]); err != nil {
				return err
			}
		}
	}

	// Field (11) 'Validators'
	{
		buf = tail[o11:o12]
		num, err := ssz.DivideInt2(len(buf), 121, 1099511627776)
		if err != nil {
			return err
		}
		b.Validators = make([]*Validator, num)
		for ii := 0; ii < num; ii++ {
			if b.Validators[ii] == nil {
				b.Validators[ii] = new(Validator)
			}
			if err = b.Validators[ii].UnmarshalSSZ(buf[ii*121 : (ii+1)*121]); err != nil {
				return err
			}
		}
	}

	// Field (12) 'Balances'
	{
		buf = tail[o12:o15]
		num, err := ssz.DivideInt2(len(buf), 8, 1099511627776)
		if err != nil {
			return err
		}
		b.Balances = ssz.ExtendUint64(b.Balances, num)
		for ii := 0; ii < num; ii++ {
			b.Balances[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}

	// Field (15) 'PreviousEpochParticipation'
	{
		buf = tail[o15:o16]
		if len(buf) > 1099511627776 {
			return ssz.ErrBytesLength
		}
		if cap(b.PreviousEpochParticipation) == 0 {
			b.PreviousEpochParticipation = make([]byte, 0, len(buf))
		}
		b.PreviousEpochParticipation = append(b.PreviousEpochParticipation, buf...)
	}

	// Field (16) 'CurrentEpochParticipation'
	{
		buf = tail[o16:o21]
		if len(buf) > 1099511627776 {
			return ssz.ErrBytesLength
		}
		if cap(b.CurrentEpochParticipation) == 0 {
			b.CurrentEpochParticipation = make([]byte, 0, len(buf))
		}
		b.CurrentEpochParticipation = append(b.CurrentEpochParticipation, buf...)
	}

	// Field (21) 'InactivityScores'
	{
		buf = tail[o21:o24]
		num, err := ssz.DivideInt2(len(buf), 8, 1099511627776)
		if err != nil {
			return err
		}
		b.InactivityScores = ssz.ExtendUint64(b.InactivityScores, num)
		for ii := 0; ii < num; ii++ {
			b.InactivityScores[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	{
		buf = tail[o24:o27]
		if b.LatestExecutionPayloadHeader == nil {
			b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
		}
		if err = b.LatestExecutionPayloadHeader.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (27) 'HistoricalSummaries'
	{
		buf = tail[o27:o34]
		num, err := ssz.DivideInt2(len(buf), 64, 16777216)
		if err != nil {
			return err
		}
		b.HistoricalSummaries = make([]*HistoricalSummary, num)
		for ii := 0; ii < num; ii++ {
			if b.HistoricalSummaries[ii] == nil {
				b.HistoricalSummaries[ii] = new(HistoricalSummary)
			}
			if err = b.HistoricalSummaries[ii].UnmarshalSSZ(buf[ii*64 : (ii+1)*64]); err != nil {
				return err
			}
		}
	}

	// Field (34) 'PendingDeposits'
	{
		buf = tail[o34:o35]
		num, err := ssz.DivideInt2(len(buf), 192, 134217728)
		if err != nil {
			return err
		}
		b.PendingDeposits = make([]*PendingDeposit, num)
		for ii := 0; ii < num; ii++ {
			if b.PendingDeposits[ii] == nil {
				b.PendingDeposits[ii] = new(PendingDeposit)
			}
			if err = b.PendingDeposits[ii].UnmarshalSSZ(buf[ii*192 : (ii+1)*192]); err != nil {
				return err
			}
		}
	}

	// Field (35) 'PendingPartialWithdrawals'
	{
		buf = tail[o35:o36]
		num, err := ssz.DivideInt2(len(buf), 24, 134217728)
		if err != nil {
			return err
		}
		b.PendingPartialWithdrawals = make([]*PendingPartialWithdrawal, num)
		for ii := 0; ii < num; ii++ {
			if b.PendingPartialWithdrawals[ii] == nil {
				b.PendingPartialWithdrawals[ii] = new(PendingPartialWithdrawal)
			}
			if err = b.PendingPartialWithdrawals[ii].UnmarshalSSZ(buf[ii*24 : (ii+1)*24]); err != nil {
				return err
			}
		}
	}

	// Field (36) 'PendingConsolidations'
	{
		buf = tail[o36:]
		num, err := ssz.DivideInt2(len(buf), 16, 262144)
		if err != nil {
			return err
		}
		b.PendingConsolidations = make([]*PendingConsolidation, num)
		for ii := 0; ii < num; ii++ {
			if b.PendingConsolidations[ii] == nil {
				b.PendingConsolidations[ii] = new(PendingConsolidation)
			}
			if err = b.PendingConsolidations[ii].UnmarshalSSZ(buf[ii*16 : (ii+1)*16]); err != nil {
				return err
			}
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconStateElectra object
func (b *BeaconStateElectra) SizeSSZ() (size int) {
	size = 2736713

	// Field (7) 'HistoricalRoots'
	size += len(b.HistoricalRoots) * 32

	// Field (9) 'Eth1DataVotes'
	size += len(b.Eth1DataVotes) * 72

	// Field (11) 'Validators'
	size += len(b.Validators) * 121

	// Field (12) 'Balances'
	size += len(b.Balances) * 8

	// Field (15) 'PreviousEpochParticipation'
	size += len(b.PreviousEpochParticipation)

	// Field (16) 'CurrentEpochParticipation'
	size += len(b.CurrentEpochParticipation)

	// Field (21) 'InactivityScores'
	size += len(b.InactivityScores) * 8

	// Field (24) 'LatestExecutionPayloadHeader'
	if b.LatestExecutionPayloadHeader == nil {
		b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
	}
	size += b.LatestExecutionPayloadHeader.SizeSSZ()

	// Field (27) 'HistoricalSummaries'
	size += len(b.HistoricalSummaries) * 64

	// Field (34) 'PendingDeposits'
	size += len(b.PendingDeposits) * 192

	// Field (35) 'PendingPartialWithdrawals'
	size += len(b.PendingPartialWithdrawals) * 24

	// Field (36) 'PendingConsolidations'
	size += len(b.PendingConsolidations) * 16

	return
}

// HashTreeRoot ssz hashes the BeaconStateElectra object
func (b *BeaconStateElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconStateElectra object with a hasher
func (b *BeaconStateElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'GenesisTime'
	hh.PutUint64(b.GenesisTime)

	// Field (1) 'GenesisValidatorsRoot'
	if size := len(b.GenesisValidatorsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.GenesisValidatorsRoot", size, 32)
		return
	}
	hh.PutBytes(b.GenesisValidatorsRoot)

	// Field (2) 'Slot'
	hh.PutUint64(b.Slot)

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if err = b.Fork.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if err = b.LatestBlockHeader.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (5) 'BlockRoots'
	{
		if size := len(b.BlockRoots); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateElectra.BlockRoots", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.BlockRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (6) 'StateRoots'
	{
		if size := len(b.StateRoots); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateElectra.StateRoots", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.StateRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (7) 'HistoricalRoots'
	{
		if size := len(b.HistoricalRoots); size > 16777216 {
			err = ssz.ErrListTooBigFn("BeaconStateElectra.HistoricalRoots", size, 16777216)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.HistoricalRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		numItems := uint64(len(b.HistoricalRoots))
		hh.MerkleizeWithMixin(subIndx, numItems, 16777216)
	}

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (9) 'Eth1DataVotes'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Eth1DataVotes))
		if num > 2048 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Eth1DataVotes {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 2048)
	}

	// Field (10) 'Eth1DepositIndex'
	hh.PutUint64(b.Eth1DepositIndex)

	// Field (11) 'Validators'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Validators))
		if num > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Validators {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 1099511627776)
	}

	// Field (12) 'Balances'
	{
		if size := len(b.Balances); size > 1099511627776 {
			err = ssz.ErrListTooBigFn("BeaconStateElectra.Balances", size, 1099511627776)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.Balances {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(b.Balances))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(1099511627776, numItems, 8))
	}

	// Field (13) 'RandaoMixes'
	{
		if size := len(b.RandaoMixes); size != 65536 {
			err = ssz.ErrVectorLengthFn("BeaconStateElectra.RandaoMixes", size, 65536)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.RandaoMixes {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (14) 'Slashings'
	{
		if size := len(b.Slashings); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateElectra.Slashings", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.Slashings {
			hh.AppendUint64(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (15) 'PreviousEpochParticipation'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(b.PreviousEpochParticipation))
		if byteLen > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(b.PreviousEpochParticipation)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (1099511627776+31)/32)
	}

	// Field (16) 'CurrentEpochParticipation'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(b.CurrentEpochParticipation))
		if byteLen > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(b.CurrentEpochParticipation)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (1099511627776+31)/32)
	}

	// Field (17) 'JustificationBits'
	if size := len(b.JustificationBits); size != 1 {
		err = ssz.ErrBytesLengthFn("BeaconStateElectra.JustificationBits", size, 1)
		return
	}
	hh.PutBytes(b.JustificationBits)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.PreviousJustifiedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.CurrentJustifiedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if err = b.FinalizedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (21) 'InactivityScores'
	{
		if size := len(b.InactivityScores); size > 1099511627776 {
			err = ssz.ErrListTooBigFn("BeaconStateElectra.InactivityScores", size, 1099511627776)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.InactivityScores {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(b.InactivityScores))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(1099511627776, numItems, 8))
	}

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if err = b.CurrentSyncCommittee.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if err = b.NextSyncCommittee.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	if err = b.LatestExecutionPayloadHeader.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (25) 'NextWithdrawalIndex'
	hh.PutUint64(b.NextWithdrawalIndex)

	// Field (26) 'NextWithdrawalValidatorIndex'
	hh.PutUint64(b.NextWithdrawalValidatorIndex)

	// Field (27) 'HistoricalSummaries'
	{
		subIndx := hh.Index()
		num := uint64(len(b.HistoricalSummaries))
		if num > 16777216 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.HistoricalSummaries {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16777216)
	}

	// Field (28) 'DepositRequestsStartIndex'
	hh.PutUint64(b.DepositRequestsStartIndex)

	// Field (29) 'DepositBalanceToConsume'
	hh.PutUint64(b.DepositBalanceToConsume)

	// Field (30) 'ExitBalanceToConsume'
	hh.PutUint64(b.ExitBalanceToConsume)

	// Field (31) 'EarliestExitEpoch'
	hh.PutUint64(b.EarliestExitEpoch)

	// Field (32) 'ConsolidationBalanceToConsume'
	hh.PutUint64(b.ConsolidationBalanceToConsume)

	// Field (33) 'EarliestConsolidationEpoch'
	hh.PutUint64(b.EarliestConsolidationEpoch)

	// Field (34) 'PendingDeposits'
	{
		subIndx := hh.Index()
		num := uint64(len(b.PendingDeposits))
		if num > 134217728 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.PendingDeposits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 134217728)
	}

	// Field (35) 'PendingPartialWithdrawals'
	{
		subIndx := hh.Index()
		num := uint64(len(b.PendingPartialWithdrawals))
		if num > 134217728 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.PendingPartialWithdrawals {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 134217728)
	}

	// Field (36) 'PendingConsolidations'
	{
		subIndx := hh.Index()
		num := uint64(len(b.PendingConsolidations))
		if num > 262144 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.PendingConsolidations {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 262144)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconStateElectra object
func (b *BeaconStateElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the AttestationElectra object
func (a *AttestationElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(a)
}

// MarshalSSZTo ssz marshals the AttestationElectra object to a target array
func (a *AttestationElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(236)

	// Offset (0) 'AggregationBits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(a.AggregationBits)

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if dst, err = a.Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Signature'
	dst = append(dst, a.Signature[:]...)

	// Field (3) 'CommitteeBits'
	if size := len(a.CommitteeBits); size != 8 {
		err = ssz.ErrBytesLengthFn("AttestationElectra.CommitteeBits", size, 8)
		return
	}
	dst = append(dst, a.CommitteeBits...)

	// Field (0) 'AggregationBits'
	if size := len(a.AggregationBits); size > 131072 {
		err = ssz.ErrBytesLengthFn("AttestationElectra.AggregationBits", size, 131072)
		return
	}
	dst = append(dst, a.AggregationBits...)

	return
}

// UnmarshalSSZ ssz unmarshals the AttestationElectra object
func (a *AttestationElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 236 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'AggregationBits'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 236 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if err = a.Data.UnmarshalSSZ(buf[4:132]); err != nil {
		return err
	}

	// Field (2) 'Signature'
	copy(a.Signature[:], buf[132:228])

	// Field (3) 'CommitteeBits'
	if cap(a.CommitteeBits) == 0 {
		a.CommitteeBits = make([]byte, 0, len(buf[228:236]))
	}
	a.CommitteeBits = append(a.CommitteeBits, buf[228:236]...)

	// Field (0) 'AggregationBits'
	{
		buf = tail[o0:]
		if err = ssz.ValidateBitlist(buf, 131072); err != nil {
			return err
		}
		if cap(a.AggregationBits) == 0 {
			a.AggregationBits = make([]byte, 0, len(buf))
		}
		a.AggregationBits = append(a.AggregationBits, buf...)
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the AttestationElectra object
func (a *AttestationElectra) SizeSSZ() (size int) {
	size = 236

	// Field (0) 'AggregationBits'
	size += len(a.AggregationBits)

	return
}

// HashTreeRoot ssz hashes the AttestationElectra object
func (a *AttestationElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(a)
}

// HashTreeRootWith ssz hashes the AttestationElectra object with a hasher
func (a *AttestationElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'AggregationBits'
	if len(a.AggregationBits) == 0 {
		err = ssz.ErrEmptyBitlist
		return
	}
	hh.PutBitlist(a.AggregationBits, 131072)

	// Field (1) 'Data'
	if a.Data == nil {
		a.Data = new(AttestationData)
	}
	if err = a.Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Signature'
	hh.PutBytes(a.Signature[:])

	// Field (3) 'CommitteeBits'
	if size := len(a.CommitteeBits); size != 8 {
		err = ssz.ErrBytesLengthFn("AttestationElectra.CommitteeBits", size, 8)
		return
	}
	hh.PutBytes(a.CommitteeBits)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the AttestationElectra object
func (a *AttestationElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(a)
}

// MarshalSSZ ssz marshals the AttesterSlashingElectra object
func (a *AttesterSlashingElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(a)
}

// MarshalSSZTo ssz marshals the AttesterSlashingElectra object to a target array
func (a *AttesterSlashingElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(8)

	// Offset (0) 'Attestation1'
	dst = ssz.WriteOffset(dst, offset)
	if a.Attestation1 == nil {
		a.Attestation1 = new(IndexedAttestationElectra)
	}
	offset += a.Attestation1.SizeSSZ()

	// Offset (1) 'Attestation2'
	dst = ssz.WriteOffset(dst, offset)
	if a.Attestation2 == nil {
		a.Attestation2 = new(IndexedAttestationElectra)
	}
	offset += a.Attestation2.SizeSSZ()

	// Field (0) 'Attestation1'
	if dst, err = a.Attestation1.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (1) 'Attestation2'
	if dst, err = a.Attestation2.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the AttesterSlashingElectra object
func (a *AttesterSlashingElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 8 {
		return ssz.ErrSize
	}

	tail := buf
	var o0, o1 uint64

	// Offset (0) 'Attestation1'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 8 {
		return ssz.ErrInvalidVariableOffset
	}

	// Offset (1) 'Attestation2'
	if o1 = ssz.ReadOffset(buf[4:8]); o1 > size || o0 > o1 {
		return ssz.ErrOffset
	}

	// Field (0) 'Attestation1'
	{
		buf = tail[o0:o1]
		if a.Attestation1 == nil {
			a.Attestation1 = new(IndexedAttestationElectra)
		}
		if err = a.Attestation1.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (1) 'Attestation2'
	{
		buf = tail[o1:]
		if a.Attestation2 == nil {
			a.Attestation2 = new(IndexedAttestationElectra)
		}
		if err = a.Attestation2.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the AttesterSlashingElectra object
func (a *AttesterSlashingElectra) SizeSSZ() (size int) {
	size = 8

	// Field (0) 'Attestation1'
	if a.Attestation1 == nil {
		a.Attestation1 = new(IndexedAttestationElectra)
	}
	size += a.Attestation1.SizeSSZ()

	// Field (1) 'Attestation2'
	if a.Attestation2 == nil {
		a.Attestation2 = new(IndexedAttestationElectra)
	}
	size += a.Attestation2.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the AttesterSlashingElectra object
func (a *AttesterSlashingElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(a)
}

// HashTreeRootWith ssz hashes the AttesterSlashingElectra object with a hasher
func (a *AttesterSlashingElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Attestation1'
	if err = a.Attestation1.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Attestation2'
	if err = a.Attestation2.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the AttesterSlashingElectra object
func (a *AttesterSlashingElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(a)
}

// MarshalSSZ ssz marshals the IndexedAttestationElectra object
func (i *IndexedAttestationElectra) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(i)
}

// MarshalSSZTo ssz marshals the IndexedAttestationElectra object to a target array
func (i *IndexedAttestationElectra) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(228)

	// Offset (0) 'AttestationIndices'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(i.AttestationIndices) * 8

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if dst, err = i.Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Signature'
	dst = append(dst, i.Signature[:]...)

	// Field (0) 'AttestationIndices'
	if size := len(i.AttestationIndices); size > 131072 {
		err = ssz.ErrListTooBigFn("IndexedAttestationElectra.AttestationIndices", size, 131072)
		return
	}
	for ii := 0; ii < len(i.AttestationIndices); ii++ {
		dst = ssz.MarshalUint64(dst, i.AttestationIndices[ii])
	}

	return
}

// UnmarshalSSZ ssz unmarshals the IndexedAttestationElectra object
func (i *IndexedAttestationElectra) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 228 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'AttestationIndices'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 228 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if err = i.Data.UnmarshalSSZ(buf[4:132]); err != nil {
		return err
	}

	// Field (2) 'Signature'
	copy(i.Signature[:], buf[132:228])

	// Field (0) 'AttestationIndices'
	{
		buf = tail[o0:]
		num, err := ssz.DivideInt2(len(buf), 8, 131072)
		if err != nil {
			return err
		}
		i.AttestationIndices = ssz.ExtendUint64(i.AttestationIndices, num)
		for ii := 0; ii < num; ii++ {
			i.AttestationIndices[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the IndexedAttestationElectra object
func (i *IndexedAttestationElectra) SizeSSZ() (size int) {
	size = 228

	// Field (0) 'AttestationIndices'
	size += len(i.AttestationIndices) * 8

	return
}

// HashTreeRoot ssz hashes the IndexedAttestationElectra object
func (i *IndexedAttestationElectra) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(i)
}

// HashTreeRootWith ssz hashes the IndexedAttestationElectra object with a hasher
func (i *IndexedAttestationElectra) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'AttestationIndices'
	{
		if size := len(i.AttestationIndices); size > 131072 {
			err = ssz.ErrListTooBigFn("IndexedAttestationElectra.AttestationIndices", size, 131072)
			return
		}
		subIndx := hh.Index()
		for _, i := range i.AttestationIndices {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(i.AttestationIndices))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(131072, numItems, 8))
	}

	// Field (1) 'Data'
	if i.Data == nil {
		i.Data = new(AttestationData)
	}
	if err = i.Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Signature'
	hh.PutBytes(i.Signature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the IndexedAttestationElectra object
func (i *IndexedAttestationElectra) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(i)
}

// MarshalSSZ ssz marshals the PendingPartialWithdrawal object
func (p *PendingPartialWithdrawal) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(p)
}

// MarshalSSZTo ssz marshals the PendingPartialWithdrawal object to a target array
func (p *PendingPartialWithdrawal) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'Index'
	dst = ssz.MarshalUint64(dst, p.Index)

	// Field (1) 'Amount'
	dst = ssz.MarshalUint64(dst, p.Amount)

	// Field (2) 'WithdrawableEpoch'
	dst = ssz.MarshalUint64(dst, p.WithdrawableEpoch)

	return
}

// UnmarshalSSZ ssz unmarshals the PendingPartialWithdrawal object
func (p *PendingPartialWithdrawal) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 24 {
		return ssz.ErrSize
	}

	// Field (0) 'Index'
	p.Index = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'Amount'
	p.Amount = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'WithdrawableEpoch'
	p.WithdrawableEpoch = ssz.UnmarshallUint64(buf[16:24])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the PendingPartialWithdrawal object
func (p *PendingPartialWithdrawal) SizeSSZ() (size int) {
	size = 24
	return
}

// HashTreeRoot ssz hashes the PendingPartialWithdrawal object
func (p *PendingPartialWithdrawal) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(p)
}

// HashTreeRootWith ssz hashes the PendingPartialWithdrawal object with a hasher
func (p *PendingPartialWithdrawal) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Index'
	hh.PutUint64(p.Index)

	// Field (1) 'Amount'
	hh.PutUint64(p.Amount)

	// Field (2) 'WithdrawableEpoch'
	hh.PutUint64(p.WithdrawableEpoch)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the PendingPartialWithdrawal object
func (p *PendingPartialWithdrawal) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(p)
}

// MarshalSSZ ssz marshals the PendingConsolidation object
func (p *PendingConsolidation) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(p)
}

// MarshalSSZTo ssz marshals the PendingConsolidation object to a target array
func (p *PendingConsolidation) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf

	// Field (0) 'SourceIndex'
	dst = ssz.MarshalUint64(dst, p.SourceIndex)

	// Field (1) 'TargetIndex'
	dst = ssz.MarshalUint64(dst, p.TargetIndex)

	return
}

// UnmarshalSSZ ssz unmarshals the PendingConsolidation object
func (p *PendingConsolidation) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size != 16 {
		return ssz.ErrSize
	}

	// Field (0) 'SourceIndex'
	p.SourceIndex = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'TargetIndex'
	p.TargetIndex = ssz.UnmarshallUint64(buf[8:16])

	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the PendingConsolidation object
func (p *PendingConsolidation) SizeSSZ() (size int) {
	size = 16
	return
}

// HashTreeRoot ssz hashes the PendingConsolidation object
func (p *PendingConsolidation) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(p)
}

// HashTreeRootWith ssz hashes the PendingConsolidation object with a hasher
func (p *PendingConsolidation) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'SourceIndex'
	hh.PutUint64(p.SourceIndex)

	// Field (1) 'TargetIndex'
	hh.PutUint64(p.TargetIndex)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the PendingConsolidation object
func (p *PendingConsolidation) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(p)
}

// MarshalSSZ ssz marshals the ExecutionRequests object
func (e *ExecutionRequests) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(e)
}

// MarshalSSZTo ssz marshals the ExecutionRequests object to a target array
func (e *ExecutionRequests) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(12)

	// Offset (0) 'Deposits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.Deposits) * 192

	// Offset (1) 'Withdrawals'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.Withdrawals) * 76

	// Offset (2) 'Consolidations'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.Consolidations) * 116

	// Field (0) 'Deposits'
	if size := len(e.Deposits); size > 8192 {
		err = ssz.ErrListTooBigFn("ExecutionRequests.Deposits", size, 8192)
		return
	}
	for ii := 0; ii < len(e.Deposits); ii++ {
		if dst, err = e.Deposits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (1) 'Withdrawals'
	if size := len(e.Withdrawals); size > 16 {
		err = ssz.ErrListTooBigFn("ExecutionRequests.Withdrawals", size, 16)
		return
	}
	for ii := 0; ii < len(e.Withdrawals); ii++ {
		if dst, err = e.Withdrawals[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (2) 'Consolidations'
	if size := len(e.Consolidations); size > 2 {
		err = ssz.ErrListTooBigFn("ExecutionRequests.Consolidations", size, 2)
		return
	}
	for ii := 0; ii < len(e.Consolidations); ii++ {
		if dst, err = e.Consolidations[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	return
}

// UnmarshalSSZ ssz unmarshals the ExecutionRequests object
func (e *ExecutionRequests) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 12 {
		return ssz.ErrSize
	}

	tail := buf
	var o0, o1, o2 uint64

	// Offset (0) 'Deposits'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 12 {
		return ssz.ErrInvalidVariableOffset
	}

	// Offset (1) 'Withdrawals'
	if o1 = ssz.ReadOffset(buf[4:8]); o1 > size || o0 > o1 {
		return ssz.ErrOffset
	}

	// Offset (2) 'Consolidations'
	if o2 = ssz.ReadOffset(buf[8:12]); o2 > size || o1 > o2 {
		return ssz.ErrOffset
	}

	// Field (0) 'Deposits'
	{
		buf = tail[o0:o1]
		num, err := ssz.DivideInt2(len(buf), 192, 8192)
		if err != nil {
			return err
		}
		e.Deposits = make([]*DepositRequest, num)
		for ii := 0; ii < num; ii++ {
			if e.Deposits[ii] == nil {
				e.Deposits[ii] = new(DepositRequest)
			}
			if err = e.Deposits[ii].UnmarshalSSZ(buf[ii*192 : (ii+1)*192]); err != nil {
				return err
			}
		}
	}

	// Field (1) 'Withdrawals'
	{
		buf = tail[o1:o2]
		num, err := ssz.DivideInt2(len(buf), 76, 16)
		if err != nil {
			return err
		}
		e.Withdrawals = make([]*WithdrawalRequest, num)
		for ii := 0; ii < num; ii++ {
			if e.Withdrawals[ii] == nil {
				e.Withdrawals[ii] = new(WithdrawalRequest)
			}
			if err = e.Withdrawals[ii].UnmarshalSSZ(buf[ii*76 : (ii+1)*76]); err != nil {
				return err
			}
		}
	}

	// Field (2) 'Consolidations'
	{
		buf = tail[o2:]
		num, err := ssz.DivideInt2(len(buf), 116, 2)
		if err != nil {
			return err
		}
		e.Consolidations = make([]*ConsolidationRequest, num)
		for ii := 0; ii < num; ii++ {
			if e.Consolidations[ii] == nil {
				e.Consolidations[ii] = new(ConsolidationRequest)
			}
			if err = e.Consolidations[ii].UnmarshalSSZ(buf[ii*116 : (ii+1)*116]); err != nil {
				return err
			}
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the ExecutionRequests object
func (e *ExecutionRequests) SizeSSZ() (size int) {
	size = 12

	// Field (0) 'Deposits'
	size += len(e.Deposits) * 192

	// Field (1) 'Withdrawals'
	size += len(e.Withdrawals) * 76

	// Field (2) 'Consolidations'
	size += len(e.Consolidations) * 116

	return
}

// HashTreeRoot ssz hashes the ExecutionRequests object
func (e *ExecutionRequests) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(e)
}

// HashTreeRootWith ssz hashes the ExecutionRequests object with a hasher
func (e *ExecutionRequests) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Deposits'
	{
		subIndx := hh.Index()
		num := uint64(len(e.Deposits))
		if num > 8192 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range e.Deposits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 8192)
	}

	// Field (1) 'Withdrawals'
	{
		subIndx := hh.Index()
		num := uint64(len(e.Withdrawals))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range e.Withdrawals {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (2) 'Consolidations'
	{
		subIndx := hh.Index()
		num := uint64(len(e.Consolidations))
		if num > 2 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range e.Consolidations {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 2)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the ExecutionRequests object
func (e *ExecutionRequests) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(e)
}
