// Code generated by fastssz. DO NOT EDIT.
// Hash: e80ede50b28a6c8990c5466d0a25a807e20ae9e7c1bd2bcfbe6d1f8df0150bc6
// Version: 0.1.3
package state

import (
	ssz "github.com/ferranbt/fastssz"
)

// MarshalSSZ ssz marshals the ExecutionPayloadDeneb object
func (e *ExecutionPayloadDeneb) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(e)
}

// MarshalSSZTo ssz marshals the ExecutionPayloadDeneb object to a target array
func (e *ExecutionPayloadDeneb) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(528)

	// Field (0) 'ParentHash'
	dst = append(dst, e.ParentHash[:]...)

	// Field (1) 'FeeRecipient'
	dst = append(dst, e.FeeRecipient[:]...)

	// Field (2) 'StateRoot'
	dst = append(dst, e.StateRoot[:]...)

	// Field (3) 'ReceiptsRoot'
	dst = append(dst, e.ReceiptsRoot[:]...)

	// Field (4) 'LogsBloom'
	dst = append(dst, e.LogsBloom[:]...)

	// Field (5) 'PrevRandao'
	dst = append(dst, e.PrevRandao[:]...)

	// Field (6) 'BlockNumber'
	dst = ssz.MarshalUint64(dst, e.BlockNumber)

	// Field (7) 'GasLimit'
	dst = ssz.MarshalUint64(dst, e.GasLimit)

	// Field (8) 'GasUsed'
	dst = ssz.MarshalUint64(dst, e.GasUsed)

	// Field (9) 'Timestamp'
	dst = ssz.MarshalUint64(dst, e.Timestamp)

	// Offset (10) 'ExtraData'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.ExtraData)

	// Field (11) 'BaseFeePerGas'
	dst = append(dst, e.BaseFeePerGas[:]...)

	// Field (12) 'BlockHash'
	dst = append(dst, e.BlockHash[:]...)

	// Offset (13) 'Transactions'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(e.Transactions); ii++ {
		offset += 4
		offset += len(e.Transactions[ii])
	}

	// Offset (14) 'Withdrawals'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.Withdrawals) * 44

	// Field (15) 'BlobGasUsed'
	dst = ssz.MarshalUint64(dst, e.BlobGasUsed)

	// Field (16) 'ExcessBlobGas'
	dst = ssz.MarshalUint64(dst, e.ExcessBlobGas)

	// Field (10) 'ExtraData'
	if size := len(e.ExtraData); size > 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadDeneb.ExtraData", size, 32)
		return
	}
	dst = append(dst, e.ExtraData...)

	// Field (13) 'Transactions'
	if size := len(e.Transactions); size > 1048576 {
		err = ssz.ErrListTooBigFn("ExecutionPayloadDeneb.Transactions", size, 1048576)
		return
	}
	{
		offset = 4 * len(e.Transactions)
		for ii := 0; ii < len(e.Transactions); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += len(e.Transactions[ii])
		}
	}
	for ii := 0; ii < len(e.Transactions); ii++ {
		if size := len(e.Transactions[ii]); size > 1073741824 {
			err = ssz.ErrBytesLengthFn("ExecutionPayloadDeneb.Transactions[ii]", size, 1073741824)
			return
		}
		dst = append(dst, e.Transactions[ii]...)
	}

	// Field (14) 'Withdrawals'
	if size := len(e.Withdrawals); size > 16 {
		err = ssz.ErrListTooBigFn("ExecutionPayloadDeneb.Withdrawals", size, 16)
		return
	}
	for ii := 0; ii < len(e.Withdrawals); ii++ {
		if dst, err = e.Withdrawals[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	return
}

// UnmarshalSSZ ssz unmarshals the ExecutionPayloadDeneb object
func (e *ExecutionPayloadDeneb) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 528 {
		return ssz.ErrSize
	}

	tail := buf
	var o10, o13, o14 uint64

	// Field (0) 'ParentHash'
	copy(e.ParentHash[:], buf[0:32])

	// Field (1) 'FeeRecipient'
	copy(e.FeeRecipient[:], buf[32:52])

	// Field (2) 'StateRoot'
	copy(e.StateRoot[:], buf[52:84])

	// Field (3) 'ReceiptsRoot'
	copy(e.ReceiptsRoot[:], buf[84:116])

	// Field (4) 'LogsBloom'
	copy(e.LogsBloom[:], buf[116:372])

	// Field (5) 'PrevRandao'
	copy(e.PrevRandao[:], buf[372:404])

	// Field (6) 'BlockNumber'
	e.BlockNumber = ssz.UnmarshallUint64(buf[404:412])

	// Field (7) 'GasLimit'
	e.GasLimit = ssz.UnmarshallUint64(buf[412:420])

	// Field (8) 'GasUsed'
	e.GasUsed = ssz.UnmarshallUint64(buf[420:428])

	// Field (9) 'Timestamp'
	e.Timestamp = ssz.UnmarshallUint64(buf[428:436])

	// Offset (10) 'ExtraData'
	if o10 = ssz.ReadOffset(buf[436:440]); o10 > size {
		return ssz.ErrOffset
	}

	if o10 < 528 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (11) 'BaseFeePerGas'
	copy(e.BaseFeePerGas[:], buf[440:472])

	// Field (12) 'BlockHash'
	copy(e.BlockHash[:], buf[472:504])

	// Offset (13) 'Transactions'
	if o13 = ssz.ReadOffset(buf[504:508]); o13 > size || o10 > o13 {
		return ssz.ErrOffset
	}

	// Offset (14) 'Withdrawals'
	if o14 = ssz.ReadOffset(buf[508:512]); o14 > size || o13 > o14 {
		return ssz.ErrOffset
	}

	// Field (15) 'BlobGasUsed'
	e.BlobGasUsed = ssz.UnmarshallUint64(buf[512:520])

	// Field (16) 'ExcessBlobGas'
	e.ExcessBlobGas = ssz.UnmarshallUint64(buf[520:528])

	// Field (10) 'ExtraData'
	{
		buf = tail[o10:o13]
		if len(buf) > 32 {
			return ssz.ErrBytesLength
		}
		if cap(e.ExtraData) == 0 {
			e.ExtraData = make([]byte, 0, len(buf))
		}
		e.ExtraData = append(e.ExtraData, buf...)
	}

	// Field (13) 'Transactions'
	{
		buf = tail[o13:o14]
		num, err := ssz.DecodeDynamicLength(buf, 1048576)
		if err != nil {
			return err
		}
		e.Transactions = make([][]byte, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if len(buf) > 1073741824 {
				return ssz.ErrBytesLength
			}
			if cap(e.Transactions[indx]) == 0 {
				e.Transactions[indx] = make([]byte, 0, len(buf))
			}
			e.Transactions[indx] = append(e.Transactions[indx], buf...)
			return nil
		})
		if err != nil {
			return err
		}
	}

	// Field (14) 'Withdrawals'
	{
		buf = tail[o14:]
		num, err := ssz.DivideInt2(len(buf), 44, 16)
		if err != nil {
			return err
		}
		e.Withdrawals = make([]*Withdrawal, num)
		for ii := 0; ii < num; ii++ {
			if e.Withdrawals[ii] == nil {
				e.Withdrawals[ii] = new(Withdrawal)
			}
			if err = e.Withdrawals[ii].UnmarshalSSZ(buf[ii*44 : (ii+1)*44]); err != nil {
				return err
			}
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the ExecutionPayloadDeneb object
func (e *ExecutionPayloadDeneb) SizeSSZ() (size int) {
	size = 528

	// Field (10) 'ExtraData'
	size += len(e.ExtraData)

	// Field (13) 'Transactions'
	for ii := 0; ii < len(e.Transactions); ii++ {
		size += 4
		size += len(e.Transactions[ii])
	}

	// Field (14) 'Withdrawals'
	size += len(e.Withdrawals) * 44

	return
}

// HashTreeRoot ssz hashes the ExecutionPayloadDeneb object
func (e *ExecutionPayloadDeneb) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(e)
}

// HashTreeRootWith ssz hashes the ExecutionPayloadDeneb object with a hasher
func (e *ExecutionPayloadDeneb) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'ParentHash'
	hh.PutBytes(e.ParentHash[:])

	// Field (1) 'FeeRecipient'
	hh.PutBytes(e.FeeRecipient[:])

	// Field (2) 'StateRoot'
	hh.PutBytes(e.StateRoot[:])

	// Field (3) 'ReceiptsRoot'
	hh.PutBytes(e.ReceiptsRoot[:])

	// Field (4) 'LogsBloom'
	hh.PutBytes(e.LogsBloom[:])

	// Field (5) 'PrevRandao'
	hh.PutBytes(e.PrevRandao[:])

	// Field (6) 'BlockNumber'
	hh.PutUint64(e.BlockNumber)

	// Field (7) 'GasLimit'
	hh.PutUint64(e.GasLimit)

	// Field (8) 'GasUsed'
	hh.PutUint64(e.GasUsed)

	// Field (9) 'Timestamp'
	hh.PutUint64(e.Timestamp)

	// Field (10) 'ExtraData'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(e.ExtraData))
		if byteLen > 32 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(e.ExtraData)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (32+31)/32)
	}

	// Field (11) 'BaseFeePerGas'
	hh.PutBytes(e.BaseFeePerGas[:])

	// Field (12) 'BlockHash'
	hh.PutBytes(e.BlockHash[:])

	// Field (13) 'Transactions'
	{
		subIndx := hh.Index()
		num := uint64(len(e.Transactions))
		if num > 1048576 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range e.Transactions {
			{
				elemIndx := hh.Index()
				byteLen := uint64(len(elem))
				if byteLen > 1073741824 {
					err = ssz.ErrIncorrectListSize
					return
				}
				hh.AppendBytes32(elem)
				hh.MerkleizeWithMixin(elemIndx, byteLen, (1073741824+31)/32)
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 1048576)
	}

	// Field (14) 'Withdrawals'
	{
		subIndx := hh.Index()
		num := uint64(len(e.Withdrawals))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range e.Withdrawals {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (15) 'BlobGasUsed'
	hh.PutUint64(e.BlobGasUsed)

	// Field (16) 'ExcessBlobGas'
	hh.PutUint64(e.ExcessBlobGas)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the ExecutionPayloadDeneb object
func (e *ExecutionPayloadDeneb) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(e)
}

// MarshalSSZ ssz marshals the ExecutionPayloadHeaderDeneb object
func (e *ExecutionPayloadHeaderDeneb) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(e)
}

// MarshalSSZTo ssz marshals the ExecutionPayloadHeaderDeneb object to a target array
func (e *ExecutionPayloadHeaderDeneb) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(584)

	// Field (0) 'ParentHash'
	if size := len(e.ParentHash); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.ParentHash", size, 32)
		return
	}
	dst = append(dst, e.ParentHash...)

	// Field (1) 'FeeRecipient'
	if size := len(e.FeeRecipient); size != 20 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.FeeRecipient", size, 20)
		return
	}
	dst = append(dst, e.FeeRecipient...)

	// Field (2) 'StateRoot'
	if size := len(e.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.StateRoot", size, 32)
		return
	}
	dst = append(dst, e.StateRoot...)

	// Field (3) 'ReceiptsRoot'
	if size := len(e.ReceiptsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.ReceiptsRoot", size, 32)
		return
	}
	dst = append(dst, e.ReceiptsRoot...)

	// Field (4) 'LogsBloom'
	if size := len(e.LogsBloom); size != 256 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.LogsBloom", size, 256)
		return
	}
	dst = append(dst, e.LogsBloom...)

	// Field (5) 'PrevRandao'
	if size := len(e.PrevRandao); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.PrevRandao", size, 32)
		return
	}
	dst = append(dst, e.PrevRandao...)

	// Field (6) 'BlockNumber'
	dst = ssz.MarshalUint64(dst, e.BlockNumber)

	// Field (7) 'GasLimit'
	dst = ssz.MarshalUint64(dst, e.GasLimit)

	// Field (8) 'GasUsed'
	dst = ssz.MarshalUint64(dst, e.GasUsed)

	// Field (9) 'Timestamp'
	dst = ssz.MarshalUint64(dst, e.Timestamp)

	// Offset (10) 'ExtraData'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(e.ExtraData)

	// Field (11) 'BaseFeePerGas'
	if size := len(e.BaseFeePerGas); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.BaseFeePerGas", size, 32)
		return
	}
	dst = append(dst, e.BaseFeePerGas...)

	// Field (12) 'BlockHash'
	if size := len(e.BlockHash); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.BlockHash", size, 32)
		return
	}
	dst = append(dst, e.BlockHash...)

	// Field (13) 'TransactionsRoot'
	if size := len(e.TransactionsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.TransactionsRoot", size, 32)
		return
	}
	dst = append(dst, e.TransactionsRoot...)

	// Field (14) 'WithdrawalsRoot'
	if size := len(e.WithdrawalsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.WithdrawalsRoot", size, 32)
		return
	}
	dst = append(dst, e.WithdrawalsRoot...)

	// Field (15) 'BlobGasUsed'
	dst = ssz.MarshalUint64(dst, e.BlobGasUsed)

	// Field (16) 'ExcessBlobGas'
	dst = ssz.MarshalUint64(dst, e.ExcessBlobGas)

	// Field (10) 'ExtraData'
	if size := len(e.ExtraData); size > 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.ExtraData", size, 32)
		return
	}
	dst = append(dst, e.ExtraData...)

	return
}

// UnmarshalSSZ ssz unmarshals the ExecutionPayloadHeaderDeneb object
func (e *ExecutionPayloadHeaderDeneb) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 584 {
		return ssz.ErrSize
	}

	tail := buf
	var o10 uint64

	// Field (0) 'ParentHash'
	if cap(e.ParentHash) == 0 {
		e.ParentHash = make([]byte, 0, len(buf[0:32]))
	}
	e.ParentHash = append(e.ParentHash, buf[0:32]...)

	// Field (1) 'FeeRecipient'
	if cap(e.FeeRecipient) == 0 {
		e.FeeRecipient = make([]byte, 0, len(buf[32:52]))
	}
	e.FeeRecipient = append(e.FeeRecipient, buf[32:52]...)

	// Field (2) 'StateRoot'
	if cap(e.StateRoot) == 0 {
		e.StateRoot = make([]byte, 0, len(buf[52:84]))
	}
	e.StateRoot = append(e.StateRoot, buf[52:84]...)

	// Field (3) 'ReceiptsRoot'
	if cap(e.ReceiptsRoot) == 0 {
		e.ReceiptsRoot = make([]byte, 0, len(buf[84:116]))
	}
	e.ReceiptsRoot = append(e.ReceiptsRoot, buf[84:116]...)

	// Field (4) 'LogsBloom'
	if cap(e.LogsBloom) == 0 {
		e.LogsBloom = make([]byte, 0, len(buf[116:372]))
	}
	e.LogsBloom = append(e.LogsBloom, buf[116:372]...)

	// Field (5) 'PrevRandao'
	if cap(e.PrevRandao) == 0 {
		e.PrevRandao = make([]byte, 0, len(buf[372:404]))
	}
	e.PrevRandao = append(e.PrevRandao, buf[372:404]...)

	// Field (6) 'BlockNumber'
	e.BlockNumber = ssz.UnmarshallUint64(buf[404:412])

	// Field (7) 'GasLimit'
	e.GasLimit = ssz.UnmarshallUint64(buf[412:420])

	// Field (8) 'GasUsed'
	e.GasUsed = ssz.UnmarshallUint64(buf[420:428])

	// Field (9) 'Timestamp'
	e.Timestamp = ssz.UnmarshallUint64(buf[428:436])

	// Offset (10) 'ExtraData'
	if o10 = ssz.ReadOffset(buf[436:440]); o10 > size {
		return ssz.ErrOffset
	}

	if o10 < 584 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (11) 'BaseFeePerGas'
	if cap(e.BaseFeePerGas) == 0 {
		e.BaseFeePerGas = make([]byte, 0, len(buf[440:472]))
	}
	e.BaseFeePerGas = append(e.BaseFeePerGas, buf[440:472]...)

	// Field (12) 'BlockHash'
	if cap(e.BlockHash) == 0 {
		e.BlockHash = make([]byte, 0, len(buf[472:504]))
	}
	e.BlockHash = append(e.BlockHash, buf[472:504]...)

	// Field (13) 'TransactionsRoot'
	if cap(e.TransactionsRoot) == 0 {
		e.TransactionsRoot = make([]byte, 0, len(buf[504:536]))
	}
	e.TransactionsRoot = append(e.TransactionsRoot, buf[504:536]...)

	// Field (14) 'WithdrawalsRoot'
	if cap(e.WithdrawalsRoot) == 0 {
		e.WithdrawalsRoot = make([]byte, 0, len(buf[536:568]))
	}
	e.WithdrawalsRoot = append(e.WithdrawalsRoot, buf[536:568]...)

	// Field (15) 'BlobGasUsed'
	e.BlobGasUsed = ssz.UnmarshallUint64(buf[568:576])

	// Field (16) 'ExcessBlobGas'
	e.ExcessBlobGas = ssz.UnmarshallUint64(buf[576:584])

	// Field (10) 'ExtraData'
	{
		buf = tail[o10:]
		if len(buf) > 32 {
			return ssz.ErrBytesLength
		}
		if cap(e.ExtraData) == 0 {
			e.ExtraData = make([]byte, 0, len(buf))
		}
		e.ExtraData = append(e.ExtraData, buf...)
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the ExecutionPayloadHeaderDeneb object
func (e *ExecutionPayloadHeaderDeneb) SizeSSZ() (size int) {
	size = 584

	// Field (10) 'ExtraData'
	size += len(e.ExtraData)

	return
}

// HashTreeRoot ssz hashes the ExecutionPayloadHeaderDeneb object
func (e *ExecutionPayloadHeaderDeneb) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(e)
}

// HashTreeRootWith ssz hashes the ExecutionPayloadHeaderDeneb object with a hasher
func (e *ExecutionPayloadHeaderDeneb) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'ParentHash'
	if size := len(e.ParentHash); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.ParentHash", size, 32)
		return
	}
	hh.PutBytes(e.ParentHash)

	// Field (1) 'FeeRecipient'
	if size := len(e.FeeRecipient); size != 20 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.FeeRecipient", size, 20)
		return
	}
	hh.PutBytes(e.FeeRecipient)

	// Field (2) 'StateRoot'
	if size := len(e.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.StateRoot", size, 32)
		return
	}
	hh.PutBytes(e.StateRoot)

	// Field (3) 'ReceiptsRoot'
	if size := len(e.ReceiptsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.ReceiptsRoot", size, 32)
		return
	}
	hh.PutBytes(e.ReceiptsRoot)

	// Field (4) 'LogsBloom'
	if size := len(e.LogsBloom); size != 256 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.LogsBloom", size, 256)
		return
	}
	hh.PutBytes(e.LogsBloom)

	// Field (5) 'PrevRandao'
	if size := len(e.PrevRandao); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.PrevRandao", size, 32)
		return
	}
	hh.PutBytes(e.PrevRandao)

	// Field (6) 'BlockNumber'
	hh.PutUint64(e.BlockNumber)

	// Field (7) 'GasLimit'
	hh.PutUint64(e.GasLimit)

	// Field (8) 'GasUsed'
	hh.PutUint64(e.GasUsed)

	// Field (9) 'Timestamp'
	hh.PutUint64(e.Timestamp)

	// Field (10) 'ExtraData'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(e.ExtraData))
		if byteLen > 32 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(e.ExtraData)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (32+31)/32)
	}

	// Field (11) 'BaseFeePerGas'
	if size := len(e.BaseFeePerGas); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.BaseFeePerGas", size, 32)
		return
	}
	hh.PutBytes(e.BaseFeePerGas)

	// Field (12) 'BlockHash'
	if size := len(e.BlockHash); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.BlockHash", size, 32)
		return
	}
	hh.PutBytes(e.BlockHash)

	// Field (13) 'TransactionsRoot'
	if size := len(e.TransactionsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.TransactionsRoot", size, 32)
		return
	}
	hh.PutBytes(e.TransactionsRoot)

	// Field (14) 'WithdrawalsRoot'
	if size := len(e.WithdrawalsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("ExecutionPayloadHeaderDeneb.WithdrawalsRoot", size, 32)
		return
	}
	hh.PutBytes(e.WithdrawalsRoot)

	// Field (15) 'BlobGasUsed'
	hh.PutUint64(e.BlobGasUsed)

	// Field (16) 'ExcessBlobGas'
	hh.PutUint64(e.ExcessBlobGas)

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the ExecutionPayloadHeaderDeneb object
func (e *ExecutionPayloadHeaderDeneb) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(e)
}

// MarshalSSZ ssz marshals the SignedBeaconBlockDeneb object
func (s *SignedBeaconBlockDeneb) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(s)
}

// MarshalSSZTo ssz marshals the SignedBeaconBlockDeneb object to a target array
func (s *SignedBeaconBlockDeneb) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(100)

	// Offset (0) 'Message'
	dst = ssz.WriteOffset(dst, offset)
	offset += s.Message.SizeSSZ()

	// Field (1) 'Signature'
	dst = append(dst, s.Signature[:]...)

	// Field (0) 'Message'
	if dst, err = s.Message.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the SignedBeaconBlockDeneb object
func (s *SignedBeaconBlockDeneb) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 100 {
		return ssz.ErrSize
	}

	tail := buf
	var o0 uint64

	// Offset (0) 'Message'
	if o0 = ssz.ReadOffset(buf[0:4]); o0 > size {
		return ssz.ErrOffset
	}

	if o0 < 100 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (1) 'Signature'
	copy(s.Signature[:], buf[4:100])

	// Field (0) 'Message'
	{
		buf = tail[o0:]
		if err = s.Message.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the SignedBeaconBlockDeneb object
func (s *SignedBeaconBlockDeneb) SizeSSZ() (size int) {
	size = 100

	// Field (0) 'Message'
	size += s.Message.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the SignedBeaconBlockDeneb object
func (s *SignedBeaconBlockDeneb) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(s)
}

// HashTreeRootWith ssz hashes the SignedBeaconBlockDeneb object with a hasher
func (s *SignedBeaconBlockDeneb) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Message'
	if err = s.Message.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (1) 'Signature'
	hh.PutBytes(s.Signature[:])

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the SignedBeaconBlockDeneb object
func (s *SignedBeaconBlockDeneb) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(s)
}

// MarshalSSZ ssz marshals the BeaconBlockDenebMainnet object
func (b *BeaconBlockDenebMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconBlockDenebMainnet object to a target array
func (b *BeaconBlockDenebMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(84)

	// Field (0) 'Slot'
	dst = ssz.MarshalUint64(dst, b.Slot)

	// Field (1) 'ProposerIndex'
	dst = ssz.MarshalUint64(dst, b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockDenebMainnet.ParentRoot", size, 32)
		return
	}
	dst = append(dst, b.ParentRoot...)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockDenebMainnet.StateRoot", size, 32)
		return
	}
	dst = append(dst, b.StateRoot...)

	// Offset (4) 'Body'
	dst = ssz.WriteOffset(dst, offset)
	if b.Body == nil {
		b.Body = new(BeaconBlockBodyDenebMainnet)
	}
	offset += b.Body.SizeSSZ()

	// Field (4) 'Body'
	if dst, err = b.Body.MarshalSSZTo(dst); err != nil {
		return
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconBlockDenebMainnet object
func (b *BeaconBlockDenebMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 84 {
		return ssz.ErrSize
	}

	tail := buf
	var o4 uint64

	// Field (0) 'Slot'
	b.Slot = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'ProposerIndex'
	b.ProposerIndex = ssz.UnmarshallUint64(buf[8:16])

	// Field (2) 'ParentRoot'
	if cap(b.ParentRoot) == 0 {
		b.ParentRoot = make([]byte, 0, len(buf[16:48]))
	}
	b.ParentRoot = append(b.ParentRoot, buf[16:48]...)

	// Field (3) 'StateRoot'
	if cap(b.StateRoot) == 0 {
		b.StateRoot = make([]byte, 0, len(buf[48:80]))
	}
	b.StateRoot = append(b.StateRoot, buf[48:80]...)

	// Offset (4) 'Body'
	if o4 = ssz.ReadOffset(buf[80:84]); o4 > size {
		return ssz.ErrOffset
	}

	if o4 < 84 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (4) 'Body'
	{
		buf = tail[o4:]
		if b.Body == nil {
			b.Body = new(BeaconBlockBodyDenebMainnet)
		}
		if err = b.Body.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconBlockDenebMainnet object
func (b *BeaconBlockDenebMainnet) SizeSSZ() (size int) {
	size = 84

	// Field (4) 'Body'
	if b.Body == nil {
		b.Body = new(BeaconBlockBodyDenebMainnet)
	}
	size += b.Body.SizeSSZ()

	return
}

// HashTreeRoot ssz hashes the BeaconBlockDenebMainnet object
func (b *BeaconBlockDenebMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconBlockDenebMainnet object with a hasher
func (b *BeaconBlockDenebMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'Slot'
	hh.PutUint64(b.Slot)

	// Field (1) 'ProposerIndex'
	hh.PutUint64(b.ProposerIndex)

	// Field (2) 'ParentRoot'
	if size := len(b.ParentRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockDenebMainnet.ParentRoot", size, 32)
		return
	}
	hh.PutBytes(b.ParentRoot)

	// Field (3) 'StateRoot'
	if size := len(b.StateRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconBlockDenebMainnet.StateRoot", size, 32)
		return
	}
	hh.PutBytes(b.StateRoot)

	// Field (4) 'Body'
	if err = b.Body.HashTreeRootWith(hh); err != nil {
		return
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconBlockDenebMainnet object
func (b *BeaconBlockDenebMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the BeaconBlockBodyDenebMainnet object
func (b *BeaconBlockBodyDenebMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconBlockBodyDenebMainnet object to a target array
func (b *BeaconBlockBodyDenebMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(392)

	// Field (0) 'RandaoReveal'
	if size := len(b.RandaoReveal); size != 96 {
		err = ssz.ErrBytesLengthFn("BeaconBlockBodyDenebMainnet.RandaoReveal", size, 96)
		return
	}
	dst = append(dst, b.RandaoReveal...)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if dst, err = b.Eth1Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (2) 'Graffiti'
	dst = append(dst, b.Graffiti[:]...)

	// Offset (3) 'ProposerSlashings'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.ProposerSlashings) * 416

	// Offset (4) 'AttesterSlashings'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		offset += 4
		offset += b.AttesterSlashings[ii].SizeSSZ()
	}

	// Offset (5) 'Attestations'
	dst = ssz.WriteOffset(dst, offset)
	for ii := 0; ii < len(b.Attestations); ii++ {
		offset += 4
		offset += b.Attestations[ii].SizeSSZ()
	}

	// Offset (6) 'Deposits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Deposits) * 1240

	// Offset (7) 'VoluntaryExits'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.VoluntaryExits) * 112

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if dst, err = b.SyncAggregate.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (9) 'ExecutionPayload'
	dst = ssz.WriteOffset(dst, offset)
	if b.ExecutionPayload == nil {
		b.ExecutionPayload = new(ExecutionPayloadDeneb)
	}
	offset += b.ExecutionPayload.SizeSSZ()

	// Offset (10) 'BlsToExecutionChanges'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.BlsToExecutionChanges) * 172

	// Offset (11) 'BlobKzgCommitments'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.BlobKzgCommitments) * 48

	// Field (3) 'ProposerSlashings'
	if size := len(b.ProposerSlashings); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.ProposerSlashings", size, 16)
		return
	}
	for ii := 0; ii < len(b.ProposerSlashings); ii++ {
		if dst, err = b.ProposerSlashings[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (4) 'AttesterSlashings'
	if size := len(b.AttesterSlashings); size > 2 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.AttesterSlashings", size, 2)
		return
	}
	{
		offset = 4 * len(b.AttesterSlashings)
		for ii := 0; ii < len(b.AttesterSlashings); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += b.AttesterSlashings[ii].SizeSSZ()
		}
	}
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		if dst, err = b.AttesterSlashings[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (5) 'Attestations'
	if size := len(b.Attestations); size > 128 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.Attestations", size, 128)
		return
	}
	{
		offset = 4 * len(b.Attestations)
		for ii := 0; ii < len(b.Attestations); ii++ {
			dst = ssz.WriteOffset(dst, offset)
			offset += b.Attestations[ii].SizeSSZ()
		}
	}
	for ii := 0; ii < len(b.Attestations); ii++ {
		if dst, err = b.Attestations[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (6) 'Deposits'
	if size := len(b.Deposits); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.Deposits", size, 16)
		return
	}
	for ii := 0; ii < len(b.Deposits); ii++ {
		if dst, err = b.Deposits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (7) 'VoluntaryExits'
	if size := len(b.VoluntaryExits); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.VoluntaryExits", size, 16)
		return
	}
	for ii := 0; ii < len(b.VoluntaryExits); ii++ {
		if dst, err = b.VoluntaryExits[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (9) 'ExecutionPayload'
	if dst, err = b.ExecutionPayload.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (10) 'BlsToExecutionChanges'
	if size := len(b.BlsToExecutionChanges); size > 16 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.BlsToExecutionChanges", size, 16)
		return
	}
	for ii := 0; ii < len(b.BlsToExecutionChanges); ii++ {
		if dst, err = b.BlsToExecutionChanges[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (11) 'BlobKzgCommitments'
	if size := len(b.BlobKzgCommitments); size > 4096 {
		err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.BlobKzgCommitments", size, 4096)
		return
	}
	for ii := 0; ii < len(b.BlobKzgCommitments); ii++ {
		dst = append(dst, b.BlobKzgCommitments[ii][:]...)
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconBlockBodyDenebMainnet object
func (b *BeaconBlockBodyDenebMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 392 {
		return ssz.ErrSize
	}

	tail := buf
	var o3, o4, o5, o6, o7, o9, o10, o11 uint64

	// Field (0) 'RandaoReveal'
	if cap(b.RandaoReveal) == 0 {
		b.RandaoReveal = make([]byte, 0, len(buf[0:96]))
	}
	b.RandaoReveal = append(b.RandaoReveal, buf[0:96]...)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.UnmarshalSSZ(buf[96:168]); err != nil {
		return err
	}

	// Field (2) 'Graffiti'
	copy(b.Graffiti[:], buf[168:200])

	// Offset (3) 'ProposerSlashings'
	if o3 = ssz.ReadOffset(buf[200:204]); o3 > size {
		return ssz.ErrOffset
	}

	if o3 < 392 {
		return ssz.ErrInvalidVariableOffset
	}

	// Offset (4) 'AttesterSlashings'
	if o4 = ssz.ReadOffset(buf[204:208]); o4 > size || o3 > o4 {
		return ssz.ErrOffset
	}

	// Offset (5) 'Attestations'
	if o5 = ssz.ReadOffset(buf[208:212]); o5 > size || o4 > o5 {
		return ssz.ErrOffset
	}

	// Offset (6) 'Deposits'
	if o6 = ssz.ReadOffset(buf[212:216]); o6 > size || o5 > o6 {
		return ssz.ErrOffset
	}

	// Offset (7) 'VoluntaryExits'
	if o7 = ssz.ReadOffset(buf[216:220]); o7 > size || o6 > o7 {
		return ssz.ErrOffset
	}

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if err = b.SyncAggregate.UnmarshalSSZ(buf[220:380]); err != nil {
		return err
	}

	// Offset (9) 'ExecutionPayload'
	if o9 = ssz.ReadOffset(buf[380:384]); o9 > size || o7 > o9 {
		return ssz.ErrOffset
	}

	// Offset (10) 'BlsToExecutionChanges'
	if o10 = ssz.ReadOffset(buf[384:388]); o10 > size || o9 > o10 {
		return ssz.ErrOffset
	}

	// Offset (11) 'BlobKzgCommitments'
	if o11 = ssz.ReadOffset(buf[388:392]); o11 > size || o10 > o11 {
		return ssz.ErrOffset
	}

	// Field (3) 'ProposerSlashings'
	{
		buf = tail[o3:o4]
		num, err := ssz.DivideInt2(len(buf), 416, 16)
		if err != nil {
			return err
		}
		b.ProposerSlashings = make([]*ProposerSlashing, num)
		for ii := 0; ii < num; ii++ {
			if b.ProposerSlashings[ii] == nil {
				b.ProposerSlashings[ii] = new(ProposerSlashing)
			}
			if err = b.ProposerSlashings[ii].UnmarshalSSZ(buf[ii*416 : (ii+1)*416]); err != nil {
				return err
			}
		}
	}

	// Field (4) 'AttesterSlashings'
	{
		buf = tail[o4:o5]
		num, err := ssz.DecodeDynamicLength(buf, 2)
		if err != nil {
			return err
		}
		b.AttesterSlashings = make([]*AttesterSlashing, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if b.AttesterSlashings[indx] == nil {
				b.AttesterSlashings[indx] = new(AttesterSlashing)
			}
			if err = b.AttesterSlashings[indx].UnmarshalSSZ(buf); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	// Field (5) 'Attestations'
	{
		buf = tail[o5:o6]
		num, err := ssz.DecodeDynamicLength(buf, 128)
		if err != nil {
			return err
		}
		b.Attestations = make([]*Attestation, num)
		err = ssz.UnmarshalDynamic(buf, num, func(indx int, buf []byte) (err error) {
			if b.Attestations[indx] == nil {
				b.Attestations[indx] = new(Attestation)
			}
			if err = b.Attestations[indx].UnmarshalSSZ(buf); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	// Field (6) 'Deposits'
	{
		buf = tail[o6:o7]
		num, err := ssz.DivideInt2(len(buf), 1240, 16)
		if err != nil {
			return err
		}
		b.Deposits = make([]*Deposit, num)
		for ii := 0; ii < num; ii++ {
			if b.Deposits[ii] == nil {
				b.Deposits[ii] = new(Deposit)
			}
			if err = b.Deposits[ii].UnmarshalSSZ(buf[ii*1240 : (ii+1)*1240]); err != nil {
				return err
			}
		}
	}

	// Field (7) 'VoluntaryExits'
	{
		buf = tail[o7:o9]
		num, err := ssz.DivideInt2(len(buf), 112, 16)
		if err != nil {
			return err
		}
		b.VoluntaryExits = make([]*SignedVoluntaryExit, num)
		for ii := 0; ii < num; ii++ {
			if b.VoluntaryExits[ii] == nil {
				b.VoluntaryExits[ii] = new(SignedVoluntaryExit)
			}
			if err = b.VoluntaryExits[ii].UnmarshalSSZ(buf[ii*112 : (ii+1)*112]); err != nil {
				return err
			}
		}
	}

	// Field (9) 'ExecutionPayload'
	{
		buf = tail[o9:o10]
		if b.ExecutionPayload == nil {
			b.ExecutionPayload = new(ExecutionPayloadDeneb)
		}
		if err = b.ExecutionPayload.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (10) 'BlsToExecutionChanges'
	{
		buf = tail[o10:o11]
		num, err := ssz.DivideInt2(len(buf), 172, 16)
		if err != nil {
			return err
		}
		b.BlsToExecutionChanges = make([]*SignedBLSToExecutionChange, num)
		for ii := 0; ii < num; ii++ {
			if b.BlsToExecutionChanges[ii] == nil {
				b.BlsToExecutionChanges[ii] = new(SignedBLSToExecutionChange)
			}
			if err = b.BlsToExecutionChanges[ii].UnmarshalSSZ(buf[ii*172 : (ii+1)*172]); err != nil {
				return err
			}
		}
	}

	// Field (11) 'BlobKzgCommitments'
	{
		buf = tail[o11:]
		num, err := ssz.DivideInt2(len(buf), 48, 4096)
		if err != nil {
			return err
		}
		b.BlobKzgCommitments = make([][48]byte, num)
		for ii := 0; ii < num; ii++ {
			copy(b.BlobKzgCommitments[ii][:], buf[ii*48:(ii+1)*48])
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconBlockBodyDenebMainnet object
func (b *BeaconBlockBodyDenebMainnet) SizeSSZ() (size int) {
	size = 392

	// Field (3) 'ProposerSlashings'
	size += len(b.ProposerSlashings) * 416

	// Field (4) 'AttesterSlashings'
	for ii := 0; ii < len(b.AttesterSlashings); ii++ {
		size += 4
		size += b.AttesterSlashings[ii].SizeSSZ()
	}

	// Field (5) 'Attestations'
	for ii := 0; ii < len(b.Attestations); ii++ {
		size += 4
		size += b.Attestations[ii].SizeSSZ()
	}

	// Field (6) 'Deposits'
	size += len(b.Deposits) * 1240

	// Field (7) 'VoluntaryExits'
	size += len(b.VoluntaryExits) * 112

	// Field (9) 'ExecutionPayload'
	if b.ExecutionPayload == nil {
		b.ExecutionPayload = new(ExecutionPayloadDeneb)
	}
	size += b.ExecutionPayload.SizeSSZ()

	// Field (10) 'BlsToExecutionChanges'
	size += len(b.BlsToExecutionChanges) * 172

	// Field (11) 'BlobKzgCommitments'
	size += len(b.BlobKzgCommitments) * 48

	return
}

// HashTreeRoot ssz hashes the BeaconBlockBodyDenebMainnet object
func (b *BeaconBlockBodyDenebMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconBlockBodyDenebMainnet object with a hasher
func (b *BeaconBlockBodyDenebMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'RandaoReveal'
	if size := len(b.RandaoReveal); size != 96 {
		err = ssz.ErrBytesLengthFn("BeaconBlockBodyDenebMainnet.RandaoReveal", size, 96)
		return
	}
	hh.PutBytes(b.RandaoReveal)

	// Field (1) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (2) 'Graffiti'
	hh.PutBytes(b.Graffiti[:])

	// Field (3) 'ProposerSlashings'
	{
		subIndx := hh.Index()
		num := uint64(len(b.ProposerSlashings))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.ProposerSlashings {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (4) 'AttesterSlashings'
	{
		subIndx := hh.Index()
		num := uint64(len(b.AttesterSlashings))
		if num > 2 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.AttesterSlashings {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 2)
	}

	// Field (5) 'Attestations'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Attestations))
		if num > 128 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Attestations {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 128)
	}

	// Field (6) 'Deposits'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Deposits))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Deposits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (7) 'VoluntaryExits'
	{
		subIndx := hh.Index()
		num := uint64(len(b.VoluntaryExits))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.VoluntaryExits {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (8) 'SyncAggregate'
	if b.SyncAggregate == nil {
		b.SyncAggregate = new(SyncAggregateMainnet)
	}
	if err = b.SyncAggregate.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (9) 'ExecutionPayload'
	if err = b.ExecutionPayload.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (10) 'BlsToExecutionChanges'
	{
		subIndx := hh.Index()
		num := uint64(len(b.BlsToExecutionChanges))
		if num > 16 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.BlsToExecutionChanges {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16)
	}

	// Field (11) 'BlobKzgCommitments'
	{
		if size := len(b.BlobKzgCommitments); size > 4096 {
			err = ssz.ErrListTooBigFn("BeaconBlockBodyDenebMainnet.BlobKzgCommitments", size, 4096)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.BlobKzgCommitments {
			hh.PutBytes(i[:])
		}
		numItems := uint64(len(b.BlobKzgCommitments))
		hh.MerkleizeWithMixin(subIndx, numItems, 4096)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconBlockBodyDenebMainnet object
func (b *BeaconBlockBodyDenebMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}

// MarshalSSZ ssz marshals the BeaconStateDenebMainnet object
func (b *BeaconStateDenebMainnet) MarshalSSZ() ([]byte, error) {
	return ssz.MarshalSSZ(b)
}

// MarshalSSZTo ssz marshals the BeaconStateDenebMainnet object to a target array
func (b *BeaconStateDenebMainnet) MarshalSSZTo(buf []byte) (dst []byte, err error) {
	dst = buf
	offset := int(2736653)

	// Field (0) 'GenesisTime'
	dst = ssz.MarshalUint64(dst, b.GenesisTime)

	// Field (1) 'GenesisValidatorsRoot'
	if size := len(b.GenesisValidatorsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.GenesisValidatorsRoot", size, 32)
		return
	}
	dst = append(dst, b.GenesisValidatorsRoot...)

	// Field (2) 'Slot'
	dst = ssz.MarshalUint64(dst, b.Slot)

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if dst, err = b.Fork.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if dst, err = b.LatestBlockHeader.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (5) 'BlockRoots'
	if size := len(b.BlockRoots); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.BlockRoots", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		if size := len(b.BlockRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.BlockRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.BlockRoots[ii]...)
	}

	// Field (6) 'StateRoots'
	if size := len(b.StateRoots); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.StateRoots", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		if size := len(b.StateRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.StateRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.StateRoots[ii]...)
	}

	// Offset (7) 'HistoricalRoots'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.HistoricalRoots) * 32

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if dst, err = b.Eth1Data.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (9) 'Eth1DataVotes'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Eth1DataVotes) * 72

	// Field (10) 'Eth1DepositIndex'
	dst = ssz.MarshalUint64(dst, b.Eth1DepositIndex)

	// Offset (11) 'Validators'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Validators) * 121

	// Offset (12) 'Balances'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.Balances) * 8

	// Field (13) 'RandaoMixes'
	if size := len(b.RandaoMixes); size != 65536 {
		err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.RandaoMixes", size, 65536)
		return
	}
	for ii := 0; ii < 65536; ii++ {
		if size := len(b.RandaoMixes[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.RandaoMixes[ii]", size, 32)
			return
		}
		dst = append(dst, b.RandaoMixes[ii]...)
	}

	// Field (14) 'Slashings'
	if size := len(b.Slashings); size != 8192 {
		err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.Slashings", size, 8192)
		return
	}
	for ii := 0; ii < 8192; ii++ {
		dst = ssz.MarshalUint64(dst, b.Slashings[ii])
	}

	// Offset (15) 'PreviousEpochParticipation'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.PreviousEpochParticipation)

	// Offset (16) 'CurrentEpochParticipation'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.CurrentEpochParticipation)

	// Field (17) 'JustificationBits'
	if size := len(b.JustificationBits); size != 1 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.JustificationBits", size, 1)
		return
	}
	dst = append(dst, b.JustificationBits...)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.PreviousJustifiedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.CurrentJustifiedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if dst, err = b.FinalizedCheckpoint.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (21) 'InactivityScores'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.InactivityScores) * 8

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if dst, err = b.CurrentSyncCommittee.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if dst, err = b.NextSyncCommittee.MarshalSSZTo(dst); err != nil {
		return
	}

	// Offset (24) 'LatestExecutionPayloadHeader'
	dst = ssz.WriteOffset(dst, offset)
	if b.LatestExecutionPayloadHeader == nil {
		b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
	}
	offset += b.LatestExecutionPayloadHeader.SizeSSZ()

	// Field (25) 'NextWithdrawalIndex'
	dst = ssz.MarshalUint64(dst, b.NextWithdrawalIndex)

	// Field (26) 'NextWithdrawalValidatorIndex'
	dst = ssz.MarshalUint64(dst, b.NextWithdrawalValidatorIndex)

	// Offset (27) 'HistoricalSummaries'
	dst = ssz.WriteOffset(dst, offset)
	offset += len(b.HistoricalSummaries) * 64

	// Field (7) 'HistoricalRoots'
	if size := len(b.HistoricalRoots); size > 16777216 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.HistoricalRoots", size, 16777216)
		return
	}
	for ii := 0; ii < len(b.HistoricalRoots); ii++ {
		if size := len(b.HistoricalRoots[ii]); size != 32 {
			err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.HistoricalRoots[ii]", size, 32)
			return
		}
		dst = append(dst, b.HistoricalRoots[ii]...)
	}

	// Field (9) 'Eth1DataVotes'
	if size := len(b.Eth1DataVotes); size > 2048 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.Eth1DataVotes", size, 2048)
		return
	}
	for ii := 0; ii < len(b.Eth1DataVotes); ii++ {
		if dst, err = b.Eth1DataVotes[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (11) 'Validators'
	if size := len(b.Validators); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.Validators", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.Validators); ii++ {
		if dst, err = b.Validators[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	// Field (12) 'Balances'
	if size := len(b.Balances); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.Balances", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.Balances); ii++ {
		dst = ssz.MarshalUint64(dst, b.Balances[ii])
	}

	// Field (15) 'PreviousEpochParticipation'
	if size := len(b.PreviousEpochParticipation); size > 1099511627776 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.PreviousEpochParticipation", size, 1099511627776)
		return
	}
	dst = append(dst, b.PreviousEpochParticipation...)

	// Field (16) 'CurrentEpochParticipation'
	if size := len(b.CurrentEpochParticipation); size > 1099511627776 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.CurrentEpochParticipation", size, 1099511627776)
		return
	}
	dst = append(dst, b.CurrentEpochParticipation...)

	// Field (21) 'InactivityScores'
	if size := len(b.InactivityScores); size > 1099511627776 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.InactivityScores", size, 1099511627776)
		return
	}
	for ii := 0; ii < len(b.InactivityScores); ii++ {
		dst = ssz.MarshalUint64(dst, b.InactivityScores[ii])
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	if dst, err = b.LatestExecutionPayloadHeader.MarshalSSZTo(dst); err != nil {
		return
	}

	// Field (27) 'HistoricalSummaries'
	if size := len(b.HistoricalSummaries); size > 16777216 {
		err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.HistoricalSummaries", size, 16777216)
		return
	}
	for ii := 0; ii < len(b.HistoricalSummaries); ii++ {
		if dst, err = b.HistoricalSummaries[ii].MarshalSSZTo(dst); err != nil {
			return
		}
	}

	return
}

// UnmarshalSSZ ssz unmarshals the BeaconStateDenebMainnet object
func (b *BeaconStateDenebMainnet) UnmarshalSSZ(buf []byte) error {
	var err error
	size := uint64(len(buf))
	if size < 2736653 {
		return ssz.ErrSize
	}

	tail := buf
	var o7, o9, o11, o12, o15, o16, o21, o24, o27 uint64

	// Field (0) 'GenesisTime'
	b.GenesisTime = ssz.UnmarshallUint64(buf[0:8])

	// Field (1) 'GenesisValidatorsRoot'
	if cap(b.GenesisValidatorsRoot) == 0 {
		b.GenesisValidatorsRoot = make([]byte, 0, len(buf[8:40]))
	}
	b.GenesisValidatorsRoot = append(b.GenesisValidatorsRoot, buf[8:40]...)

	// Field (2) 'Slot'
	b.Slot = ssz.UnmarshallUint64(buf[40:48])

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if err = b.Fork.UnmarshalSSZ(buf[48:64]); err != nil {
		return err
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if err = b.LatestBlockHeader.UnmarshalSSZ(buf[64:176]); err != nil {
		return err
	}

	// Field (5) 'BlockRoots'
	b.BlockRoots = make([][]byte, 8192)
	for ii := 0; ii < 8192; ii++ {
		if cap(b.BlockRoots[ii]) == 0 {
			b.BlockRoots[ii] = make([]byte, 0, len(buf[176:262320][ii*32:(ii+1)*32]))
		}
		b.BlockRoots[ii] = append(b.BlockRoots[ii], buf[176:262320][ii*32:(ii+1)*32]...)
	}

	// Field (6) 'StateRoots'
	b.StateRoots = make([][]byte, 8192)
	for ii := 0; ii < 8192; ii++ {
		if cap(b.StateRoots[ii]) == 0 {
			b.StateRoots[ii] = make([]byte, 0, len(buf[262320:524464][ii*32:(ii+1)*32]))
		}
		b.StateRoots[ii] = append(b.StateRoots[ii], buf[262320:524464][ii*32:(ii+1)*32]...)
	}

	// Offset (7) 'HistoricalRoots'
	if o7 = ssz.ReadOffset(buf[524464:524468]); o7 > size {
		return ssz.ErrOffset
	}

	if o7 < 2736653 {
		return ssz.ErrInvalidVariableOffset
	}

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.UnmarshalSSZ(buf[524468:524540]); err != nil {
		return err
	}

	// Offset (9) 'Eth1DataVotes'
	if o9 = ssz.ReadOffset(buf[524540:524544]); o9 > size || o7 > o9 {
		return ssz.ErrOffset
	}

	// Field (10) 'Eth1DepositIndex'
	b.Eth1DepositIndex = ssz.UnmarshallUint64(buf[524544:524552])

	// Offset (11) 'Validators'
	if o11 = ssz.ReadOffset(buf[524552:524556]); o11 > size || o9 > o11 {
		return ssz.ErrOffset
	}

	// Offset (12) 'Balances'
	if o12 = ssz.ReadOffset(buf[524556:524560]); o12 > size || o11 > o12 {
		return ssz.ErrOffset
	}

	// Field (13) 'RandaoMixes'
	b.RandaoMixes = make([][]byte, 65536)
	for ii := 0; ii < 65536; ii++ {
		if cap(b.RandaoMixes[ii]) == 0 {
			b.RandaoMixes[ii] = make([]byte, 0, len(buf[524560:2621712][ii*32:(ii+1)*32]))
		}
		b.RandaoMixes[ii] = append(b.RandaoMixes[ii], buf[524560:2621712][ii*32:(ii+1)*32]...)
	}

	// Field (14) 'Slashings'
	b.Slashings = ssz.ExtendUint64(b.Slashings, 8192)
	for ii := 0; ii < 8192; ii++ {
		b.Slashings[ii] = ssz.UnmarshallUint64(buf[2621712:2687248][ii*8 : (ii+1)*8])
	}

	// Offset (15) 'PreviousEpochParticipation'
	if o15 = ssz.ReadOffset(buf[2687248:2687252]); o15 > size || o12 > o15 {
		return ssz.ErrOffset
	}

	// Offset (16) 'CurrentEpochParticipation'
	if o16 = ssz.ReadOffset(buf[2687252:2687256]); o16 > size || o15 > o16 {
		return ssz.ErrOffset
	}

	// Field (17) 'JustificationBits'
	if cap(b.JustificationBits) == 0 {
		b.JustificationBits = make([]byte, 0, len(buf[2687256:2687257]))
	}
	b.JustificationBits = append(b.JustificationBits, buf[2687256:2687257]...)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.PreviousJustifiedCheckpoint.UnmarshalSSZ(buf[2687257:2687297]); err != nil {
		return err
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.CurrentJustifiedCheckpoint.UnmarshalSSZ(buf[2687297:2687337]); err != nil {
		return err
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if err = b.FinalizedCheckpoint.UnmarshalSSZ(buf[2687337:2687377]); err != nil {
		return err
	}

	// Offset (21) 'InactivityScores'
	if o21 = ssz.ReadOffset(buf[2687377:2687381]); o21 > size || o16 > o21 {
		return ssz.ErrOffset
	}

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if err = b.CurrentSyncCommittee.UnmarshalSSZ(buf[2687381:2712005]); err != nil {
		return err
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if err = b.NextSyncCommittee.UnmarshalSSZ(buf[2712005:2736629]); err != nil {
		return err
	}

	// Offset (24) 'LatestExecutionPayloadHeader'
	if o24 = ssz.ReadOffset(buf[2736629:2736633]); o24 > size || o21 > o24 {
		return ssz.ErrOffset
	}

	// Field (25) 'NextWithdrawalIndex'
	b.NextWithdrawalIndex = ssz.UnmarshallUint64(buf[2736633:2736641])

	// Field (26) 'NextWithdrawalValidatorIndex'
	b.NextWithdrawalValidatorIndex = ssz.UnmarshallUint64(buf[2736641:2736649])

	// Offset (27) 'HistoricalSummaries'
	if o27 = ssz.ReadOffset(buf[2736649:2736653]); o27 > size || o24 > o27 {
		return ssz.ErrOffset
	}

	// Field (7) 'HistoricalRoots'
	{
		buf = tail[o7:o9]
		num, err := ssz.DivideInt2(len(buf), 32, 16777216)
		if err != nil {
			return err
		}
		b.HistoricalRoots = make([][]byte, num)
		for ii := 0; ii < num; ii++ {
			if cap(b.HistoricalRoots[ii]) == 0 {
				b.HistoricalRoots[ii] = make([]byte, 0, len(buf[ii*32:(ii+1)*32]))
			}
			b.HistoricalRoots[ii] = append(b.HistoricalRoots[ii], buf[ii*32:(ii+1)*32]...)
		}
	}

	// Field (9) 'Eth1DataVotes'
	{
		buf = tail[o9:o11]
		num, err := ssz.DivideInt2(len(buf), 72, 2048)
		if err != nil {
			return err
		}
		b.Eth1DataVotes = make([]*Eth1Data, num)
		for ii := 0; ii < num; ii++ {
			if b.Eth1DataVotes[ii] == nil {
				b.Eth1DataVotes[ii] = new(Eth1Data)
			}
			if err = b.Eth1DataVotes[ii].UnmarshalSSZ(buf[ii*72 : (ii+1)*72]); err != nil {
				return err
			}
		}
	}

	// Field (11) 'Validators'
	{
		buf = tail[o11:o12]
		num, err := ssz.DivideInt2(len(buf), 121, 1099511627776)
		if err != nil {
			return err
		}
		b.Validators = make([]*Validator, num)
		for ii := 0; ii < num; ii++ {
			if b.Validators[ii] == nil {
				b.Validators[ii] = new(Validator)
			}
			if err = b.Validators[ii].UnmarshalSSZ(buf[ii*121 : (ii+1)*121]); err != nil {
				return err
			}
		}
	}

	// Field (12) 'Balances'
	{
		buf = tail[o12:o15]
		num, err := ssz.DivideInt2(len(buf), 8, 1099511627776)
		if err != nil {
			return err
		}
		b.Balances = ssz.ExtendUint64(b.Balances, num)
		for ii := 0; ii < num; ii++ {
			b.Balances[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}

	// Field (15) 'PreviousEpochParticipation'
	{
		buf = tail[o15:o16]
		if len(buf) > 1099511627776 {
			return ssz.ErrBytesLength
		}
		if cap(b.PreviousEpochParticipation) == 0 {
			b.PreviousEpochParticipation = make([]byte, 0, len(buf))
		}
		b.PreviousEpochParticipation = append(b.PreviousEpochParticipation, buf...)
	}

	// Field (16) 'CurrentEpochParticipation'
	{
		buf = tail[o16:o21]
		if len(buf) > 1099511627776 {
			return ssz.ErrBytesLength
		}
		if cap(b.CurrentEpochParticipation) == 0 {
			b.CurrentEpochParticipation = make([]byte, 0, len(buf))
		}
		b.CurrentEpochParticipation = append(b.CurrentEpochParticipation, buf...)
	}

	// Field (21) 'InactivityScores'
	{
		buf = tail[o21:o24]
		num, err := ssz.DivideInt2(len(buf), 8, 1099511627776)
		if err != nil {
			return err
		}
		b.InactivityScores = ssz.ExtendUint64(b.InactivityScores, num)
		for ii := 0; ii < num; ii++ {
			b.InactivityScores[ii] = ssz.UnmarshallUint64(buf[ii*8 : (ii+1)*8])
		}
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	{
		buf = tail[o24:o27]
		if b.LatestExecutionPayloadHeader == nil {
			b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
		}
		if err = b.LatestExecutionPayloadHeader.UnmarshalSSZ(buf); err != nil {
			return err
		}
	}

	// Field (27) 'HistoricalSummaries'
	{
		buf = tail[o27:]
		num, err := ssz.DivideInt2(len(buf), 64, 16777216)
		if err != nil {
			return err
		}
		b.HistoricalSummaries = make([]*HistoricalSummary, num)
		for ii := 0; ii < num; ii++ {
			if b.HistoricalSummaries[ii] == nil {
				b.HistoricalSummaries[ii] = new(HistoricalSummary)
			}
			if err = b.HistoricalSummaries[ii].UnmarshalSSZ(buf[ii*64 : (ii+1)*64]); err != nil {
				return err
			}
		}
	}
	return err
}

// SizeSSZ returns the ssz encoded size in bytes for the BeaconStateDenebMainnet object
func (b *BeaconStateDenebMainnet) SizeSSZ() (size int) {
	size = 2736653

	// Field (7) 'HistoricalRoots'
	size += len(b.HistoricalRoots) * 32

	// Field (9) 'Eth1DataVotes'
	size += len(b.Eth1DataVotes) * 72

	// Field (11) 'Validators'
	size += len(b.Validators) * 121

	// Field (12) 'Balances'
	size += len(b.Balances) * 8

	// Field (15) 'PreviousEpochParticipation'
	size += len(b.PreviousEpochParticipation)

	// Field (16) 'CurrentEpochParticipation'
	size += len(b.CurrentEpochParticipation)

	// Field (21) 'InactivityScores'
	size += len(b.InactivityScores) * 8

	// Field (24) 'LatestExecutionPayloadHeader'
	if b.LatestExecutionPayloadHeader == nil {
		b.LatestExecutionPayloadHeader = new(ExecutionPayloadHeaderDeneb)
	}
	size += b.LatestExecutionPayloadHeader.SizeSSZ()

	// Field (27) 'HistoricalSummaries'
	size += len(b.HistoricalSummaries) * 64

	return
}

// HashTreeRoot ssz hashes the BeaconStateDenebMainnet object
func (b *BeaconStateDenebMainnet) HashTreeRoot() ([32]byte, error) {
	return ssz.HashWithDefaultHasher(b)
}

// HashTreeRootWith ssz hashes the BeaconStateDenebMainnet object with a hasher
func (b *BeaconStateDenebMainnet) HashTreeRootWith(hh ssz.HashWalker) (err error) {
	indx := hh.Index()

	// Field (0) 'GenesisTime'
	hh.PutUint64(b.GenesisTime)

	// Field (1) 'GenesisValidatorsRoot'
	if size := len(b.GenesisValidatorsRoot); size != 32 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.GenesisValidatorsRoot", size, 32)
		return
	}
	hh.PutBytes(b.GenesisValidatorsRoot)

	// Field (2) 'Slot'
	hh.PutUint64(b.Slot)

	// Field (3) 'Fork'
	if b.Fork == nil {
		b.Fork = new(Fork)
	}
	if err = b.Fork.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (4) 'LatestBlockHeader'
	if b.LatestBlockHeader == nil {
		b.LatestBlockHeader = new(BeaconBlockHeader)
	}
	if err = b.LatestBlockHeader.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (5) 'BlockRoots'
	{
		if size := len(b.BlockRoots); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.BlockRoots", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.BlockRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (6) 'StateRoots'
	{
		if size := len(b.StateRoots); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.StateRoots", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.StateRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (7) 'HistoricalRoots'
	{
		if size := len(b.HistoricalRoots); size > 16777216 {
			err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.HistoricalRoots", size, 16777216)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.HistoricalRoots {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		numItems := uint64(len(b.HistoricalRoots))
		hh.MerkleizeWithMixin(subIndx, numItems, 16777216)
	}

	// Field (8) 'Eth1Data'
	if b.Eth1Data == nil {
		b.Eth1Data = new(Eth1Data)
	}
	if err = b.Eth1Data.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (9) 'Eth1DataVotes'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Eth1DataVotes))
		if num > 2048 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Eth1DataVotes {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 2048)
	}

	// Field (10) 'Eth1DepositIndex'
	hh.PutUint64(b.Eth1DepositIndex)

	// Field (11) 'Validators'
	{
		subIndx := hh.Index()
		num := uint64(len(b.Validators))
		if num > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.Validators {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 1099511627776)
	}

	// Field (12) 'Balances'
	{
		if size := len(b.Balances); size > 1099511627776 {
			err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.Balances", size, 1099511627776)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.Balances {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(b.Balances))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(1099511627776, numItems, 8))
	}

	// Field (13) 'RandaoMixes'
	{
		if size := len(b.RandaoMixes); size != 65536 {
			err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.RandaoMixes", size, 65536)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.RandaoMixes {
			if len(i) != 32 {
				err = ssz.ErrBytesLength
				return
			}
			hh.Append(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (14) 'Slashings'
	{
		if size := len(b.Slashings); size != 8192 {
			err = ssz.ErrVectorLengthFn("BeaconStateDenebMainnet.Slashings", size, 8192)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.Slashings {
			hh.AppendUint64(i)
		}
		hh.Merkleize(subIndx)
	}

	// Field (15) 'PreviousEpochParticipation'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(b.PreviousEpochParticipation))
		if byteLen > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(b.PreviousEpochParticipation)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (1099511627776+31)/32)
	}

	// Field (16) 'CurrentEpochParticipation'
	{
		elemIndx := hh.Index()
		byteLen := uint64(len(b.CurrentEpochParticipation))
		if byteLen > 1099511627776 {
			err = ssz.ErrIncorrectListSize
			return
		}
		hh.Append(b.CurrentEpochParticipation)
		hh.MerkleizeWithMixin(elemIndx, byteLen, (1099511627776+31)/32)
	}

	// Field (17) 'JustificationBits'
	if size := len(b.JustificationBits); size != 1 {
		err = ssz.ErrBytesLengthFn("BeaconStateDenebMainnet.JustificationBits", size, 1)
		return
	}
	hh.PutBytes(b.JustificationBits)

	// Field (18) 'PreviousJustifiedCheckpoint'
	if b.PreviousJustifiedCheckpoint == nil {
		b.PreviousJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.PreviousJustifiedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (19) 'CurrentJustifiedCheckpoint'
	if b.CurrentJustifiedCheckpoint == nil {
		b.CurrentJustifiedCheckpoint = new(Checkpoint)
	}
	if err = b.CurrentJustifiedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (20) 'FinalizedCheckpoint'
	if b.FinalizedCheckpoint == nil {
		b.FinalizedCheckpoint = new(Checkpoint)
	}
	if err = b.FinalizedCheckpoint.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (21) 'InactivityScores'
	{
		if size := len(b.InactivityScores); size > 1099511627776 {
			err = ssz.ErrListTooBigFn("BeaconStateDenebMainnet.InactivityScores", size, 1099511627776)
			return
		}
		subIndx := hh.Index()
		for _, i := range b.InactivityScores {
			hh.AppendUint64(i)
		}
		hh.FillUpTo32()
		numItems := uint64(len(b.InactivityScores))
		hh.MerkleizeWithMixin(subIndx, numItems, ssz.CalculateLimit(1099511627776, numItems, 8))
	}

	// Field (22) 'CurrentSyncCommittee'
	if b.CurrentSyncCommittee == nil {
		b.CurrentSyncCommittee = new(SyncCommittee)
	}
	if err = b.CurrentSyncCommittee.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (23) 'NextSyncCommittee'
	if b.NextSyncCommittee == nil {
		b.NextSyncCommittee = new(SyncCommittee)
	}
	if err = b.NextSyncCommittee.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (24) 'LatestExecutionPayloadHeader'
	if err = b.LatestExecutionPayloadHeader.HashTreeRootWith(hh); err != nil {
		return
	}

	// Field (25) 'NextWithdrawalIndex'
	hh.PutUint64(b.NextWithdrawalIndex)

	// Field (26) 'NextWithdrawalValidatorIndex'
	hh.PutUint64(b.NextWithdrawalValidatorIndex)

	// Field (27) 'HistoricalSummaries'
	{
		subIndx := hh.Index()
		num := uint64(len(b.HistoricalSummaries))
		if num > 16777216 {
			err = ssz.ErrIncorrectListSize
			return
		}
		for _, elem := range b.HistoricalSummaries {
			if err = elem.HashTreeRootWith(hh); err != nil {
				return
			}
		}
		hh.MerkleizeWithMixin(subIndx, num, 16777216)
	}

	hh.Merkleize(indx)
	return
}

// GetTree ssz hashes the BeaconStateDenebMainnet object
func (b *BeaconStateDenebMainnet) GetTree() (*ssz.Node, error) {
	return ssz.ProofTree(b)
}
