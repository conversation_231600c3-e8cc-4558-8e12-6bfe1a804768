package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHexToBytes(t *testing.T) {
	values := []struct {
		name     string
		hex      string
		expected []byte
	}{
		{
			name:     "committee bits hex",
			hex:      "0xedfdbdffbffbffffffffdffffffff7ff7feffff7fffffffffbff7dfafdefffffdffbffaffffffeffffffeefbf6dffffffffffffffffffeffdfff7ffffff7fdff",
			expected: []byte{0xed, 0xfd, 0xbd, 0xff, 0xbf, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xf7, 0xff, 0x7f, 0xef, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0x7d, 0xfa, 0xfd, 0xef, 0xff, 0xff, 0xdf, 0xfb, 0xff, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xee, 0xfb, 0xf6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xdf, 0xff, 0x7f, 0xff, 0xff, 0xf7, 0xfd, 0xff},
		},
		{
			name:     "aggregation bits",
			hex:      "0x0000000000000000000000000000000104",
			expected: []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x4},
		},
	}

	for _, tt := range values {
		result, err := HexStringToByteArray(tt.hex)
		assert.NoError(t, err)
		assert.NotEmpty(t, result)
		assert.Equal(t, tt.expected, result)
	}
}
