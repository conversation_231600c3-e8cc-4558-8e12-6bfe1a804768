[package]
name = "snowbridge-preimage-chopsticks"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
codec = { package = "parity-scale-codec", version = "3.6.1", default-features = false }
serde = { version = "1.0.197", features = ["derive"] }
serde_json = "1.0.114"
sp-crypto-hashing = "0.1.0"
handlebars = "5.1.2"
hex = "0.4.3"

[features]
default = []

