[package]
name = "snowbridge-preimage"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
futures = "0.3.30"
tokio = { version = "1.36.0", features = ["macros", "rt-multi-thread", "time"] }
codec = { package = "parity-scale-codec", version = "3.6.1", default-features = false }
scale-info = { version = "2.9.0", default-features = false, features = [
    "derive",
] }
hex-literal = { version = "0.4.1" }

clap = { version = "4.5.1", features = ["derive"] }

hex = "0.4.3"
subxt = { workspace = true }
serde = { version = "1.0.197", features = ["derive"] }
sp-arithmetic = "24.0.0"
alloy-primitives = "0.6.3"
snowbridge-beacon-primitives = "0.2.0"
snowbridge-router-primitives = "0.2.0"

polkadot-runtime = { path = "../runtimes/polkadot", optional = true }
bridge-hub-polkadot-runtime = { path = "../runtimes/bridge-hub-polkadot", optional = true }
asset-hub-polkadot-runtime = { path = "../runtimes/asset-hub-polkadot", optional = true }

kusama-runtime = { path = "../runtimes/kusama", optional = true }
bridge-hub-kusama-runtime = { path = "../runtimes/bridge-hub-kusama", optional = true }
asset-hub-kusama-runtime = { path = "../runtimes/asset-hub-kusama", optional = true }

paseo-runtime = { path = "../runtimes/paseo", optional = true }
bridge-hub-paseo-runtime = { path = "../runtimes/bridge-hub-paseo", optional = true }
asset-hub-paseo-runtime = { path = "../runtimes/asset-hub-paseo", optional = true }

polkadot-runtime-constants = "3.0.0"

sp-crypto-hashing = "0.1.0"

westend-runtime = { path = "../runtimes/westend", optional = true }
bridge-hub-westend-runtime = { path = "../runtimes/bridge-hub-westend", optional = true }
asset-hub-westend-runtime = { path = "../runtimes/asset-hub-westend", optional = true }
snowbridge-preimage-chopsticks = { path = "../chopsticks" }

serde_json = "1.0.114"

[features]
default = []
kusama = [
    "kusama-runtime",
    "asset-hub-kusama-runtime",
    "bridge-hub-kusama-runtime",
]
polkadot = [
    "polkadot-runtime",
    "asset-hub-polkadot-runtime",
    "bridge-hub-polkadot-runtime",
]
westend = [
    "westend-runtime",
    "asset-hub-westend-runtime",
    "bridge-hub-westend-runtime",
]
paseo = ["paseo-runtime", "asset-hub-paseo-runtime", "bridge-hub-paseo-runtime"]
