# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 4

[[package]]
name = "Inflector"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3"
dependencies = [
 "lazy_static",
 "regex",
]

[[package]]
name = "addr2line"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a76fd60b23679b7d19bd066031410fb7e458ccc5e958eb5c325888ce4baedc97"
dependencies = [
 "gimli 0.27.3",
]

[[package]]
name = "addr2line"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a30b2e23b9e17a9f90641c7ab1549cd9b44f296d3ccbf309d2863cfe398a0cb"
dependencies = [
 "gimli 0.28.1",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array 0.14.7",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "ghash",
 "subtle",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if",
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2969dcb958b36655471fc61f7e416fa76033bdd4bfed0678d8fee1e2d07a1f0"
dependencies = [
 "memchr",
]

[[package]]
name = "allocator-api2"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0942ffc6dcaadf03badf6e6a2d0228460359d5e34b57ccdc720b7382dfbd5ec5"

[[package]]
name = "alloy-primitives"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0628ec0ba5b98b3370bb6be17b12f23bfce8ee4ad83823325a20546d9b03b78"
dependencies = [
 "alloy-rlp",
 "bytes",
 "cfg-if",
 "const-hex",
 "derive_more 0.99.17",
 "hex-literal",
 "itoa",
 "proptest",
 "rand 0.8.5",
 "ruint",
 "serde",
 "tiny-keccak",
]

[[package]]
name = "alloy-primitives"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef197eb250c64962003cb08b90b17f0882c192f4a6f2f544809d424fd7cb0e7d"
dependencies = [
 "alloy-rlp",
 "bytes",
 "cfg-if",
 "const-hex",
 "derive_more 0.99.17",
 "hex-literal",
 "itoa",
 "k256",
 "keccak-asm",
 "proptest",
 "rand 0.8.5",
 "ruint",
 "serde",
 "tiny-keccak",
]

[[package]]
name = "alloy-rlp"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d58d9f5da7b40e9bfff0b7e7816700be4019db97d4b6359fe7f94a9e22e42ac"
dependencies = [
 "arrayvec 0.7.4",
 "bytes",
]

[[package]]
name = "alloy-sol-macro"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a98ad1696a2e17f010ae8e43e9f2a1e930ed176a8e3ff77acfeff6dfb07b42c"
dependencies = [
 "const-hex",
 "dunce",
 "heck 0.4.1",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "syn-solidity",
 "tiny-keccak",
]

[[package]]
name = "alloy-sol-types"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98d7107bed88e8f09f0ddcc3335622d87bfb6821f3e0c7473329fb1cfad5e015"
dependencies = [
 "alloy-primitives 0.4.2",
 "alloy-sol-macro",
 "const-hex",
 "serde",
]

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi",
]

[[package]]
name = "anstream"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96b09b5178381e0874812a9b157f7fe84982617e48f71f4e3235482775e5b540"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8901269c6307e8d93993578286ac0edf7f195079ffff5ebdeea6a59ffb7e36bc"

[[package]]
name = "anstyle-parse"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c75ac65da39e5fe5ab759307499ddad880d724eed2f6ce5b5e8a26f4f387928c"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e28923312444cdd728e4738b3f9c9cac739500909bb3d3c94b43551b16517648"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cd54b81ec8d6180e24654d0b371ad22fc3dd083b6ff8ba325b72e00c87660a7"
dependencies = [
 "anstyle",
 "windows-sys 0.52.0",
]

[[package]]
name = "anyhow"
version = "1.0.96"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b964d184e89d9b6b67dd2715bc8e74cf3107fb2b529990c90cf517326150bf4"

[[package]]
name = "approx"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cab112f0a86d568ea0e627cc1d6be74a1e9cd55214684db5561995f6dad897c6"
dependencies = [
 "num-traits",
]

[[package]]
name = "aquamarine"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1da02abba9f9063d786eab1509833ebb2fac0f966862ca59439c76b9c566760"
dependencies = [
 "include_dir",
 "itertools 0.10.5",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "aquamarine"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21cc1548309245035eb18aa7f0967da6bc65587005170c56e6ef2788a4cf3f4e"
dependencies = [
 "include_dir",
 "itertools 0.10.5",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ark-bls12-377"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb00293ba84f51ce3bd026bd0de55899c4e68f0a39a5728cebae3a73ffdc0a4f"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bls12-377-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20c7021f180a0cbea0380eba97c2af3c57074cdaffe0eef7e840e1c9f2841e55"
dependencies = [
 "ark-bls12-377",
 "ark-ec",
 "ark-models-ext",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bls12-381"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c775f0d12169cba7aae4caeb547bb6a50781c7449a8aa53793827c9ec4abf488"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bls12-381-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1dc4b3d08f19e8ec06e949712f95b8361e43f1391d94f65e4234df03480631c"
dependencies = [
 "ark-bls12-381",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-models-ext",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bw6-761"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e0605daf0cc5aa2034b78d008aaf159f56901d92a52ee4f6ecdfdac4f426700"
dependencies = [
 "ark-bls12-377",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bw6-761-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccee5fba47266f460067588ee1bf070a9c760bf2050c1c509982c5719aadb4f2"
dependencies = [
 "ark-bw6-761",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-models-ext",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-ec"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defd9a439d56ac24968cca0571f598a61bc8c55f71d50a89cda591cb750670ba"
dependencies = [
 "ark-ff 0.4.2",
 "ark-poly",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
 "itertools 0.10.5",
 "num-traits",
 "rayon",
 "zeroize",
]

[[package]]
name = "ark-ed-on-bls12-377"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b10d901b9ac4b38f9c32beacedfadcdd64e46f8d7f8e88c1ae1060022cf6f6c6"
dependencies = [
 "ark-bls12-377",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-ed-on-bls12-377-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524a4fb7540df2e1a8c2e67a83ba1d1e6c3947f4f9342cc2359fc2e789ad731d"
dependencies = [
 "ark-ec",
 "ark-ed-on-bls12-377",
 "ark-ff 0.4.2",
 "ark-models-ext",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-ed-on-bls12-381-bandersnatch"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9cde0f2aa063a2a5c28d39b47761aa102bda7c13c84fc118a61b87c7b2f785c"
dependencies = [
 "ark-bls12-381",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-ed-on-bls12-381-bandersnatch-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d15185f1acb49a07ff8cbe5f11a1adc5a93b19e211e325d826ae98e98e124346"
dependencies = [
 "ark-ec",
 "ark-ed-on-bls12-381-bandersnatch",
 "ark-ff 0.4.2",
 "ark-models-ext",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-ff"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b3235cc41ee7a12aaaf2c575a2ad7b46713a8a50bda2fc3b003a04845c05dd6"
dependencies = [
 "ark-ff-asm 0.3.0",
 "ark-ff-macros 0.3.0",
 "ark-serialize 0.3.0",
 "ark-std 0.3.0",
 "derivative",
 "num-bigint",
 "num-traits",
 "paste",
 "rustc_version 0.3.3",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm 0.4.2",
 "ark-ff-macros 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "digest 0.10.7",
 "itertools 0.10.5",
 "num-bigint",
 "num-traits",
 "paste",
 "rustc_version 0.4.0",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db02d390bf6643fb404d3d22d31aee1c4bc4459600aef9113833d17e786c6e44"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fd794a08ccb318058009eefdf15bcaaaaf6f8161eb3345f907222bac38b20"
dependencies = [
 "num-bigint",
 "num-traits",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-models-ext"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e9eab5d4b5ff2f228b763d38442adc9b084b0a465409b059fac5c2308835ec2"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
]

[[package]]
name = "ark-poly"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d320bfc44ee185d899ccbadfa8bc31aab923ce1558716e1997a1e74057fe86bf"
dependencies = [
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
]

[[package]]
name = "ark-scale"
version = "0.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f69c00b3b529be29528a6f2fd5fa7b1790f8bed81b9cdca17e326538545a179"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "parity-scale-codec",
 "scale-info",
]

[[package]]
name = "ark-serialize"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6c2b318ee6e10f8c2853e73a83adc0ccb88995aa978d8a3408d492ab2ee671"
dependencies = [
 "ark-std 0.3.0",
 "digest 0.9.0",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-serialize-derive",
 "ark-std 0.4.0",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae3281bc6d0fd7e549af32b52511e1302185bd688fd3359fa36423346ff682ea"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-std"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1df2c09229cbc5a028b1d70e00fdb2acee28b1055dfb5ca73eea49c5a25c4e7c"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
 "rayon",
]

[[package]]
name = "array-bytes"
version = "6.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f840fb7195bcfc5e17ea40c26e5ce6d5b9ce5d584466e17703209657e459ae0"

[[package]]
name = "arrayref"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b4930d2cb77ce62f89ee5d5289b4ac049559b1c45539271f5ed4fdc7db34545"

[[package]]
name = "arrayvec"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd9fd44efafa8690358b7408d253adf110036b88f55672a933f01d616ad9b1b9"
dependencies = [
 "nodrop",
]

[[package]]
name = "arrayvec"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23b62fc65de8e4e7f52534fb52b0f3ed04746ae267519eef2a83941e8085068b"

[[package]]
name = "arrayvec"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96d30a06541fbafbc7f82ed10c06164cfbd2c401138f6addd8404629c4b16711"

[[package]]
name = "assert_matches"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b34d609dfbaf33d6889b2b7106d3ca345eacad44200913df5ba02bfd31d2ba9"

[[package]]
name = "asset-hub-kusama-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "asset-hub-paseo-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "asset-hub-polkadot-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "asset-hub-westend-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "asset-test-utils"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0324df9ce91a9840632e865dd3272bd20162023856f1b189b7ae58afa5c6b61"
dependencies = [
 "cumulus-pallet-parachain-system",
 "cumulus-pallet-xcmp-queue",
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-assets",
 "pallet-balances 39.0.1",
 "pallet-collator-selection",
 "pallet-session 38.0.0",
 "pallet-timestamp 37.0.0",
 "pallet-xcm",
 "pallet-xcm-bridge-hub-router",
 "parachains-common",
 "parachains-runtimes-test-utils",
 "parity-scale-codec",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-parachain-info",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "substrate-wasm-builder",
]

[[package]]
name = "assets-common"
version = "0.18.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c540587f89a03003946b14decef4fcadb083edc4e62f968de245b82e5402e923"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-asset-conversion",
 "pallet-assets",
 "pallet-xcm",
 "parachains-common",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "substrate-wasm-builder",
]

[[package]]
name = "async-channel"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b47800b0be77592da0afd425cc03468052844aff33b84e33cc696f64e77b6a"
dependencies = [
 "concurrent-queue",
 "event-listener-strategy 0.5.3",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-executor"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ae5ebefcc48e7452b4987947920dac9450be1110cadf34d1b8c116bdbaf97c"
dependencies = [
 "async-lock 3.3.0",
 "async-task",
 "concurrent-queue",
 "fastrand",
 "futures-lite",
 "slab",
]

[[package]]
name = "async-fs"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc19683171f287921f2405677dd2ed2549c3b3bda697a563ebc3a121ace2aba1"
dependencies = [
 "async-lock 3.3.0",
 "blocking",
 "futures-lite",
]

[[package]]
name = "async-io"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f97ab0c5b00a7cdbe5a371b9a782ee7be1316095885c8a4ea1daf490eb0ef65"
dependencies = [
 "async-lock 3.3.0",
 "cfg-if",
 "concurrent-queue",
 "futures-io",
 "futures-lite",
 "parking",
 "polling",
 "rustix 0.38.31",
 "slab",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "async-lock"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "287272293e9d8c41773cec55e365490fe034813a2f172f502d6ddcf75b2f582b"
dependencies = [
 "event-listener 2.5.3",
]

[[package]]
name = "async-lock"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d034b430882f8381900d3fe6f0aaa3ad94f2cb4ac519b429692a1bc2dda4ae7b"
dependencies = [
 "event-listener 4.0.3",
 "event-listener-strategy 0.4.0",
 "pin-project-lite",
]

[[package]]
name = "async-net"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b948000fad4873c1c9339d60f2623323a0cfd3816e5181033c6a5cb68b2accf7"
dependencies = [
 "async-io",
 "blocking",
 "futures-lite",
]

[[package]]
name = "async-process"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "451e3cf68011bd56771c79db04a9e333095ab6349f7e47592b788e9b98720cc8"
dependencies = [
 "async-channel",
 "async-io",
 "async-lock 3.3.0",
 "async-signal",
 "blocking",
 "cfg-if",
 "event-listener 5.4.0",
 "futures-lite",
 "rustix 0.38.31",
 "windows-sys 0.52.0",
]

[[package]]
name = "async-signal"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e47d90f65a225c4527103a8d747001fc56e375203592b25ad103e1ca13124c5"
dependencies = [
 "async-io",
 "async-lock 2.8.0",
 "atomic-waker",
 "cfg-if",
 "futures-core",
 "futures-io",
 "rustix 0.38.31",
 "signal-hook-registry",
 "slab",
 "windows-sys 0.48.0",
]

[[package]]
name = "async-task"
version = "4.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbb36e985947064623dbd357f727af08ffd077f93d696782f3c56365fa2e2799"

[[package]]
name = "async-trait"
version = "0.1.86"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "644dd749086bf3771a2fbc5f256fdb982d53f011c7d5d560304eafeecebce79d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "atomic-take"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8ab6b55fe97976e46f91ddbed8d147d966475dc29b2032757ba47e02376fbc3"

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "auto_impl"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "823b8bb275161044e2ac7a25879cb3e2480cb403e3943022c7c769c599b756aa"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "autocfg"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"

[[package]]
name = "backtrace"
version = "0.3.71"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b05800d2e817c8b3b4b54abd461726265fa9789ae34330622f2db9ee696f9d"
dependencies = [
 "addr2line 0.21.0",
 "cc",
 "cfg-if",
 "libc",
 "miniz_oxide",
 "object 0.32.2",
 "rustc-demangle",
]

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base58"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6107fe1be6682a68940da878d9e9f5e90ca5745b3dec9fd1bb393c8777d4f581"

[[package]]
name = "base64"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64ct"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c3c1a368f70d6cf7302d78f8f7093da241fb8e8807c05cc9e51a125895a6d5b"

[[package]]
name = "binary-merkle-tree"
version = "15.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "336bf780dd7526a9a4bc1521720b25c1994dc132cccd59553431923fa4d1a693"
dependencies = [
 "hash-db",
 "log",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bip39"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93f2635620bf0b9d4576eb7bb9a38a55df78bd1205d26fa994b25911a69f212f"
dependencies = [
 "bitcoin_hashes 0.11.0",
 "rand 0.8.5",
 "rand_core 0.6.4",
 "serde",
 "unicode-normalization",
]

[[package]]
name = "bit-set"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0700ddab506f33b20a03b13996eccd309a48e5ff77d0d95926aa0210fb4e95f1"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349f9b6a179ed607305526ca489b34ad0a41aed5f7980fa90eb03160b69598fb"

[[package]]
name = "bitcoin-internals"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9425c3bf7089c983facbae04de54513cce73b41c7f9ff8c845b54e7bc64ebbfb"

[[package]]
name = "bitcoin_hashes"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90064b8dee6815a6470d60bad07bbbaee885c0e12d04177138fa3291a01b7bc4"

[[package]]
name = "bitcoin_hashes"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1930a4dabfebb8d7d9992db18ebe3ae2876f0a305fab206fd168df931ede293b"
dependencies = [
 "bitcoin-internals",
 "hex-conservative",
]

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f68f53c83ab957f72c32642f3868eec03eb974d1fb82e453128456482613d36"

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "serde",
 "tap",
 "wyz",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "blake2-rfc"
version = "0.2.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d6d530bdd2d52966a6d03b7a964add7ae1a288d25214066fd4b600f0f796400"
dependencies = [
 "arrayvec 0.4.12",
 "constant_time_eq 0.1.5",
]

[[package]]
name = "blake2b_simd"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23285ad32269793932e830392f2fe2f83e26488fd3ec778883a93c8323735780"
dependencies = [
 "arrayref",
 "arrayvec 0.7.4",
 "constant_time_eq 0.3.0",
]

[[package]]
name = "block-buffer"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0940dc441f31689269e10ac70eb1002a3a1d3ad1390e030043662eb7fe4688b"
dependencies = [
 "block-padding",
 "byte-tools",
 "byteorder",
 "generic-array 0.12.4",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-padding"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa79dedbb091f449f1f39e53edf88d5dbe95f895dae6135a8d7b881fb5af73f5"
dependencies = [
 "byte-tools",
]

[[package]]
name = "blocking"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a37913e8dc4ddcc604f0c6d3bf2887c995153af3611de9e23c352b44c1b9118"
dependencies = [
 "async-channel",
 "async-lock 3.3.0",
 "async-task",
 "fastrand",
 "futures-io",
 "futures-lite",
 "piper",
 "tracing",
]

[[package]]
name = "bounded-collections"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca548b6163b872067dc5eb82fd130c56881435e30367d2073594a3d9744120dd"
dependencies = [
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "bounded-collections"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ad8a0bed7827f0b07a5d23cec2e58cc02038a99e4ca81616cb2bb2025f804d"
dependencies = [
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "bp-header-chain"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "890df97cea17ee61ff982466bb9e90cb6b1462adb45380999019388d05e4b92d"
dependencies = [
 "bp-runtime",
 "finality-grandpa",
 "frame-support 38.2.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-consensus-grandpa",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-messages"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7efabf94339950b914ba87249497f1a0e35a73849934d164fecae4b275928cf6"
dependencies = [
 "bp-header-chain",
 "bp-runtime",
 "frame-support 38.2.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-parachains"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9011e5c12c15caf3c4129a98f4f4916ea9165db8daf6ed85867c3106075f40df"
dependencies = [
 "bp-header-chain",
 "bp-polkadot-core",
 "bp-runtime",
 "frame-support 38.2.0",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-polkadot"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa6277dd4333917ecfbcc35e9332a9f11682e0a506e76b617c336224660fce33"
dependencies = [
 "bp-header-chain",
 "bp-polkadot-core",
 "bp-runtime",
 "frame-support 38.2.0",
 "sp-api 34.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-polkadot-core"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "345cf472bac11ef79d403e4846a666b7d22a13cd16d9c85b62cd6b5e16c4a042"
dependencies = [
 "bp-messages",
 "bp-runtime",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "parity-util-mem",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-relayers"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9465ad727e466d67d64244a1aa7bb19933a297913fdde34b8e9bda0a341bdeb"
dependencies = [
 "bp-header-chain",
 "bp-messages",
 "bp-parachains",
 "bp-runtime",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-utility",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "bp-runtime"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "746d9464f912b278f8a5e2400f10541f95da7fc6c7d688a2788b9a46296146ee"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "hash-db",
 "impl-trait-for-tuples",
 "log",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-state-machine 0.43.0",
 "sp-std 14.0.0",
 "sp-trie 37.0.0",
 "trie-db 0.29.1",
]

[[package]]
name = "bp-test-utils"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92e659078b54c0b6bd79896738212a305842ad37168976363233516754337826"
dependencies = [
 "bp-header-chain",
 "bp-parachains",
 "bp-polkadot-core",
 "bp-runtime",
 "ed25519-dalek",
 "finality-grandpa",
 "parity-scale-codec",
 "sp-application-crypto 38.0.0",
 "sp-consensus-grandpa",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "bp-xcm-bridge-hub"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0873c54562b3d492541cbc8a7974c6854a5157d07880a2a71f8ba888a69e17e9"
dependencies = [
 "bp-messages",
 "bp-runtime",
 "frame-support 38.2.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
]

[[package]]
name = "bp-xcm-bridge-hub-router"
version = "0.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9284820ca704f5c065563cad77d2e3d069a23cc9cb3a29db9c0de8dd3b173a87"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
]

[[package]]
name = "bridge-hub-common"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b53c53d627e2da38f8910807944bf3121e154b5c0ac9e122995af9dfb13ed"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "pallet-message-queue 41.0.2",
 "parity-scale-codec",
 "scale-info",
 "snowbridge-core 0.10.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
]

[[package]]
name = "bridge-hub-kusama-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.2.0",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "bridge-hub-paseo-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.2.0",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "bridge-hub-polkadot-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.2.0",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "bridge-hub-test-utils"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de0b3aa5fd8481a06ca16e47fd3d2d9c6abe76b27d922ec8980a853f242173b3"
dependencies = [
 "asset-test-utils",
 "bp-header-chain",
 "bp-messages",
 "bp-parachains",
 "bp-polkadot-core",
 "bp-relayers",
 "bp-runtime",
 "bp-test-utils",
 "bp-xcm-bridge-hub",
 "bridge-runtime-common",
 "cumulus-pallet-parachain-system",
 "cumulus-pallet-xcmp-queue",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-balances 39.0.1",
 "pallet-bridge-grandpa",
 "pallet-bridge-messages",
 "pallet-bridge-parachains",
 "pallet-bridge-relayers",
 "pallet-timestamp 37.0.0",
 "pallet-utility",
 "pallet-xcm",
 "pallet-xcm-bridge-hub",
 "parachains-common",
 "parachains-runtimes-test-utils",
 "parity-scale-codec",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-keyring",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "bridge-hub-westend-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.2.0",
 "sp-arithmetic 24.0.0",
 "subxt",
]

[[package]]
name = "bridge-runtime-common"
version = "0.18.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "789eb7841c8791991317ec4b6e56c119e5e1c2e480ad293b8502736fd7f64b2e"
dependencies = [
 "bp-header-chain",
 "bp-messages",
 "bp-parachains",
 "bp-polkadot-core",
 "bp-relayers",
 "bp-runtime",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-bridge-grandpa",
 "pallet-bridge-messages",
 "pallet-bridge-parachains",
 "pallet-bridge-relayers",
 "pallet-transaction-payment 38.0.2",
 "pallet-utility",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "sp-trie 37.0.0",
 "staging-xcm 14.2.0",
 "tuplex",
]

[[package]]
name = "bs58"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf88ba1141d185c399bee5288d850d63b8369520c1eafc32a0430b5b6c287bf4"
dependencies = [
 "tinyvec",
]

[[package]]
name = "build-helper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdce191bf3fa4995ce948c8c83b4640a1745457a149e73c6db75b4ffe36aad5f"
dependencies = [
 "semver 0.6.0",
]

[[package]]
name = "bumpalo"
version = "3.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea184aa71bb362a1157c896979544cc23974e08fd265f29ea96b59f0b4a555b"

[[package]]
name = "byte-slice-cast"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3ac9f8b63eca6fd385229b3675f6cc0dc5c8a5c8a54a59d4f52ffd670d87b0c"

[[package]]
name = "byte-tools"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3b5ca7a04898ad4bcd41c90c5285445ff5b791899bb1b0abdd2a2aa791211d7"

[[package]]
name = "bytemuck"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78834c15cb5d5efe3452d58b1e8ba890dd62d21907f867f383358198e56ebca5"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2bd12c1caf447e69cd4528f47f94d203fd2582878ecb9e9465484c4148a8223"

[[package]]
name = "camino"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b96ec4966b5813e2c0507c1f86115c8c5abaadc3980879c3424042a02fd1ad3"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eee4243f1f26fc7a42710e7439c149e2b10b05472f88090acce52632f231a73a"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cc"
version = "1.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fcb57c740ae1daf453ae85f16e37396f672b039e00d9d866e07ddb24e328e3a"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cesu8"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d43a04d8753f35258c91f8ec639f792891f748a1edbd759cf1dcea3382ad83c"

[[package]]
name = "cfg-expr"
version = "0.15.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa50868b64a9a6fda9d593ce778849ea8715cd2a3d2cc17ffdb4a2f2f2f1961d"
dependencies = [
 "smallvec",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "chacha20"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3613f74bd2eac03dad61bd53dbe620703d4371614fe0bc3b9f04dd36fe4e818"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "chrono"
version = "0.4.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bc015644b92d5890fab7489e49d21f879d5c990186827d42ec511919404f38b"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "num-traits",
 "windows-targets 0.52.6",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
]

[[package]]
name = "clap"
version = "4.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c918d541ef2913577a0f9566e9ce27cb35b6df072075769e0b26cb5a554520da"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f3e7391dad68afb0c2ede1bf619f579a3dc9c2ec67f089baa397123a2f3d1eb"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
]

[[package]]
name = "clap_derive"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307bc0538d5f0f83b8248db3087aa92fe504e4691294d0c96c0eabc33f47ba47"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "clap_lex"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98cc8fbded0c607b7ba9dd60cd98df59af97e84d24e49c8557331cfc26d301ce"

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width 0.1.14",
]

[[package]]
name = "colorchoice"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acbf1af155f9b9ef647e42cdc158db4b64a1b61f743629225fde6f3e0be2a7c7"

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes",
 "memchr",
]

[[package]]
name = "common-path"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2382f75942f4b3be3690fe4f86365e9c853c1587d6ee58212cebf6e2a9ccd101"

[[package]]
name = "concurrent-queue"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ca0197aee26d1ae37445ee532fefce43251d24cc7c166799f4d46817f1d3973"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "console"
version = "0.15.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea3c6ecd8059b57859df5c69830340ed3c41d30e3da0c1cbed90a96ac853041b"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width 0.2.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "const-hex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efbd12d49ab0eaf8193ba9175e45f56bbc2e4b27d57b8cfe62aa47942a46b9a9"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "hex",
 "proptest",
 "serde",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-random"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87e00182fe74b066627d63b85fd550ac2998d4b0bd86bfed477a0ae4c7c71359"
dependencies = [
 "const-random-macro",
]

[[package]]
name = "const-random-macro"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d839f2a20b0aee515dc581a6172f2321f96cab76c1a38a4c584a194955390e"
dependencies = [
 "getrandom 0.2.12",
 "once_cell",
 "tiny-keccak",
]

[[package]]
name = "const_format"
version = "0.2.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126f97965c8ad46d6d9163268ff28432e8f6a1196a55578867832e3049df63dd"
dependencies = [
 "const_format_proc_macros",
]

[[package]]
name = "const_format_proc_macros"
version = "0.2.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d57c2eccfb16dbac1f4e61e206105db5820c9d26c3c472bc17c774259ef7744"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-xid",
]

[[package]]
name = "constant_time_eq"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "245097e9a4535ee1e3e3931fcfcd55a796a44c643e8596ff6566d68f09b87bbc"

[[package]]
name = "constant_time_eq"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7144d30dcf0fafbce74250a3963025d8d52177934239851c917d29f1df280c2"

[[package]]
name = "constcat"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd7e35aee659887cbfb97aaf227ac12cad1a9d7c71e55ff3376839ed4e282d08"

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06ea2b9bc92be3c2baa9334a323ebca2d6f074ff852cd1d7b11064035cd3868f"

[[package]]
name = "cpp_demangle"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeaa953eaad386a53111e47172c2fedba671e5684c8dd601a5f474f4f118710f"
dependencies = [
 "cfg-if",
]

[[package]]
name = "cpufeatures"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53fe5e26ff1b7aef8bca9c6080520cfb8d9333c7568e1829cef191a9723e5504"
dependencies = [
 "libc",
]

[[package]]
name = "cranelift-bforest"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1277fbfa94bc82c8ec4af2ded3e639d49ca5f7f3c7eeab2c66accd135ece4e70"
dependencies = [
 "cranelift-entity",
]

[[package]]
name = "cranelift-codegen"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6e8c31ad3b2270e9aeec38723888fe1b0ace3bea2b06b3f749ccf46661d3220"
dependencies = [
 "bumpalo",
 "cranelift-bforest",
 "cranelift-codegen-meta",
 "cranelift-codegen-shared",
 "cranelift-entity",
 "cranelift-isle",
 "gimli 0.27.3",
 "hashbrown 0.13.2",
 "log",
 "regalloc2 0.6.1",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-codegen-meta"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8ac5ac30d62b2d66f12651f6b606dbdfd9c2cfd0908de6b387560a277c5c9da"
dependencies = [
 "cranelift-codegen-shared",
]

[[package]]
name = "cranelift-codegen-shared"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd82b8b376247834b59ed9bdc0ddeb50f517452827d4a11bccf5937b213748b8"

[[package]]
name = "cranelift-entity"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40099d38061b37e505e63f89bab52199037a72b931ad4868d9089ff7268660b0"
dependencies = [
 "serde",
]

[[package]]
name = "cranelift-frontend"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64a25d9d0a0ae3079c463c34115ec59507b4707175454f0eee0891e83e30e82d"
dependencies = [
 "cranelift-codegen",
 "log",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-isle"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80de6a7d0486e4acbd5f9f87ec49912bf4c8fb6aea00087b989685460d4469ba"

[[package]]
name = "cranelift-native"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6b03e0e03801c4b3fd8ce0758a94750c07a44e7944cc0ffbf0d3f2e7c79b00"
dependencies = [
 "cranelift-codegen",
 "libc",
 "target-lexicon",
]

[[package]]
name = "cranelift-wasm"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff3220489a3d928ad91e59dd7aeaa8b3de18afb554a6211213673a71c90737ac"
dependencies = [
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "itertools 0.10.5",
 "log",
 "smallvec",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "crc32fast"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3855a8a784b474f333699ef2bbca9db2c4a1f6d9088a90a2d25b1eb53111eaa"
dependencies = [
 "cfg-if",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df0346b5d5e76ac2fe4e327c5fd1118d6be7c51dfb18f9b7922923f287471e35"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "248e3bacc7dc6baa3b21e405ee045c3047101a49145e7e9eca583ab4c2ca5345"

[[package]]
name = "crunchy"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a81dae078cea95a014a339291cec439d2f232ebe854a9d672b796c6afafa9b7"

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b584a330336237c1eecd3e94266efb216c56ed91225d634cb2991c5f3fd1aeab"
dependencies = [
 "generic-array 0.14.7",
 "subtle",
]

[[package]]
name = "crypto-mac"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25fab6889090c8133f3deb8f73ba3c65a7f456f66436fc012a1b1e272b1e103e"
dependencies = [
 "generic-array 0.14.7",
 "subtle",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "cumulus-pallet-aura-ext"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2cbe2735fc7cf2b6521eab00cb1a1ab025abc1575cc36887b36dc8c5cb1c9434"
dependencies = [
 "cumulus-pallet-parachain-system",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-aura",
 "pallet-timestamp 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-consensus-aura",
 "sp-runtime 39.0.5",
]

[[package]]
name = "cumulus-pallet-dmp-queue"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97263a8e758d201ebe81db7cea7b278b4fb869c11442f77acef70138ac1a252f"
dependencies = [
 "cumulus-primitives-core",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
]

[[package]]
name = "cumulus-pallet-parachain-system"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "546403ee1185f4051a74cc9c9d76e82c63cac3fb68e1bf29f61efb5604c96488"
dependencies = [
 "bytes",
 "cumulus-pallet-parachain-system-proc-macro",
 "cumulus-primitives-core",
 "cumulus-primitives-parachain-inherent",
 "cumulus-primitives-proof-size-hostfunction",
 "environmental",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-message-queue 41.0.2",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-runtime-common 17.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "scale-info",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-state-machine 0.43.0",
 "sp-std 14.0.0",
 "sp-trie 37.0.0",
 "sp-version 37.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "trie-db 0.29.1",
]

[[package]]
name = "cumulus-pallet-parachain-system-proc-macro"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "befbaf3a1ce23ac8476481484fef5f4d500cbd15b4dad6380ce1d28134b0c1f7"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "cumulus-pallet-session-benchmarking"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18168570689417abfb514ac8812fca7e6429764d01942750e395d7d8ce0716ef"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "sp-runtime 39.0.5",
]

[[package]]
name = "cumulus-pallet-solo-to-para"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42c74548c8cab75da6f2479a953f044b582cfce98479862344a24df7bbd215"
dependencies = [
 "cumulus-pallet-parachain-system",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-sudo",
 "parity-scale-codec",
 "polkadot-primitives 16.0.0",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "cumulus-pallet-xcm"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e49231f6cd8274438b078305dc8ce44c54c0d3f4a28e902589bcbaa53d954608"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
]

[[package]]
name = "cumulus-pallet-xcmp-queue"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbc44222c528b88dcc6e921e7a0dc94d66b5895aab9e9d9db8798fc62f7ccd40"
dependencies = [
 "bounded-collections 0.2.4",
 "bp-xcm-bridge-hub-router",
 "cumulus-primitives-core",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-message-queue 41.0.2",
 "parity-scale-codec",
 "polkadot-runtime-common 17.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "cumulus-ping"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f47128f797359951723e2d106a80e592d007bb7446c299958cdbafb1489ddbf0"
dependencies = [
 "cumulus-pallet-xcm",
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
]

[[package]]
name = "cumulus-primitives-aura"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11e7825bcf3cc6c962a5b9b9f47e02dc381109e521d0bc00cad785c65da18471"
dependencies = [
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "polkadot-primitives 15.0.0",
 "sp-api 34.0.0",
 "sp-consensus-aura",
 "sp-runtime 39.0.5",
]

[[package]]
name = "cumulus-primitives-core"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c6b5221a4a3097f2ebef66c84c1e6d7a0b8ec7e63f2bd5ae04c1e6d3fc7514e"
dependencies = [
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "scale-info",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
 "sp-trie 37.0.0",
 "staging-xcm 14.2.0",
]

[[package]]
name = "cumulus-primitives-parachain-inherent"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "842a694901e04a62d88995418dec35c22f7dba2b34d32d2b8de37d6b92f973ff"
dependencies = [
 "async-trait",
 "cumulus-primitives-core",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "cumulus-primitives-proof-size-hostfunction"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "421f03af054aac7c89e87a49e47964886e53a8d7395990eab27b6f201d42524f"
dependencies = [
 "sp-externalities 0.29.0",
 "sp-runtime-interface 28.0.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "cumulus-primitives-storage-weight-reclaim"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fc49dfec0ba3438afad73787736cc0dba88d15b5855881f12a4d8b812a72927"
dependencies = [
 "cumulus-primitives-core",
 "cumulus-primitives-proof-size-hostfunction",
 "docify",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "cumulus-primitives-timestamp"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33cffb8f010f39ac36b31d38994b8f9d9256d9b5e495d96b4ec59d3e30852d53"
dependencies = [
 "cumulus-primitives-core",
 "sp-inherents 34.0.0",
 "sp-timestamp 34.0.0",
]

[[package]]
name = "cumulus-primitives-utility"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bdcf4d46dd93f1e6d5dd6d379133566a44042ba6476d04bdcbdb4981c622ae4"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "log",
 "pallet-asset-conversion",
 "parity-scale-codec",
 "polkadot-runtime-common 17.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "cumulus-test-relay-sproof-builder"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e570e41c3f05a8143ebff967bbb0c7dcaaa6f0bebd8639b9418b8005b13eda03"
dependencies = [
 "cumulus-primitives-core",
 "parity-scale-codec",
 "polkadot-primitives 16.0.0",
 "sp-runtime 39.0.5",
 "sp-state-machine 0.43.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "curve25519-dalek"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b85542f99a2dfa2a1b8e192662741c9859a846b296bef1c92ef9b58b5a216"
dependencies = [
 "byteorder",
 "digest 0.8.1",
 "rand_core 0.5.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b9fdf9972b2bd6af2d913799d9ebc165ea4d2e65878e329d9c6b372c4491b61"
dependencies = [
 "byteorder",
 "digest 0.9.0",
 "rand_core 0.5.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek"
version = "4.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fb8b7c4503de7d6ae7b42ab72a5a59857b4c937ec27a3d4539dba95b5ab2be"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "curve25519-dalek-derive",
 "digest 0.10.7",
 "fiat-crypto",
 "rustc_version 0.4.0",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46882e17999c6cc590af592290432be3bce0428cb0d5f8b6715e4dc7b383eb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "cxx"
version = "1.0.141"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bc580dceb395cae0efdde0a88f034cfd8a276897e40c693a7b87bed17971d33"
dependencies = [
 "cc",
 "cxxbridge-cmd",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "foldhash",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.141"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49d8c1baedad72a7efda12ad8d7ad687b3e7221dfb304a12443fd69e9de8bb30"
dependencies = [
 "cc",
 "codespan-reporting",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.98",
]

[[package]]
name = "cxxbridge-cmd"
version = "1.0.141"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e43afb0e3b2ef293492a31ecd796af902112460d53e5f923f7804f348a769f9c"
dependencies = [
 "clap",
 "codespan-reporting",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.141"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0257ad2096a2474fe877e9e055ab69603851c3d6b394efcc7e0443899c2492ce"

[[package]]
name = "cxxbridge-macro"
version = "1.0.141"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b46cbd7358a46b760609f1cb5093683328e58ca50e594a308716f5403fdc03e5"
dependencies = [
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.98",
]

[[package]]
name = "darling"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f63b86c8a8826a49b8c21f08a2d07338eec8d900540f8630dc76284be802989"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95133861a8032aaea082871032f5815eb9e98cef03fa916ab4500513994df9e5"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim",
 "syn 2.0.98",
]

[[package]]
name = "darling_macro"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d336a2a514f6ccccaa3e09b02d41d35330c07ddf03a62165fcec10bb561c7806"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "der"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fffa369a668c8af7dbf8b5e56c9f744fbd399949ed171606040001947de40b1c"
dependencies = [
 "const-oid",
 "zeroize",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-syn-parse"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79116f119dd1dba1abf1f3405f03b9b0e79a27a3883864bfebded8a3dc768cd"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-syn-parse"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d65d7ce8132b7c0e54497a4d9a55a1c2a0912a0d786cf894472ba818fba45762"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "derive-where"
version = "1.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62d671cc41a825ebabc75757b62d3d168c577f9149b2d49ece1dad1f72119d25"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "derive_more"
version = "0.99.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fb810d30a7c1953f91334de7244731fc3f3c10d7fe163338a35b9f640960321"
dependencies = [
 "convert_case",
 "proc-macro2",
 "quote",
 "rustc_version 0.4.0",
 "syn 1.0.109",
]

[[package]]
name = "derive_more"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b99b9cbbe49445b21764dc0625032a89b145a2642e67603e1c936f5458d05"
dependencies = [
 "derive_more-impl",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7330aeadfbe296029522e6c40f315320aba36fc43a5b3632f3795348f3bd22"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "unicode-xid",
]

[[package]]
name = "digest"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3d0c8c8752312f9713efd397ff63acb9f85585afbf179282e720e7704954dd5"
dependencies = [
 "generic-array 0.12.4",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "directories-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339ee130d97a610ea5a5872d2bbb130fdf68884ff09d3028b81bec8a1ac23bbc"
dependencies = [
 "cfg-if",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "docify"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a772b62b1837c8f060432ddcc10b17aae1453ef17617a99bc07789252d2a5896"
dependencies = [
 "docify_macros",
]

[[package]]
name = "docify_macros"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60e6be249b0a462a14784a99b19bf35a667bb5e09de611738bb7362fa4c95ff7"
dependencies = [
 "common-path",
 "derive-syn-parse 0.2.0",
 "once_cell",
 "proc-macro2",
 "quote",
 "regex",
 "syn 2.0.98",
 "termcolor",
 "toml 0.8.12",
 "walkdir",
]

[[package]]
name = "downcast-rs"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ea835d29036a4087793836fa931b08837ad5e957da9e23886b29586fb9b6650"

[[package]]
name = "dunce"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92773504d58c093f6de2459af4af33faa518c13451eb8f2b5698ed3d36e7c813"

[[package]]
name = "dyn-clonable"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e9232f0e607a262ceb9bd5141a3dfb3e4db6994b31989bbfd845878cba59fd4"
dependencies = [
 "dyn-clonable-impl",
 "dyn-clone",
]

[[package]]
name = "dyn-clonable-impl"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "558e40ea573c374cf53507fd240b7ee2f5477df7cfebdb97323ec61c719399c5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "dyn-clone"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "545b22097d44f8a9581187cdf93de7a71e4722bf51200cfaba810865b49a495d"

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der",
 "digest 0.10.7",
 "elliptic-curve",
 "rfc6979",
 "serdect",
 "signature",
 "spki",
]

[[package]]
name = "ed25519"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "115531babc129696a58c64a4fef0a8bf9e9698629fb97e9e40767d235cfbcd53"
dependencies = [
 "pkcs8",
 "signature",
]

[[package]]
name = "ed25519-dalek"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a3daa8e81a3963a60642bcc1f90a670680bd4a77535faa384e9d1c79d620871"
dependencies = [
 "curve25519-dalek 4.1.3",
 "ed25519",
 "serde",
 "sha2 0.10.8",
 "subtle",
 "zeroize",
]

[[package]]
name = "ed25519-zebra"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c24f403d068ad0b359e577a77f92392118be3f3c927538f2bb544a5ecd828c6"
dependencies = [
 "curve25519-dalek 3.2.0",
 "hashbrown 0.12.3",
 "hex",
 "rand_core 0.6.4",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "ed25519-zebra"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d9ce6874da5d4415896cd45ffbc4d1cfc0c4f9c079427bd870742c30f2f65a9"
dependencies = [
 "curve25519-dalek 4.1.3",
 "ed25519",
 "hashbrown 0.14.5",
 "hex",
 "rand_core 0.6.4",
 "sha2 0.10.8",
 "zeroize",
]

[[package]]
name = "either"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7914353092ddf589ad78f25c5c1c21b7f80b0ff8621e7c814c3485b5306da9d"

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct",
 "crypto-bigint",
 "digest 0.10.7",
 "ff",
 "generic-array 0.14.7",
 "group",
 "pkcs8",
 "rand_core 0.6.4",
 "sec1",
 "serdect",
 "subtle",
 "zeroize",
]

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "enumflags2"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba2f4b465f5318854c6f8dd686ede6c0a9dc67d4b1ac241cf0eb51521a309147"
dependencies = [
 "enumflags2_derive",
]

[[package]]
name = "enumflags2_derive"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc4caf64a58d7a6d65ab00639b046ff54399a39f5f2554728895ace4b297cd79"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "enumn"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fd000fd6988e73bbe993ea3db9b1aa64906ab88766d654973924340c8cddb42"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "env_logger"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd405aab171cb85d6735e5c8d9db038c17d3ca007a4d2c25f337935c3d90580"
dependencies = [
 "humantime",
 "is-terminal",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "environmental"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48c92028aaa870e83d51c64e5d4e0b6981b360c522198c23959f219a4e1b15b"

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "errno"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a258e46cdc063eb8519c00b9fc845fc47bcfca4130e2f08e88665ceda8474245"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "ethabi-decode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09d398648d65820a727d6a81e58b962f874473396a047e4c30bafe3240953417"
dependencies = [
 "ethereum-types",
 "tiny-keccak",
]

[[package]]
name = "ethbloom"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c22d4b5885b6aa2fe5e8b9329fb8d232bf739e434e6b87347c63bdd00c120f60"
dependencies = [
 "crunchy",
 "fixed-hash",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "scale-info",
 "tiny-keccak",
]

[[package]]
name = "ethereum-types"
version = "0.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02d215cbf040552efcbe99a38372fe80ab9d00268e20012b79fcd0f073edd8ee"
dependencies = [
 "ethbloom",
 "fixed-hash",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "primitive-types 0.12.2",
 "scale-info",
 "uint 0.9.5",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67b215c49b2b248c855fb73579eb1f4f26c38ffdc12973e20e07b91d78d5646e"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3492acde4c3fc54c845eaab3eed8bd00c7a7d881f78bfc801e43a93dec1331ae"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "958e4d70b6d5e81971bebec42271ec641e7ff4e170a6fa605f2b8a8b65cb97d3"
dependencies = [
 "event-listener 4.0.3",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c3e4e0dd3673c1139bf041f3008816d9cf2946bbfac2945c09e523b8d7b05b2"
dependencies = [
 "event-listener 5.4.0",
 "pin-project-lite",
]

[[package]]
name = "expander"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f86a749cf851891866c10515ef6c299b5c69661465e9c3bbe7e07a2b77fb0f7"
dependencies = [
 "blake2",
 "fs-err",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "fake-simd"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e88a8acf291dafb59c2d96e8f59828f3838bb1a70398823ade51a84de6a6deed"

[[package]]
name = "fallible-iterator"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4443176a9f2c162692bd3d352d745ef9413eec5782a80d8fd6f8a1ac692a07f7"

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fastrand"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25cbce373ec4653f1a01a31e8a5e5ec0c622dc27ff9c4e6606eefef5cbbed4a5"

[[package]]
name = "fastrlp"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "139834ddba373bbdd213dffe02c8d110508dcf1726c2be27e8d1f7d7e1856418"
dependencies = [
 "arrayvec 0.7.4",
 "auto_impl",
 "bytes",
]

[[package]]
name = "ff"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ded41244b729663b1e574f1b4fb731469f69f79c17667b5d776b16cda0479449"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "fiat-crypto"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1676f435fc1dadde4d03e43f5d62b259e1ce5f40bd4ffb21db2b42ebe59c1382"

[[package]]
name = "file-per-thread-logger"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84f2e425d9790201ba4af4630191feac6dcc98765b118d4d18e91d23c2353866"
dependencies = [
 "env_logger",
 "log",
]

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "finality-grandpa"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4f8f43dc520133541781ec03a8cab158ae8b7f7169cdf22e9050aa6cf0fbdfc"
dependencies = [
 "either",
 "futures",
 "futures-timer",
 "log",
 "num-traits",
 "parity-scale-codec",
 "parking_lot",
 "scale-info",
]

[[package]]
name = "fixed-hash"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835c052cb0c08c1acf6ffd71c022172e18723949c8282f2b9f27efbc51e64534"
dependencies = [
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0d2fde1f7b3d48b8395d5f2de76c18a528bd6a9cdde438df747bfcba3e05d6f"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "frame-benchmarking"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a01af5751a0e4492dc979c57586976403e7ab63641add1a9fd804cad4169f4f6"
dependencies = [
 "frame-support 24.0.0",
 "frame-support-procedural 19.0.0",
 "frame-system 24.0.0",
 "linregress",
 "log",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "sp-api 22.0.0",
 "sp-application-crypto 26.0.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-runtime-interface 20.0.0",
 "sp-std 11.0.0",
 "sp-storage 16.0.0",
 "static_assertions",
]

[[package]]
name = "frame-benchmarking"
version = "30.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34134abd64876c2cba150b703d8c74b1b222147e61dbc33cbb9db72f7c1cdb2f"
dependencies = [
 "frame-support 30.0.0",
 "frame-support-procedural 25.0.0",
 "frame-system 30.0.0",
 "linregress",
 "log",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "sp-api 28.0.0",
 "sp-application-crypto 32.0.0",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-runtime-interface 26.0.0",
 "sp-std 14.0.0",
 "sp-storage 20.0.0",
 "static_assertions",
]

[[package]]
name = "frame-benchmarking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a01bdd47c2d541b38bd892da647d1e972c9d85b4ecd7094ad64f7600175da54d"
dependencies = [
 "frame-support 38.2.0",
 "frame-support-procedural 30.0.6",
 "frame-system 38.0.0",
 "linregress",
 "log",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-runtime-interface 28.0.0",
 "sp-storage 21.0.0",
 "static_assertions",
]

[[package]]
name = "frame-benchmarking-pallet-pov"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ffde6f573a63eeb1ccb7d2667c5741a11ce93bc30f33712e5326b9d8a811c29"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "frame-decode"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6027a409bac4fe95b4d107f965fcdbc252fc89d884a360d076b3070b6128c094"
dependencies = [
 "frame-metadata 17.0.0",
 "parity-scale-codec",
 "scale-decode",
 "scale-info",
 "scale-type-resolver",
 "sp-crypto-hashing",
]

[[package]]
name = "frame-election-provider-solution-type"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35d1461dc3a49bbd9bdf8955eca27f54cdcc6b38373bbd636e011a8594d23f3f"
dependencies = [
 "proc-macro-crate 1.3.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-election-provider-solution-type"
version = "14.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8156f209055d352994ecd49e19658c6b469d7c6de923bd79868957d0dcfb6f71"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-election-provider-support"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9fe9b8322a08a8a52ed3a7a7f7ca90827aa3bace41dc060c5b32d2ff0cd25c3"
dependencies = [
 "frame-election-provider-solution-type 11.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-npos-elections 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "frame-election-provider-support"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c36f5116192c63d39f1b4556fa30ac7db5a6a52575fa241b045f7dfa82ecc2be"
dependencies = [
 "frame-election-provider-solution-type 14.0.1",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-npos-elections 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "frame-executive"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c365bf3879de25bbee28e9584096955a02fbe8d7e7624e10675800317f1cee5b"
dependencies = [
 "aquamarine 0.5.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "frame-try-runtime",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
]

[[package]]
name = "frame-metadata"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87cf1549fba25a6fcac22785b61698317d958e96cac72a59102ea45b9ae64692"
dependencies = [
 "cfg-if",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "frame-metadata"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "701bac17e9b55e0f95067c428ebcb46496587f08e8cf4ccc0fe5903bea10dbb8"
dependencies = [
 "cfg-if",
 "parity-scale-codec",
 "scale-info",
 "serde",
]

[[package]]
name = "frame-metadata-hash-extension"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56ac71dbd97039c49fdd69f416a4dd5d8da3652fdcafc3738b45772ad79eb4ec"
dependencies = [
 "array-bytes",
 "docify",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "frame-support"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0dc5640279221fbd316a3a652963c1cb9d51630ea3f62a08a5ad7fa402f23a4"
dependencies = [
 "aquamarine 0.3.3",
 "bitflags 1.3.2",
 "docify",
 "environmental",
 "frame-metadata 16.0.0",
 "frame-support-procedural 19.0.0",
 "impl-trait-for-tuples",
 "k256",
 "log",
 "macro_magic 0.4.2",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "serde_json",
 "smallvec",
 "sp-api 22.0.0",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-core-hashing-proc-macro",
 "sp-debug-derive 11.0.0",
 "sp-genesis-builder 0.3.0",
 "sp-inherents 22.0.0",
 "sp-io 26.0.0",
 "sp-metadata-ir 0.3.0",
 "sp-runtime 27.0.0",
 "sp-staking 22.0.0",
 "sp-state-machine 0.31.0",
 "sp-std 11.0.0",
 "sp-tracing 13.0.0",
 "sp-weights 23.0.0",
 "static_assertions",
 "tt-call",
]

[[package]]
name = "frame-support"
version = "30.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40bde5b74ac70a1c9fe4f846220ea10e78b81b0ffcdb567d16d28472bc332f95"
dependencies = [
 "aquamarine 0.5.0",
 "array-bytes",
 "bitflags 1.3.2",
 "docify",
 "environmental",
 "frame-metadata 16.0.0",
 "frame-support-procedural 25.0.0",
 "impl-trait-for-tuples",
 "k256",
 "log",
 "macro_magic 0.5.1",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "serde_json",
 "smallvec",
 "sp-api 28.0.0",
 "sp-arithmetic 25.0.0",
 "sp-core 30.0.0",
 "sp-crypto-hashing-proc-macro",
 "sp-debug-derive 14.0.0",
 "sp-genesis-builder 0.9.0",
 "sp-inherents 28.0.0",
 "sp-io 32.0.0",
 "sp-metadata-ir 0.6.0",
 "sp-runtime 33.0.0",
 "sp-staking 28.0.0",
 "sp-state-machine 0.37.0",
 "sp-std 14.0.0",
 "sp-tracing 16.0.0",
 "sp-weights 29.0.0",
 "static_assertions",
 "tt-call",
]

[[package]]
name = "frame-support"
version = "38.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7dd8b9f161a8289e3b9fe6c1068519358dbff2270d38097a923d3d1b4459dca"
dependencies = [
 "aquamarine 0.5.0",
 "array-bytes",
 "bitflags 1.3.2",
 "docify",
 "environmental",
 "frame-metadata 16.0.0",
 "frame-support-procedural 30.0.6",
 "impl-trait-for-tuples",
 "k256",
 "log",
 "macro_magic 0.5.1",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "serde_json",
 "smallvec",
 "sp-api 34.0.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-crypto-hashing-proc-macro",
 "sp-debug-derive 14.0.0",
 "sp-genesis-builder 0.15.1",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-metadata-ir 0.7.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
 "sp-state-machine 0.43.0",
 "sp-std 14.0.0",
 "sp-tracing 17.0.1",
 "sp-weights 31.0.0",
 "static_assertions",
 "tt-call",
]

[[package]]
name = "frame-support-procedural"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f22719c65353a0010a084cb2040e2e6569aff34562e59119cb66ddd7ecfa588c"
dependencies = [
 "Inflector",
 "cfg-expr",
 "derive-syn-parse 0.1.5",
 "expander",
 "frame-support-procedural-tools 8.0.0",
 "itertools 0.10.5",
 "macro_magic 0.4.2",
 "proc-macro-warning 0.4.2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural"
version = "25.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c762bf871c6655636a40a74d06f7f1bf69813f8037ad269704ae35b1c56c42ec"
dependencies = [
 "Inflector",
 "cfg-expr",
 "derive-syn-parse 0.1.5",
 "expander",
 "frame-support-procedural-tools 11.0.0",
 "itertools 0.10.5",
 "macro_magic 0.5.1",
 "proc-macro-warning 1.0.2",
 "proc-macro2",
 "quote",
 "sp-crypto-hashing",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural"
version = "30.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8da784d943f2a945be923ab081a7c0837355b38045c50945d7ec1a138e2f3c52"
dependencies = [
 "Inflector",
 "cfg-expr",
 "derive-syn-parse 0.2.0",
 "docify",
 "expander",
 "frame-support-procedural-tools 13.0.1",
 "itertools 0.11.0",
 "macro_magic 0.5.1",
 "proc-macro-warning 1.0.2",
 "proc-macro2",
 "quote",
 "sp-crypto-hashing",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural-tools"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e046ecdc04dd66f17d760525631f553ddcbea6f09423f78fcf52b47c97656cd0"
dependencies = [
 "frame-support-procedural-tools-derive 9.0.0",
 "proc-macro-crate 1.3.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural-tools"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be30b1ce0b477476a3fe13cd8ff479007582340d14f0ddea9e832b01e706a07"
dependencies = [
 "frame-support-procedural-tools-derive 12.0.0",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural-tools"
version = "13.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81a088fd6fda5f53ff0c17fc7551ce8bd0ead14ba742228443c8196296a7369b"
dependencies = [
 "frame-support-procedural-tools-derive 12.0.0",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural-tools-derive"
version = "9.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4034ebf9ca7497fa3893191fe3e81adcd3d7cd1c232e60ef41ef58ea0c445ae9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-support-procedural-tools-derive"
version = "12.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed971c6435503a099bdac99fe4c5bea08981709e5b5a0a8535a1856f48561191"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "frame-system"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc19d4d4037b695805385d56983da173bbb969f68e0e4e6a1240bb30118e87d7"
dependencies = [
 "cfg-if",
 "frame-support 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-version 25.0.0",
 "sp-weights 23.0.0",
]

[[package]]
name = "frame-system"
version = "30.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c302f711acf3196b4bf2b4629a07a2ac6e44cd1782434ec88b85d59adfb1204d"
dependencies = [
 "cfg-if",
 "docify",
 "frame-support 30.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "sp-version 31.0.0",
 "sp-weights 29.0.0",
]

[[package]]
name = "frame-system"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3c7fa02f8c305496d2ae52edaecdb9d165f11afa965e05686d7d7dd1ce93611"
dependencies = [
 "cfg-if",
 "docify",
 "frame-support 38.2.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "sp-version 37.0.0",
 "sp-weights 31.0.0",
]

[[package]]
name = "frame-system-benchmarking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9693b2a736beb076e673520e1e8dee4fc128b8d35b020ef3e8a4b1b5ad63d9f2"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "frame-system-rpc-runtime-api"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "475c4f8604ba7e4f05cd2c881ba71105093e638b9591ec71a8db14a64b3b4ec3"
dependencies = [
 "docify",
 "parity-scale-codec",
 "sp-api 34.0.0",
]

[[package]]
name = "frame-try-runtime"
version = "0.44.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83c811a5a1f5429c7fb5ebbf6cf9502d8f9b673fd395c12cf46c44a30a7daf0e"
dependencies = [
 "frame-support 38.2.0",
 "parity-scale-codec",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "fs-err"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88a41f105fe1d5b6b34b2055e3dc59bb79b46b48b2040b9e6c7b4b5de097aa41"
dependencies = [
 "autocfg",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-lite"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5edaec856126859abb19ed65f39e90fea3a9574b9707f13539acf4abf7eb532"
dependencies = [
 "fastrand",
 "futures-core",
 "futures-io",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "generic-array"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffdf9f34f1447443d37393cc6c2b8313aebddcd96906caf34e54c68d8e57d7bd"
dependencies = [
 "typenum",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "getrandom"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fc3cb4d91f53b50155bdcfd23f6a4c39ae1969c2ae85982b135750cccaf5fce"
dependencies = [
 "cfg-if",
 "libc",
 "wasi 0.9.0+wasi-snapshot-preview1",
]

[[package]]
name = "getrandom"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "190092ea657667030ac6a35e305e62fc4dd69fd98ac98631e5d3a2b1575a12b5"
dependencies = [
 "cfg-if",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
]

[[package]]
name = "getrandom_or_panic"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ea1015b5a70616b688dc230cfe50c8af89d972cb132d5a622814d29773b10b9"
dependencies = [
 "rand 0.8.5",
 "rand_core 0.6.4",
]

[[package]]
name = "ghash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0d8a4362ccb29cb0b265253fb0a2728f592895ee6854fd9bc13f2ffda266ff1"
dependencies = [
 "opaque-debug 0.3.0",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c80984affa11d98d1b88b66ac8853f143217b399d3c74116778ff8fdb4ed2e"
dependencies = [
 "fallible-iterator 0.2.0",
 "indexmap 1.9.3",
 "stable_deref_trait",
]

[[package]]
name = "gimli"
version = "0.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4271d37baee1b8c7e4b708028c57d816cf9d2434acb33a549475f78c181f6253"
dependencies = [
 "fallible-iterator 0.3.0",
 "stable_deref_trait",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "handlebars"
version = "5.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d08485b96a0e6393e9e4d1b8d48cf74ad6c063cd905eb33f42c1ce3f0377539b"
dependencies = [
 "log",
 "pest",
 "pest_derive",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "hash-db"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e7d7786361d7425ae2fe4f9e407eb0efaa0840f5212d109cc018c40c35c6ab4"

[[package]]
name = "hash256-std-hasher"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92c171d55b98633f4ed3860808f004099b36c1cc29c42cfc53aa8591b21efcf2"
dependencies = [
 "crunchy",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash 0.8.11",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash 0.8.11",
 "allocator-api2",
 "serde",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd5256b483761cd23699d0da46cc6fd2ee3be420bbe6d020ae4a091e70b7e9fd"

[[package]]
name = "hermit-abi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbf6a919d6cf397374f7dfeeea91d974c7c0a7221d0d0f4f20d859d329e53fcc"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hex-conservative"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30ed443af458ccb6d81c1e7e661545f94d3176752fb1df2f543b902a1e0f51e2"

[[package]]
name = "hex-literal"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe2267d4ed49bc07b63801559be28c718ea06c4738b7a03c94df7386d2cde46"

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac 0.12.1",
]

[[package]]
name = "hmac"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126888268dcc288495a26bf004b38c5fdbb31682f992c84ceb046a1f0fe38840"
dependencies = [
 "crypto-mac 0.8.0",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2a2320eb7ec0ebe8da8f744d7812d9fc4cb4d09344ac01898dbcb6a20ae69b"
dependencies = [
 "crypto-mac 0.11.0",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-drbg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ea0a1394df5b6574da6e0c1ade9e78868c9fb0a4e5ef4428e32da4676b85b1"
dependencies = [
 "digest 0.9.0",
 "generic-array 0.14.7",
 "hmac 0.8.1",
]

[[package]]
name = "http"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f16ca2af56261c99fba8bac40a10251ce8188205a4c448fbb745a2e4daa76fea"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "iana-time-zone"
version = "0.1.60"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7ffbb5a1b541ea2561f8c41c087286cc091e21e556a4f09a8f6cbf17b69b141"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows-core",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "icu_collections"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fa452206ebee18c4b5c2274dbf1de17008e874b4dc4f0aea9d01ca79e4526"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locid"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13acbb8371917fc971be86fc8057c41a64b521c184808a698c02acc242dbf637"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_locid_transform"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01d11ac35de8e40fdeda00d9e1e9d92525f3f9d887cdd7aa81d727596788b54e"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_locid_transform_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_locid_transform_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc8ff3388f852bede6b579ad4e978ab004f139284d7b28715f773507b946f6e"

[[package]]
name = "icu_normalizer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19ce3e0da2ec68599d193c93d088142efd7f9c5d6fc9b803774855747dc6a84f"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "utf16_iter",
 "utf8_iter",
 "write16",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8cafbf7aa791e9b22bec55a167906f9e1215fd475cd22adfcf660e03e989516"

[[package]]
name = "icu_properties"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93d6020766cfc6302c15dbbc9c8778c37e62c14427cb7f6e601d849e092aeef5"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locid_transform",
 "icu_properties_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67a8effbc3dd3e4ba1afa8ad918d5684b8868b3b26500753effea8d2eed19569"

[[package]]
name = "icu_provider"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ed421c8a8ef78d3e2dbc98a973be2f3770cb42b606e3ab18d6237c4dfde68d9"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_provider_macros",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_provider_macros"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ec89e9337638ecdc08744df490b221a7399bf8d164eb52a665454e60e075ad6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daca1df1c957320b2cf139ac61e7bd64fed304c5040df000a745aa1de3b4ef71"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "impl-codec"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba6a270039626615617f3f36d15fc827041df3b78c439da2cadfa47455a77f2f"
dependencies = [
 "parity-scale-codec",
]

[[package]]
name = "impl-codec"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d40b9d5e17727407e55028eafc22b2dc68781786e6d7eb8a21103f5058e3a14"
dependencies = [
 "parity-scale-codec",
]

[[package]]
name = "impl-rlp"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f28220f89297a075ddc7245cd538076ee98b01f2a9c23a53a4f1105d5a322808"
dependencies = [
 "rlp",
]

[[package]]
name = "impl-serde"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc88fc67028ae3db0c853baa36269d398d5f45b6982f95549ff5def78c935cd"
dependencies = [
 "serde",
]

[[package]]
name = "impl-serde"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a143eada6a1ec4aefa5049037a26a6d597bfd64f8c026d07b77133e02b7dd0b"
dependencies = [
 "serde",
]

[[package]]
name = "impl-trait-for-tuples"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0eb5a3343abf848c0984fe4604b2b105da9539376e24fc0a3b0007411ae4fd9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "include_dir"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18762faeff7122e89e0857b02f7ce6fcc0d101d5e9ad2ad7846cc01d61b7f19e"
dependencies = [
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b139284b5cf57ecfa712bcc66950bb635b31aff41c188e8a4cfc758eca374a3f"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "233cf39063f058ea2caae4091bf4a3ef70a653afbc026f5c4a4135d114e3c177"
dependencies = [
 "equivalent",
 "hashbrown 0.14.5",
]

[[package]]
name = "indexmap-nostd"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e04e2fd2b8188ea827b32ef11de88377086d690286ab35747ef7f9bf3ccb590"

[[package]]
name = "inout"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0c10553d664a4d0bcff9f4215d0aac67a639cc68ef660840afe309b807bc9f5"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "integer-sqrt"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "276ec31bcb4a9ee45f58bec6f9ec700ae4cf4f4f8f2fa7e06cb406bd5ffdd770"
dependencies = [
 "num-traits",
]

[[package]]
name = "io-lifetimes"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eae7b9aee968036d54dce06cebaefd919e4472e753296daccd6d344e3e2df0c2"
dependencies = [
 "hermit-abi 0.3.6",
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "is-terminal"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e19b23d53f35ce9f56aebc7d1bb4e6ac1e9c0db7ac85c8d1760c04379edced37"
dependencies = [
 "hermit-abi 0.4.0",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1a46d1a171d865aa5f83f92695765caa047a9b4cbae2cbf37dbd613a793fd4c"

[[package]]
name = "jni"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6df18c2e3db7e453d3c6ac5b3e9d5182664d28788126d39b91f2d1e22b017ec"
dependencies = [
 "cesu8",
 "combine",
 "jni-sys",
 "log",
 "thiserror",
 "walkdir",
]

[[package]]
name = "jni-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eaf4bc02d17cbdd7ff4c7438cafcdf7fb9a4613313ad11b4f8fefe7d3fa0130"

[[package]]
name = "jobserver"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d1dbcbbeb6a7fec7e059840aa538bd62aaccf972c7346c4d9d2059312853d0"
dependencies = [
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "jsonrpsee"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "834af00800e962dee8f7bfc0f60601de215e73e78e5497d733a2919da837d3c8"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "jsonrpsee-ws-client",
]

[[package]]
name = "jsonrpsee-client-transport"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "def0fd41e2f53118bd1620478d12305b2c75feef57ea1f93ef70568c98081b7e"
dependencies = [
 "base64 0.22.1",
 "futures-util",
 "http",
 "jsonrpsee-core",
 "pin-project",
 "rustls",
 "rustls-pki-types",
 "rustls-platform-verifier",
 "soketto",
 "thiserror",
 "tokio",
 "tokio-rustls",
 "tokio-util",
 "tracing",
 "url",
]

[[package]]
name = "jsonrpsee-core"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76637f6294b04e747d68e69336ef839a3493ca62b35bf488ead525f7da75c5bb"
dependencies = [
 "async-trait",
 "futures-timer",
 "futures-util",
 "jsonrpsee-types",
 "pin-project",
 "rustc-hash 2.1.1",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "jsonrpsee-types"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddb81adb1a5ae9182df379e374a79e24e992334e7346af4d065ae5b2acb8d4c6"
dependencies = [
 "http",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "jsonrpsee-ws-client"
version = "0.24.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f4f3642a292f5b76d8a16af5c88c16a0860f2ccc778104e5c848b28183d9538"
dependencies = [
 "http",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "url",
]

[[package]]
name = "k256"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e3919bbaa2945715f0bb6d3934a173d1e9a59ac23767fbaaef277265a7411b"
dependencies = [
 "cfg-if",
 "ecdsa",
 "elliptic-curve",
 "once_cell",
 "serdect",
 "sha2 0.10.8",
 "signature",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "keccak-asm"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb8515fff80ed850aea4a1595f2e519c003e2a00a82fe168ebf5269196caf444"
dependencies = [
 "digest 0.10.7",
 "sha3-asm",
]

[[package]]
name = "keccak-hash"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e1b8590eb6148af2ea2d75f38e7d29f5ca970d5a4df456b3ef19b8b415d0264"
dependencies = [
 "primitive-types 0.13.1",
 "tiny-keccak",
]

[[package]]
name = "kusama-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "subxt",
]

[[package]]
name = "lazy_static"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"

[[package]]
name = "libc"
version = "0.2.170"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "875b3680cb2f8f71bdcf9a30f38d48282f5d3c95cbf9b3fa57269bb5d5c06828"

[[package]]
name = "libm"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec2a862134d2a7d32d7983ddcdd1c4923530833c9f2ea1a44fc5fa473989058"

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.8.0",
 "libc",
 "redox_syscall 0.5.9",
]

[[package]]
name = "libsecp256k1"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95b09eff1b35ed3b33b877ced3a691fc7a481919c7e29c53c906226fcf55e2a1"
dependencies = [
 "arrayref",
 "base64 0.13.1",
 "digest 0.9.0",
 "hmac-drbg",
 "libsecp256k1-core",
 "libsecp256k1-gen-ecmult",
 "libsecp256k1-gen-genmult",
 "rand 0.8.5",
 "serde",
 "sha2 0.9.9",
 "typenum",
]

[[package]]
name = "libsecp256k1-core"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be9b9bb642d8522a44d533eab56c16c738301965504753b03ad1de3425d5451"
dependencies = [
 "crunchy",
 "digest 0.9.0",
 "subtle",
]

[[package]]
name = "libsecp256k1-gen-ecmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3038c808c55c87e8a172643a7d87187fc6c4174468159cb3090659d55bcb4809"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsecp256k1-gen-genmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db8d6ba2cec9eacc40e6e8ccc98931840301f1006e95647ceb2dd5c3aa06f7c"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "link-cplusplus"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d240c6f7e1ba3a28b0249f774e6a9dd0175054b52dfbb61b16eb8505c3785c9"
dependencies = [
 "cc",
]

[[package]]
name = "linregress"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4de04dcecc58d366391f9920245b85ffa684558a5ef6e7736e754347c3aea9c2"
dependencies = [
 "nalgebra",
]

[[package]]
name = "linux-raw-sys"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f051f77a7c8e6957c0696eac88f26b0117e54f52d3fc682ab19397a8812846a4"

[[package]]
name = "linux-raw-sys"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01cda141df6706de531b6c46c3a33ecca755538219bd484262fa09410c13539c"

[[package]]
name = "litemap"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23fb14cb19457329c82206317a5663005a4d404783dc74f4252769b0d5f42856"

[[package]]
name = "lock_api"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c168f8615b12bc01f9c17e2eb0cc07dcae1940121185446edc3744920e8ef45"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30bde2b3dc3671ae49d8e2e9f044c7c005836e7a023ee57cffa25ab82764bb9e"

[[package]]
name = "lru"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6e8aaa3f231bb4bd57b84b2d5dc3ae7f350265df8aa96492e0bc394a1571909"
dependencies = [
 "hashbrown 0.12.3",
]

[[package]]
name = "lru"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2c024b41519440580066ba82aab04092b333e09066a5eb86c7c4890df31f22"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc",
]

[[package]]
name = "macro_magic"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aee866bfee30d2d7e83835a4574aad5b45adba4cc807f2a3bbba974e5d4383c9"
dependencies = [
 "macro_magic_core 0.4.2",
 "macro_magic_macros 0.4.2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc33f9f0351468d26fbc53d9ce00a096c8522ecb42f19b50f34f2c422f76d21d"
dependencies = [
 "macro_magic_core 0.5.1",
 "macro_magic_macros 0.5.1",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e766a20fd9c72bab3e1e64ed63f36bd08410e75803813df210d1ce297d7ad00"
dependencies = [
 "const-random",
 "derive-syn-parse 0.1.5",
 "macro_magic_core_macros 0.4.3",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1687dc887e42f352865a393acae7cf79d98fab6351cde1f58e9e057da89bf150"
dependencies = [
 "const-random",
 "derive-syn-parse 0.2.0",
 "macro_magic_core_macros 0.5.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_core_macros"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d710e1214dffbab3b5dacb21475dde7d6ed84c69ff722b3a47a782668d44fbac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_core_macros"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b02abfe41815b5bd98dbd4260173db2c116dda171dc0fe7838cb206333b83308"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fb85ec1620619edf2984a7693497d4ec88a9665d8b87e942856884c92dbf2a"
dependencies = [
 "macro_magic_core 0.4.2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "macro_magic_macros"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73ea28ee64b88876bf45277ed9a5817c1817df061a74f2b988971a12570e5869"
dependencies = [
 "macro_magic_core 0.5.1",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "matchers"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f099785f7595cc4b4553a174ce30dd7589ef93391ff414dbb67f62392b9e0ce1"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matrixmultiply"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7574c1cf36da4798ab73da5b215bbf444f50718207754cb522201d78d1cd0ff2"
dependencies = [
 "autocfg",
 "rawpointer",
]

[[package]]
name = "memchr"
version = "2.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "523dc4f511e55ab87b694dc30d0f820d60906ef06413f93d4d7a1385599cc149"

[[package]]
name = "memfd"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2cffa4ad52c6f791f4f8b15f0c05f9824b2ced1160e88cc393d64fff9a8ac64"
dependencies = [
 "rustix 0.38.31",
]

[[package]]
name = "memoffset"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d61c719bcfbcf5d62b3a09efa6088de8c54bc0bfcd3ea7ae39fcc186108b8de1"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory-db"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "808b50db46293432a45e63bc15ea51e0ab4c0a1647b8eb114e31a3e698dd6fbe"
dependencies = [
 "hash-db",
]

[[package]]
name = "merlin"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e261cf0f8b3c42ded9f7d2bb59dea03aa52bc8a1cbc7482f9fc3fd1229d3b42"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.5.1",
 "zeroize",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d811f3e15f28568be3407c8e7fdb6514c1cda3cb30683f15b6a1a1dc4ea14a7"
dependencies = [
 "adler",
]

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "multi-stash"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "685a9ac4b61f4e728e1d2c6a7844609c16527aeb5e6c865915c08e619c16410f"

[[package]]
name = "nalgebra"
version = "0.32.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5c17de023a86f59ed79891b2e5d5a94c705dbe904a5b5c9c952ea6221b03e4"
dependencies = [
 "approx",
 "matrixmultiply",
 "nalgebra-macros",
 "num-complex",
 "num-rational",
 "num-traits",
 "simba",
 "typenum",
]

[[package]]
name = "nalgebra-macros"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91761aed67d03ad966ef783ae962ef9bbaca728d2dd7ceb7939ec110fffad998"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "nodrop"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72ef4a56884ca558e5ddb05a1d1e7e1bfd9a68d9ed024c21704cc98872dae1bb"

[[package]]
name = "nohash-hasher"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bf50223579dc7cdcfb3bfcacf7069ff68243f8c363f62ffa99cf000a6b9c451"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "num-bigint"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "608e7659b5c3d7cba262d894801b9ec9d00de989e8a82bd4bef91d08da45cdc0"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f88a1307638156682bada9d7604135552957b7818057dcef22705b4d509495"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec 0.7.4",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0638a1c9d0a3c0914158145bc76cff373a75a627e6ecbfb71cbe6f453a5a19b0"
dependencies = [
 "autocfg",
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.6",
 "libc",
]

[[package]]
name = "object"
version = "0.30.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03b4680b86d9cfafba8fc491dc9b6df26b68cf40e9e6cd73909194759a63c385"
dependencies = [
 "crc32fast",
 "hashbrown 0.13.2",
 "indexmap 1.9.3",
 "memchr",
]

[[package]]
name = "object"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6a622008b6e321afc04970976f62ee297fdbaa6f95318ca343e3eebb9648441"
dependencies = [
 "memchr",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "memchr",
]

[[package]]
name = "once_cell"
version = "1.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fdb12b2476b595f9358c5161aa467c2438859caa136dec86c26fdd2efe17b92"

[[package]]
name = "opaque-debug"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2839e79665f131bdb5782e51f2c6c9599c133c6098982a54c794358bf432529c"

[[package]]
name = "opaque-debug"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "624a8340c38c1b80fd549087862da4ba43e08858af025b236e509b6649fc13d5"

[[package]]
name = "openssl-probe"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff011a302c396a5197692431fc1948019154afc178baf7d8e37367442a4601cf"

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "pallet-alliance"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59378a648a0aa279a4b10650366c3389cd0a1239b1876f74bfecd268eecb086b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-collective",
 "pallet-identity",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-crypto-hashing",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-asset-conversion"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33f0078659ae95efe6a1bf138ab5250bc41ab98f22ff3651d0208684f08ae797"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-asset-conversion-ops"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3edbeda834bcd6660f311d4eead3dabdf6d385b7308ac75b0fae941a960e6c3a"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-asset-conversion",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-asset-conversion-tx-payment"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ab66c4c22ac0f20e620a954ce7ba050118d6d8011e2d02df599309502064e98"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-asset-conversion",
 "pallet-transaction-payment 38.0.2",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-asset-rate"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71b2149aa741bc39466bbcc92d9d0ab6e9adcf39d2790443a735ad573b3191e7"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-asset-tx-payment"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "406a486466d15acc48c99420191f96f1af018f3381fde829c467aba489030f18"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-transaction-payment 38.0.2",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-assets"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f45f4eb6027fc34c4650e0ed6a7e57ed3335cc364be74b4531f714237676bcee"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-assets-freezer"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "127adc2250b89416b940850ce2175dab10a9297b503b1fcb05dc555bd9bd3207"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-assets",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-atomic-swap"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15906a685adeabe6027e49c814a34066222dd6136187a8a79c213d0d739b6634"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-aura"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b31da6e794d655d1f9c4da6557a57399538d75905a7862a2ed3f7e5fb711d7e4"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-timestamp 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-consensus-aura",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-authority-discovery"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bdc251538bcec9340337a1624372561e6d8e6ae4eeb1adb1d7b1af13b349cda"
dependencies = [
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "pallet-session 24.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 26.0.0",
 "sp-authority-discovery 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-authority-discovery"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffb0208f0538d58dcb78ce1ff5e6e8641c5f37b23b20b05587e51da30ab13541"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-authority-discovery 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-authorship"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae682e78744224150298730dfa1e2c39220e600dce17e42d2c77e49af3d9c59f"
dependencies = [
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-authorship"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "625d47577cabbe1318ccec5d612e2379002d1b6af1ab6edcef3243c66ec246df"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-babe"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eebab5b1891cc12c13348509831703bea1f160eff60fa7b76b94097cf13b7dcc"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "pallet-authorship 24.0.0",
 "pallet-session 24.0.0",
 "pallet-timestamp 23.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 26.0.0",
 "sp-consensus-babe 0.28.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-session 23.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-babe"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ee096c0def13832475b340d00121025e0225de29604d44bc6dfcaa294c995b4"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "pallet-session 38.0.0",
 "pallet-timestamp 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-consensus-babe 0.40.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-bags-list"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fd23a6f94ba9c1e57c8a7f8a41327d132903a79c55c0c83f36cbae19946cf10"
dependencies = [
 "aquamarine 0.5.0",
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
]

[[package]]
name = "pallet-balances"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c17ec19ad23b26866ad7d60cdf8b613f653db7f44232aa25009811441908e2b"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-balances"
version = "39.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcb1f72d7048fbd11e884b4693f7d438b8202340ff252e2a402e04c638fe2d02"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-beefy"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "014d177a3aba19ac144fc6b2b5eb94930b9874734b91fd014902b6706288bb5f"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-consensus-beefy",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-beefy-mmr"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c64f536e7f04cf3a0a17fdf20870ddb3d63a7690419c40f75cfd2f72b6e6d22"
dependencies = [
 "array-bytes",
 "binary-merkle-tree",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-beefy",
 "pallet-mmr",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-consensus-beefy",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-state-machine 0.43.0",
]

[[package]]
name = "pallet-bounties"
version = "37.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59f3d032f78624b12238a31b6e80ab3e112381a7bc222df152650e33bb2ce190"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-treasury 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-bridge-grandpa"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d825fbed9fb68bc5d344311653dc0f69caeabe647365abf79a539310b2245f6"
dependencies = [
 "bp-header-chain",
 "bp-runtime",
 "bp-test-utils",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-consensus-grandpa",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "pallet-bridge-messages"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1decdc9fb885e46eb17f850aa14f8cf39e17f31574aa6a5fa1a9e603cc526a2"
dependencies = [
 "bp-header-chain",
 "bp-messages",
 "bp-runtime",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "pallet-bridge-parachains"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41450a8d214f20eaff57aeca8e647b20c0df7d66871ee2262609b90824bd4cca"
dependencies = [
 "bp-header-chain",
 "bp-parachains",
 "bp-polkadot-core",
 "bp-runtime",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-bridge-grandpa",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "pallet-bridge-relayers"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fe3be7077b7ddee7178b1b12e9171435da73778d093788e10b1bdfad1e10962"
dependencies = [
 "bp-header-chain",
 "bp-messages",
 "bp-relayers",
 "bp-runtime",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-bridge-grandpa",
 "pallet-bridge-messages",
 "pallet-bridge-parachains",
 "pallet-transaction-payment 38.0.2",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "pallet-broker"
version = "0.17.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "018b477d7d464c451b1d09a4ce9e792c3c65b15fd764b23da38ff9980e786065"
dependencies = [
 "bitvec",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-child-bounties"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7f3bc38ae6584b5f57e4de3e49e5184bfc0f20692829530ae1465ffe04e09e7"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-bounties",
 "pallet-treasury 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-collator-selection"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658798d70c9054165169f6a6a96cfa9d6a5e7d24a524bc19825bf17fcbc5cc5a"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-collective"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e149f1aefd444c9a1da6ec5a94bc8a7671d7a33078f85dd19ae5b06e3438e60"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-collective-content"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38a6a5cbe781d9c711be74855ba32ef138f3779d6c54240c08e6d1b4bbba4d1d"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-contracts"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5df77077745d891c822b4275f273f336077a97e69e62a30134776aa721c96fee"
dependencies = [
 "bitflags 1.3.2",
 "environmental",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-balances 39.0.1",
 "pallet-contracts-proc-macro",
 "pallet-contracts-uapi",
 "parity-scale-codec",
 "paste",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "smallvec",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "wasm-instrument",
 "wasmi",
]

[[package]]
name = "pallet-contracts-mock-network"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "309666537ed001c61a99f59fa7b98680f4a6e4e361ed3bc64f7b0237da3e3e06"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-assets",
 "pallet-balances 39.0.1",
 "pallet-contracts",
 "pallet-contracts-proc-macro",
 "pallet-contracts-uapi",
 "pallet-insecure-randomness-collective-flip",
 "pallet-message-queue 41.0.2",
 "pallet-proxy",
 "pallet-timestamp 37.0.0",
 "pallet-utility",
 "pallet-xcm",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "scale-info",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "xcm-simulator",
]

[[package]]
name = "pallet-contracts-proc-macro"
version = "23.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3170e2f4a3d95f2ace274b703a72630294f0a27c687a4adbad9590e2b3e5fe82"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "pallet-contracts-uapi"
version = "12.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d3e13d72cda1a30083a1c080acc56fc5f286d09c89d9d91e8e4942a230c58c8"
dependencies = [
 "bitflags 1.3.2",
 "parity-scale-codec",
 "paste",
 "scale-info",
]

[[package]]
name = "pallet-conviction-voting"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "999c242491b74395b8c5409ef644e782fe426d87ae36ad92240ffbf21ff0a76e"
dependencies = [
 "assert_matches",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-core-fellowship"
version = "22.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93052dd8d5910e1b939441541cec416e629b2c0ab92680124c2e5a137e12c285"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-ranked-collective",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-delegated-staking"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "117f003a97f980514c6db25a50c22aaec2a9ccb5664b3cb32f52fb990e0b0c12"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-democracy"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6d1dc655f50b7c65bb2fb14086608ba11af02ef2936546f7a67db980ec1f133"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-dev-mode"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae1d8050c09c5e003d502c1addc7fdfbde21a854bd57787e94447078032710c8"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-election-provider-multi-phase"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "394d978de9611f3b23fc499369f86d5d82e64800959a5701d66907737e779406"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-election-provider-support 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "pallet-election-provider-support-benchmarking 23.0.0",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-npos-elections 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "strum 0.24.1",
]

[[package]]
name = "pallet-election-provider-multi-phase"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62f9ad5ae0c13ba3727183dadf1825b6b7b0b0598ed5c366f8697e13fd540f7d"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-election-provider-support-benchmarking 37.0.0",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-npos-elections 34.0.0",
 "sp-runtime 39.0.5",
 "strum 0.26.3",
]

[[package]]
name = "pallet-election-provider-support-benchmarking"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a23b0dec60fd7a0c98234a4b04ef5e1f682bdf7ff3266ca499d85e1aac7bbec"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-election-provider-support 24.0.0",
 "frame-system 24.0.0",
 "parity-scale-codec",
 "sp-npos-elections 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-election-provider-support-benchmarking"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4111d0d27545c260c9dd0d6fc504961db59c1ec4b42e1bcdc28ebd478895c22"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "sp-npos-elections 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-elections-phragmen"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "705c66d6c231340c6d085a0df0319a6ce42a150f248171e88e389ab1e3ce20f5"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-npos-elections 34.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-fast-unstake"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c41338a9d75f9c4c656d5d5ff15d8154edd7de61a97361e2d0ddc552baf6e944"
dependencies = [
 "docify",
 "frame-benchmarking 24.0.0",
 "frame-election-provider-support 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-fast-unstake"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0ee60e8ef10b3936f2700bd61fa45dcc190c61124becc63bed787addcfa0d20"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-glutton"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1c79ab340890f6ab088a638c350ac1173a1b2a79c18004787523032025582b4"
dependencies = [
 "blake2",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-grandpa"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d3a570a4aac3173ea46b600408183ca2bcfdaadc077f802f11e6055963e2449"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-consensus-grandpa",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-identity"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3a4288548de9a755e39fcb82ffb9024b6bb1ba0f582464a44423038dd7a892e"
dependencies = [
 "enumflags2",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-im-online"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6fd95270cf029d16cb40fe6bd9f8ab9c78cd966666dccbca4d8bfec35c5bba5"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-application-crypto 38.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-indices"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5e4b97de630427a39d50c01c9e81ab8f029a00e56321823958b39b438f7b940"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-keyring",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-insecure-randomness-collective-flip"
version = "26.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dce7ad80675d78bd38a7a66ecbbf2d218dd32955e97f8e301d0afe6c87b0f251"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "safe-mix",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-lottery"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae0920ee53cf7b0665cfb6d275759ae0537dc3850ec78da5f118d814c99d3562"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-membership"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1868b5dca4bbfd1f4a222cbb80735a5197020712a71577b496bbb7e19aaa5394"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-message-queue"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13771c5cba1de8dd9b0492ab8923a8dd6b76a657f6eed6265db65c169b5f9111"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-weights 23.0.0",
]

[[package]]
name = "pallet-message-queue"
version = "41.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "983f7d1be18e9a089a3e23670918f5085705b4403acd3fdde31878d57b76a1a8"
dependencies = [
 "environmental",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
]

[[package]]
name = "pallet-migrations"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b417fc975636bce94e7c6d707e42d0706d67dfa513e72f5946918e1044beef1"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-mixnet"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf3fa2b7f759a47f698a403ab40c54bc8935e2969387947224cbdb4e2bc8a28a"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-io 38.0.0",
 "sp-mixnet",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-mmr"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6932dfb85f77a57c2d1fdc28a7b3a59ffe23efd8d5bb02dc3039d91347e4a3b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-mmr-primitives",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-multisig"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e5099c9a4442efcc1568d88ca1d22d624e81ab96358f99f616c67fbd82532d2"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-nft-fractionalization"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "168792cf95a32fa3baf9b874efec82a45124da0a79cee1ae3c98a823e6841959"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-assets",
 "pallet-nfts",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-nfts"
version = "32.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59e2aad461a0849d7f0471576eeb1fe3151795bcf2ec9e15eca5cca5b9d743b2"
dependencies = [
 "enumflags2",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-nfts-runtime-api"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7a1f50c217e19dc50ff586a71eb5915df6a05bc0b25564ea20674c8cd182c1f"
dependencies = [
 "pallet-nfts",
 "parity-scale-codec",
 "sp-api 34.0.0",
]

[[package]]
name = "pallet-nis"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ac349e119880b7df1a7c4c36d919b33a498d0e9548af3c237365c654ae0c73d"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-node-authorization"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39ec3133be9e767b8feafbb26edd805824faa59956da008d2dc7fcf4b4720e56"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-nomination-pools"
version = "35.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50d04f050ab02af6cbe058e101abb8706be7f8ea7958e5bf1d4cd8caa6b66c71"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
 "sp-tracing 17.0.1",
]

[[package]]
name = "pallet-nomination-pools-benchmarking"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38d2eaca0349bcda923343226b8b64d25a80b67e0a1ebaaa5b0ab1e1b3b225bc"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-bags-list",
 "pallet-delegated-staking",
 "pallet-nomination-pools",
 "pallet-staking 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-runtime-interface 28.0.0",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-nomination-pools-runtime-api"
version = "33.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03eea431eba0658ca763a078bd849e0622c37c85eddd011b8e886460b50c0827"
dependencies = [
 "pallet-nomination-pools",
 "parity-scale-codec",
 "sp-api 34.0.0",
]

[[package]]
name = "pallet-offences"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c4379cf853465696c1c5c03e7e8ce80aeaca0a6139d698abe9ecb3223fd732a"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-offences-benchmarking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69aa1b24cdffc3fa8c89cdea32c83f1bf9c1c82a87fa00e57ae4be8e85f5e24f"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-babe 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-grandpa",
 "pallet-im-online",
 "pallet-offences",
 "pallet-session 38.0.0",
 "pallet-staking 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-paged-list"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8e099fb116068836b17ca4232dc52f762b69dc8cd4e33f509372d958de278b0"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-metadata-ir 0.7.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-parameters"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9aba424d55e17b2a2bec766a41586eab878137704d4803c04bebd6a4743db7b"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "paste",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-preimage"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "407828bc48c6193ac076fdf909b2fadcaaecd65f42b0b0a04afe22fe8e563834"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-proxy"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d39df395f0dbcf07dafe842916adea3266a87ce36ed87b5132184b6bcd746393"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-ranked-collective"
version = "38.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15a640e732164203eb5298823cc8c29cfc563763c43c9114e76153b3166b8b9d"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-recovery"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "406a116aa6d05f88f3c10d79ff89cf577323680a48abd8e5550efb47317e67fa"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-referenda"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3008c20531d1730c9b457ae77ecf0e3c9b07aaf8c4f5d798d61ef6f0b9e2d4b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 26.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-remark"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3e8cae0e20888065ec73dda417325c6ecabf797f4002329484b59c25ecc34d4"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-revive"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be02c94dcbadd206a910a244ec19b493aac793eed95e23d37d6699547234569f"
dependencies = [
 "bitflags 1.3.2",
 "environmental",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-balances 39.0.1",
 "pallet-revive-fixtures",
 "pallet-revive-proc-macro",
 "pallet-revive-uapi",
 "parity-scale-codec",
 "paste",
 "polkavm 0.10.0",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
]

[[package]]
name = "pallet-revive-fixtures"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a38c27f1531f36e5327f3084eb24cf1c9dd46b372e030c0169e843ce363105e"
dependencies = [
 "anyhow",
 "frame-system 38.0.0",
 "parity-wasm",
 "polkavm-linker 0.10.0",
 "sp-runtime 39.0.5",
 "tempfile",
 "toml 0.8.12",
]

[[package]]
name = "pallet-revive-mock-network"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60e74591d44dbd78db02c8593f5caa75bd61bcc4d63999302150223fb969ae37"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-assets",
 "pallet-balances 39.0.1",
 "pallet-message-queue 41.0.2",
 "pallet-proxy",
 "pallet-revive",
 "pallet-revive-proc-macro",
 "pallet-revive-uapi",
 "pallet-timestamp 37.0.0",
 "pallet-utility",
 "pallet-xcm",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "scale-info",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "xcm-simulator",
]

[[package]]
name = "pallet-revive-proc-macro"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8aee42afa416be6324cf6650c137da9742f27dc7be3c7ed39ad9748baf3b9ae"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "pallet-revive-uapi"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecb4686c8415619cc13e43fadef146ffff46424d9b4d037fe4c069de52708aac"
dependencies = [
 "bitflags 1.3.2",
 "parity-scale-codec",
 "paste",
 "polkavm-derive 0.10.0",
 "scale-info",
]

[[package]]
name = "pallet-root-offences"
version = "35.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b35774b830928daaeeca7196cead7c56eeed952a6616ad6dc5ec068d8c85c81a"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-session 38.0.0",
 "pallet-staking 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-root-testing"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be95e7c320ac1d381715364cd721e67ab3152ab727f8e4defd3a92e41ebbc880"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-safe-mode"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d3e67dd4644c168cedbf257ac3dd2527aad81acf4a0d413112197094e549f76"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-proxy",
 "pallet-utility",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-salary"
version = "23.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3af2d92b1fef1c379c0692113b505c108c186e09c25c72b38e879b6e0f172ebe"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-ranked-collective",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-scheduler"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26899a331e7ab5f7d5966cbf203e1cf5bd99cd110356d7ddcaa7597087cdc0b5"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
]

[[package]]
name = "pallet-scored-pool"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f84b48bb4702712c902f43931c4077d3a1cb6773c8d8c290d4a6251f6bc2a5c"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-session"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f8482f465a73688a7d58e20dea4b10c9a0425995975b2a43d9ce4fe9a21a491"
dependencies = [
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-timestamp 23.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-session 23.0.0",
 "sp-staking 22.0.0",
 "sp-state-machine 0.31.0",
 "sp-std 11.0.0",
 "sp-trie 25.0.0",
]

[[package]]
name = "pallet-session"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8474b62b6b7622f891e83d922a589e2ad5be5471f5ca47d45831a797dba0b3f4"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-timestamp 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
 "sp-state-machine 0.43.0",
 "sp-trie 37.0.0",
]

[[package]]
name = "pallet-session-benchmarking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aadce7df0fee981721983795919642648b846dab5ab9096f82c2cea781007d0"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-session 38.0.0",
 "pallet-staking 38.0.0",
 "parity-scale-codec",
 "rand 0.8.5",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
]

[[package]]
name = "pallet-skip-feeless-payment"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8c2cb0dae13d2c2d2e76373f337d408468f571459df1900cbd7458f21cf6c01"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-society"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1dc69fea8a8de343e71691f009d5fece6ae302ed82b7bb357882b2ea6454143"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "rand_chacha 0.3.1",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-staking"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a1b649d9b95f842258b2e811960bf5e08285180e912303bee6b13c256a466c4"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-election-provider-support 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "pallet-authorship 24.0.0",
 "pallet-session 24.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-application-crypto 26.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-staking"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c870d123f4f053b56af808a4beae1ffc4309a696e829796c26837936c926db3b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-authorship 38.0.0",
 "pallet-session 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-application-crypto 38.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-staking-reward-fn"
version = "15.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a280ef9dfb602b5f39e448d1c7d5922c154b1aee20e42c0740ffef8e2a58f8d1"
dependencies = [
 "log",
 "sp-arithmetic 19.0.0",
]

[[package]]
name = "pallet-staking-reward-fn"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "988a7ebeacc84d4bdb0b12409681e956ffe35438447d8f8bc78db547cffb6ebc"
dependencies = [
 "log",
 "sp-arithmetic 26.0.0",
]

[[package]]
name = "pallet-staking-runtime-api"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7298559ef3a6b2f5dfbe9a3b8f3d22f2ff9b073c97f4c4853d2b316d973e72d"
dependencies = [
 "parity-scale-codec",
 "sp-api 34.0.0",
 "sp-staking 36.0.0",
]

[[package]]
name = "pallet-state-trie-migration"
version = "40.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138c15b4200b9dc4c3e031def6a865a235cdc76ff91ee96fba19ca1787c9dda6"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-statement"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e03e147efa900e75cd106337f36da3d7dcd185bd9e5f5c3df474c08c3c37d16"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-statement-store",
]

[[package]]
name = "pallet-sudo"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1574fe2aed3d52db4a389b77b53d8c9758257b121e3e7bbe24c4904e11681e0e"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-timestamp"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dac4e66316d53673471420fb887b6a74e2507df169ced62584507ff0fb065c6b"
dependencies = [
 "docify",
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-inherents 22.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-storage 16.0.0",
 "sp-timestamp 22.0.0",
]

[[package]]
name = "pallet-timestamp"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9ba9b71bbfd33ae672f23ba7efaeed2755fdac37b8f946cb7474fc37841b7e1"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-storage 21.0.0",
 "sp-timestamp 34.0.0",
]

[[package]]
name = "pallet-tips"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa1d4371a70c309ba11624933f8f5262fe4edad0149c556361d31f26190da936"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-treasury 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-transaction-payment"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4cbb78b8499af1d338072950e4aef6acf3cc630afdb8e19b00306e5252d0386"
dependencies = [
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-transaction-payment"
version = "30.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d598d0ad779d19fa44ce6f80c57192537fa9f84995953bf2a8c104b7676b6b7"
dependencies = [
 "frame-support 30.0.0",
 "frame-system 30.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "pallet-transaction-payment"
version = "38.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6cdb86580c72b58145f9cddba21a0c1814742ca56abc9caac3c1ac72f6bde649"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-transaction-payment-rpc-runtime-api"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49fdf5ab71e9dbcadcf7139736b6ea6bac8ec4a83985d46cbd130e1eec770e41"
dependencies = [
 "pallet-transaction-payment 38.0.2",
 "parity-scale-codec",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
]

[[package]]
name = "pallet-transaction-storage"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8c337a972a6a796c0a0acc6c03b5e02901c43ad721ce79eb87b45717d75c93b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-transaction-storage-proof",
]

[[package]]
name = "pallet-treasury"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd64a50b82946d4ccf2178b7f3927ebac562b2ef31cecda53d31f3ff53a57c4"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "impl-trait-for-tuples",
 "pallet-balances 24.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-treasury"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98bfdd3bb9b58fb010bcd419ff5bf940817a8e404cdbf7886a53ac730f5dda2b"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-tx-pause"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cee153f5be5efc84ebd53aa581e5361cde17dc3669ef80d8ad327f4041d89ebe"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-proxy",
 "pallet-utility",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-uniques"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2b13cdaedf2d5bd913a5f6e637cb52b5973d8ed4b8d45e56d921bc4d627006f"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-utility"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fdcade6efc0b66fc7fc4138964802c02d0ffb7380d894e26b9dd5073727d2b3"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-vesting"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1da7d0e09ddc3decc4abe6adca41a24325a458040b1ecdd246143796af2d47b8"
dependencies = [
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "pallet-vesting"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "807df2ef13ab6bf940879352c3013bfa00b670458b4c125c2f60e5753f68e3d5"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-whitelist"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ef17df925290865cf37096dd0cb76f787df11805bba01b1d0ca3e106d06280b"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "pallet-xcm"
version = "17.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "989676964dbda5f5275650fbdcd3894fe7fac626d113abf89d572b4952adcc36"
dependencies = [
 "bounded-collections 0.2.4",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "tracing",
 "xcm-runtime-apis",
]

[[package]]
name = "pallet-xcm-benchmarks"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2da423463933b42f4a4c74175f9e9295a439de26719579b894ce533926665e4a"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "pallet-xcm-bridge-hub"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bdb76fff08633830063a4cb36664f0cf2f926ac0da02ee439d4f521763e26b7"
dependencies = [
 "bp-messages",
 "bp-runtime",
 "bp-xcm-bridge-hub",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-bridge-messages",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "pallet-xcm-bridge-hub-router"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fabf1fdcf451ac79995f11cb9b6a0761924c57bb79442c2d91b3bbefe4dfa081"
dependencies = [
 "bp-xcm-bridge-hub-router",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
]

[[package]]
name = "parachains-common"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9460a69f409be27c62161d8b4d36ffc32735d09a4f9097f9c789db0cca7196c"
dependencies = [
 "cumulus-primitives-core",
 "cumulus-primitives-utility",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-asset-tx-payment",
 "pallet-assets",
 "pallet-authorship 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-collator-selection",
 "pallet-message-queue 41.0.2",
 "pallet-xcm",
 "parity-scale-codec",
 "polkadot-primitives 16.0.0",
 "scale-info",
 "sp-consensus-aura",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "staging-parachain-info",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
 "substrate-wasm-builder",
]

[[package]]
name = "parachains-runtimes-test-utils"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "287d2db0a2d19466caa579a69f021bfdc6fa352f382c8395dade58d1d0c6adfe"
dependencies = [
 "cumulus-pallet-parachain-system",
 "cumulus-pallet-xcmp-queue",
 "cumulus-primitives-core",
 "cumulus-primitives-parachain-inherent",
 "cumulus-test-relay-sproof-builder",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-collator-selection",
 "pallet-session 38.0.0",
 "pallet-timestamp 37.0.0",
 "pallet-xcm",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "sp-consensus-aura",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-tracing 17.0.1",
 "staging-parachain-info",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
 "substrate-wasm-builder",
]

[[package]]
name = "parity-bip39"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e69bf016dc406eff7d53a7d3f7cf1c2e72c82b9088aac1118591e36dd2cd3e9"
dependencies = [
 "bitcoin_hashes 0.13.0",
 "rand 0.8.5",
 "rand_core 0.6.4",
 "serde",
 "unicode-normalization",
]

[[package]]
name = "parity-bytes"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16b56e3a2420138bdb970f84dfb9c774aea80fa0e7371549eedec0d80c209c67"

[[package]]
name = "parity-scale-codec"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9fde3d0718baf5bc92f577d652001da0f8d54cd03a7974e118d04fc888dc23d"
dependencies = [
 "arrayvec 0.7.4",
 "bitvec",
 "byte-slice-cast",
 "bytes",
 "const_format",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive",
 "rustversion",
 "serde",
]

[[package]]
name = "parity-scale-codec-derive"
version = "3.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581c837bb6b9541ce7faa9377c20616e4fb7650f6b0f68bc93c827ee504fb7b3"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "parity-util-mem"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d32c34f4f5ca7f9196001c0aba5a1f9a5a12382c8944b8b0f90233282d1e8f8"
dependencies = [
 "cfg-if",
 "ethereum-types",
 "hashbrown 0.12.3",
 "impl-trait-for-tuples",
 "lru 0.8.1",
 "parity-util-mem-derive",
 "parking_lot",
 "primitive-types 0.12.2",
 "smallvec",
 "winapi",
]

[[package]]
name = "parity-util-mem-derive"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f557c32c6d268a07c921471619c0295f5efad3a0e76d4f97a05c091a51d110b2"
dependencies = [
 "proc-macro2",
 "syn 1.0.109",
 "synstructure 0.12.6",
]

[[package]]
name = "parity-wasm"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1ad0aff30c1da14b1254fcb2af73e1fa9a28670e584a626f53a369d0e157304"

[[package]]
name = "parking"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb813b8af86854136c6922af0598d719255ecb2179515e6e7730d468f05c9cae"

[[package]]
name = "parking_lot"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"
dependencies = [
 "lock_api",
 "parking_lot_core",
]

[[package]]
name = "parking_lot_core"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c42a9226546d68acdd9c0a280d17ce19bfe27a46bf68784e4066115788d008e"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.4.1",
 "smallvec",
 "windows-targets 0.48.5",
]

[[package]]
name = "paseo-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "subxt",
]

[[package]]
name = "password-hash"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "346f04948ba92c43e8469c1ee6736c7563d71012b17d40745260fe106aac2166"
dependencies = [
 "base64ct",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pbkdf2"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d95f5254224e617595d2cc3cc73ff0a5eaf2637519e25f03388154e9378b6ffa"
dependencies = [
 "crypto-mac 0.11.0",
]

[[package]]
name = "pbkdf2"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83a0692ec44e4cf1ef28ca317f14f8f07da2d95ec3fa01f86e4467b725e60917"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "pbkdf2"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ed6a7761f76e3b9f92dfb0a60a6a6477c61024b775147ff0973a02653abaf2"
dependencies = [
 "digest 0.10.7",
 "password-hash",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "pest"
version = "2.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "311fb059dee1a7b802f036316d790138c613a4e8b180c822e3925a662e9f0c95"
dependencies = [
 "memchr",
 "thiserror",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f73541b156d32197eecda1a4014d7f868fd2bcb3c550d5386087cfba442bf69c"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c35eeed0a3fab112f75165fdc026b3913f4183133f19b49be773ac9ea966e8bd"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "pest_meta"
version = "2.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2adbf29bb9776f28caece835398781ab24435585fe0d4dc1374a61db5accedca"
dependencies = [
 "once_cell",
 "pest",
 "sha2 0.10.8",
]

[[package]]
name = "pin-project"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfe2e71e1471fe07709406bf725f710b02927c9c54b2b5b2ec0e8087d97c327d"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e859e6e5bd50440ab63c47e3ebabc90f26251f7c73c3d3e837b74a1cc3fa67"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "pin-project-lite"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8afb450f006bf6385ca15ef45d71d2288452bc3683ce2e2cacc0d18e4be60b58"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "piper"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "668d31b1c4eba19242f2088b2bf3316b82ca31082a8335764db4e083db7485d4"
dependencies = [
 "atomic-waker",
 "fastrand",
 "futures-io",
]

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der",
 "spki",
]

[[package]]
name = "pkg-config"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "953ec861398dccce10c670dfeaf3ec4911ca479e9c02154b3a215178c5f566f2"

[[package]]
name = "polkadot-ckb-merkle-mountain-range"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4b44320e5f7ce2c18227537a3032ae5b2c476a7e8eddba45333e1011fc31b92"
dependencies = [
 "cfg-if",
 "itertools 0.10.5",
]

[[package]]
name = "polkadot-core-primitives"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "209cc9c9f7ed951bb11a2ff2f592e6b5cd79d6f50311e4f702adce0fcc577152"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core 24.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "polkadot-core-primitives"
version = "9.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89a881f63ab7a652aba19300f95f9341ee245ad45a3f89cf02053ecace474769"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core 30.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "polkadot-core-primitives"
version = "15.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2900d3b857e34c480101618a950c3a4fbcddc8c0d50573d48553376185908b8"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "polkadot-parachain-primitives"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "864de6d7b7ed9c955aa1da6c53585ad75c73618307b569447dd0559f8f9baef9"
dependencies = [
 "bounded-collections 0.1.9",
 "derive_more 0.99.17",
 "frame-support 24.0.0",
 "parity-scale-codec",
 "polkadot-core-primitives 3.0.0",
 "scale-info",
 "serde",
 "sp-core 24.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "polkadot-parachain-primitives"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567c738aa6b8d7eb113fe73e50fb9b6292f818f54da98bb25c7fe73e98d1709a"
dependencies = [
 "bounded-collections 0.2.4",
 "derive_more 0.99.17",
 "parity-scale-codec",
 "polkadot-core-primitives 9.0.0",
 "scale-info",
 "serde",
 "sp-core 30.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "sp-weights 29.0.0",
]

[[package]]
name = "polkadot-parachain-primitives"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52b5648a2e8ce1f9a0f8c41c38def670cefd91932cd793468e1a5b0b0b4e4af1"
dependencies = [
 "bounded-collections 0.2.4",
 "derive_more 0.99.17",
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
]

[[package]]
name = "polkadot-primitives"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9de3d7ae1191401fe2476a69dece0853011d2c9fdcb7eb194cdead641f395abb"
dependencies = [
 "bitvec",
 "hex-literal",
 "parity-scale-codec",
 "polkadot-core-primitives 3.0.0",
 "polkadot-parachain-primitives 2.0.0",
 "scale-info",
 "serde",
 "sp-api 22.0.0",
 "sp-application-crypto 26.0.0",
 "sp-arithmetic 19.0.0",
 "sp-authority-discovery 22.0.0",
 "sp-consensus-slots 0.28.0",
 "sp-core 24.0.0",
 "sp-inherents 22.0.0",
 "sp-io 26.0.0",
 "sp-keystore 0.30.0",
 "sp-runtime 27.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "polkadot-primitives"
version = "15.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b57bc055fa389372ec5fc0001b99aeffd50f3fd379280ce572d935189bb58dd8"
dependencies = [
 "bitvec",
 "hex-literal",
 "log",
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-authority-discovery 34.0.0",
 "sp-consensus-slots 0.40.1",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-staking 34.0.0",
]

[[package]]
name = "polkadot-primitives"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bb20b75d33212150242d39890d7ededab55f1084160c337f15d0eb8ca8c3ad4"
dependencies = [
 "bitvec",
 "hex-literal",
 "log",
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-authority-discovery 34.0.0",
 "sp-consensus-slots 0.40.1",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "polkadot-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "subxt",
]

[[package]]
name = "polkadot-runtime-common"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac922a958e3e1435d7adffe49c1528b6396baf668ab45f635b79bff750053543"
dependencies = [
 "bitvec",
 "frame-benchmarking 24.0.0",
 "frame-election-provider-support 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "impl-trait-for-tuples",
 "libsecp256k1",
 "log",
 "pallet-authorship 24.0.0",
 "pallet-balances 24.0.0",
 "pallet-election-provider-multi-phase 23.0.0",
 "pallet-fast-unstake 23.0.0",
 "pallet-session 24.0.0",
 "pallet-staking 24.0.0",
 "pallet-staking-reward-fn 15.0.0",
 "pallet-timestamp 23.0.0",
 "pallet-transaction-payment 24.0.0",
 "pallet-treasury 23.0.0",
 "pallet-vesting 24.0.0",
 "parity-scale-codec",
 "polkadot-primitives 3.0.0",
 "polkadot-runtime-parachains 3.0.0",
 "rustc-hex",
 "scale-info",
 "serde",
 "serde_derive",
 "slot-range-helper 3.0.0",
 "sp-api 22.0.0",
 "sp-core 24.0.0",
 "sp-inherents 22.0.0",
 "sp-io 26.0.0",
 "sp-npos-elections 22.0.0",
 "sp-runtime 27.0.0",
 "sp-session 23.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
 "staging-xcm 3.0.1",
 "static_assertions",
]

[[package]]
name = "polkadot-runtime-common"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc15154ba5ca55d323fcf7af0f5dcd39d58dcb4dfac3d9b30404840a6d8bbde4"
dependencies = [
 "bitvec",
 "frame-benchmarking 38.0.0",
 "frame-election-provider-support 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "libsecp256k1",
 "log",
 "pallet-asset-rate",
 "pallet-authorship 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-broker",
 "pallet-election-provider-multi-phase 37.0.0",
 "pallet-fast-unstake 37.0.0",
 "pallet-identity",
 "pallet-session 38.0.0",
 "pallet-staking 38.0.0",
 "pallet-staking-reward-fn 22.0.0",
 "pallet-timestamp 37.0.0",
 "pallet-transaction-payment 38.0.2",
 "pallet-treasury 37.0.0",
 "pallet-vesting 38.0.0",
 "parity-scale-codec",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "rustc-hex",
 "scale-info",
 "serde",
 "serde_derive",
 "slot-range-helper 15.0.0",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-npos-elections 34.0.0",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "static_assertions",
]

[[package]]
name = "polkadot-runtime-constants"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d4a7abfe0dff335456790ac8edc342737dffbd67013a38b7bec2f6b523cc175"
dependencies = [
 "frame-support 24.0.0",
 "polkadot-primitives 3.0.0",
 "polkadot-runtime-common 3.0.0",
 "smallvec",
 "sp-core 24.0.0",
 "sp-runtime 27.0.0",
 "sp-weights 23.0.0",
]

[[package]]
name = "polkadot-runtime-metrics"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93dbff21f9ce253f4cd976a972c5b4b18150cecb6fdae195a975e51cf3c797df"
dependencies = [
 "bs58",
 "frame-benchmarking 24.0.0",
 "parity-scale-codec",
 "polkadot-primitives 3.0.0",
 "sp-std 11.0.0",
 "sp-tracing 13.0.0",
]

[[package]]
name = "polkadot-runtime-metrics"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c306f1ace7644a24de860479f92cf8d6467393bb0c9b0777c57e2d42c9d452a"
dependencies = [
 "bs58",
 "frame-benchmarking 38.0.0",
 "parity-scale-codec",
 "polkadot-primitives 16.0.0",
 "sp-tracing 17.0.1",
]

[[package]]
name = "polkadot-runtime-parachains"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ffa4bade116bef54be48bb1ead3917d84478f43184d5f9ae001ec333d7d7d66"
dependencies = [
 "bitflags 1.3.2",
 "bitvec",
 "derive_more 0.99.17",
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "frame-system 24.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-authority-discovery 24.0.0",
 "pallet-authorship 24.0.0",
 "pallet-babe 24.0.0",
 "pallet-balances 24.0.0",
 "pallet-message-queue 27.0.0",
 "pallet-session 24.0.0",
 "pallet-staking 24.0.0",
 "pallet-timestamp 23.0.0",
 "pallet-vesting 24.0.0",
 "parity-scale-codec",
 "polkadot-core-primitives 3.0.0",
 "polkadot-parachain-primitives 2.0.0",
 "polkadot-primitives 3.0.0",
 "polkadot-runtime-metrics 3.0.0",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rustc-hex",
 "scale-info",
 "serde",
 "sp-api 22.0.0",
 "sp-application-crypto 26.0.0",
 "sp-core 24.0.0",
 "sp-inherents 22.0.0",
 "sp-io 26.0.0",
 "sp-keystore 0.30.0",
 "sp-runtime 27.0.0",
 "sp-session 23.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
 "staging-xcm 3.0.1",
 "staging-xcm-executor 3.0.0",
]

[[package]]
name = "polkadot-runtime-parachains"
version = "17.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d4cdf181c2419b35c2cbde813da2d8ee777b69b4a6fa346b962d144e3521976"
dependencies = [
 "bitflags 1.3.2",
 "bitvec",
 "derive_more 0.99.17",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-authority-discovery 38.0.0",
 "pallet-authorship 38.0.0",
 "pallet-babe 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-broker",
 "pallet-message-queue 41.0.2",
 "pallet-mmr",
 "pallet-session 38.0.0",
 "pallet-staking 38.0.0",
 "pallet-timestamp 37.0.0",
 "pallet-vesting 38.0.0",
 "parity-scale-codec",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-metrics 17.0.0",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "polkadot-sdk"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb819108697967452fa6d8d96ab4c0d48cbaa423b3156499dcb24f1cf95d6775"
dependencies = [
 "asset-test-utils",
 "assets-common",
 "binary-merkle-tree",
 "bp-header-chain",
 "bp-messages",
 "bp-parachains",
 "bp-polkadot",
 "bp-polkadot-core",
 "bp-relayers",
 "bp-runtime",
 "bp-test-utils",
 "bp-xcm-bridge-hub",
 "bp-xcm-bridge-hub-router",
 "bridge-hub-common",
 "bridge-hub-test-utils",
 "bridge-runtime-common",
 "cumulus-pallet-aura-ext",
 "cumulus-pallet-dmp-queue",
 "cumulus-pallet-parachain-system",
 "cumulus-pallet-parachain-system-proc-macro",
 "cumulus-pallet-session-benchmarking",
 "cumulus-pallet-solo-to-para",
 "cumulus-pallet-xcm",
 "cumulus-pallet-xcmp-queue",
 "cumulus-ping",
 "cumulus-primitives-aura",
 "cumulus-primitives-core",
 "cumulus-primitives-parachain-inherent",
 "cumulus-primitives-proof-size-hostfunction",
 "cumulus-primitives-storage-weight-reclaim",
 "cumulus-primitives-timestamp",
 "cumulus-primitives-utility",
 "cumulus-test-relay-sproof-builder",
 "frame-benchmarking 38.0.0",
 "frame-benchmarking-pallet-pov",
 "frame-election-provider-support 38.0.0",
 "frame-executive",
 "frame-metadata-hash-extension",
 "frame-support 38.2.0",
 "frame-support-procedural 30.0.6",
 "frame-system 38.0.0",
 "frame-system-benchmarking",
 "frame-system-rpc-runtime-api",
 "frame-try-runtime",
 "pallet-alliance",
 "pallet-asset-conversion",
 "pallet-asset-conversion-ops",
 "pallet-asset-conversion-tx-payment",
 "pallet-asset-rate",
 "pallet-asset-tx-payment",
 "pallet-assets",
 "pallet-assets-freezer",
 "pallet-atomic-swap",
 "pallet-aura",
 "pallet-authority-discovery 38.0.0",
 "pallet-authorship 38.0.0",
 "pallet-babe 38.0.0",
 "pallet-bags-list",
 "pallet-balances 39.0.1",
 "pallet-beefy",
 "pallet-beefy-mmr",
 "pallet-bounties",
 "pallet-bridge-grandpa",
 "pallet-bridge-messages",
 "pallet-bridge-parachains",
 "pallet-bridge-relayers",
 "pallet-broker",
 "pallet-child-bounties",
 "pallet-collator-selection",
 "pallet-collective",
 "pallet-collective-content",
 "pallet-contracts",
 "pallet-contracts-mock-network",
 "pallet-conviction-voting",
 "pallet-core-fellowship",
 "pallet-delegated-staking",
 "pallet-democracy",
 "pallet-dev-mode",
 "pallet-election-provider-multi-phase 37.0.0",
 "pallet-election-provider-support-benchmarking 37.0.0",
 "pallet-elections-phragmen",
 "pallet-fast-unstake 37.0.0",
 "pallet-glutton",
 "pallet-grandpa",
 "pallet-identity",
 "pallet-im-online",
 "pallet-indices",
 "pallet-insecure-randomness-collective-flip",
 "pallet-lottery",
 "pallet-membership",
 "pallet-message-queue 41.0.2",
 "pallet-migrations",
 "pallet-mixnet",
 "pallet-mmr",
 "pallet-multisig",
 "pallet-nft-fractionalization",
 "pallet-nfts",
 "pallet-nfts-runtime-api",
 "pallet-nis",
 "pallet-node-authorization",
 "pallet-nomination-pools",
 "pallet-nomination-pools-benchmarking",
 "pallet-nomination-pools-runtime-api",
 "pallet-offences",
 "pallet-offences-benchmarking",
 "pallet-paged-list",
 "pallet-parameters",
 "pallet-preimage",
 "pallet-proxy",
 "pallet-ranked-collective",
 "pallet-recovery",
 "pallet-referenda",
 "pallet-remark",
 "pallet-revive",
 "pallet-revive-fixtures",
 "pallet-revive-mock-network",
 "pallet-root-offences",
 "pallet-root-testing",
 "pallet-safe-mode",
 "pallet-salary",
 "pallet-scheduler",
 "pallet-scored-pool",
 "pallet-session 38.0.0",
 "pallet-session-benchmarking",
 "pallet-skip-feeless-payment",
 "pallet-society",
 "pallet-staking 38.0.0",
 "pallet-staking-reward-fn 22.0.0",
 "pallet-staking-runtime-api",
 "pallet-state-trie-migration",
 "pallet-statement",
 "pallet-sudo",
 "pallet-timestamp 37.0.0",
 "pallet-tips",
 "pallet-transaction-payment 38.0.2",
 "pallet-transaction-payment-rpc-runtime-api",
 "pallet-transaction-storage",
 "pallet-treasury 37.0.0",
 "pallet-tx-pause",
 "pallet-uniques",
 "pallet-utility",
 "pallet-vesting 38.0.0",
 "pallet-whitelist",
 "pallet-xcm",
 "pallet-xcm-benchmarks",
 "pallet-xcm-bridge-hub",
 "pallet-xcm-bridge-hub-router",
 "parachains-common",
 "parachains-runtimes-test-utils",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-common 17.0.0",
 "polkadot-runtime-metrics 17.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "polkadot-sdk-frame",
 "sc-executor",
 "slot-range-helper 15.0.0",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-core 0.10.0",
 "snowbridge-ethereum 0.9.0",
 "snowbridge-outbound-queue-merkle-tree",
 "snowbridge-outbound-queue-runtime-api",
 "snowbridge-pallet-ethereum-client",
 "snowbridge-pallet-ethereum-client-fixtures",
 "snowbridge-pallet-inbound-queue",
 "snowbridge-pallet-inbound-queue-fixtures",
 "snowbridge-pallet-outbound-queue",
 "snowbridge-pallet-system",
 "snowbridge-router-primitives 0.16.1",
 "snowbridge-runtime-common",
 "snowbridge-runtime-test-common",
 "snowbridge-system-runtime-api",
 "sp-api 34.0.0",
 "sp-api-proc-macro 20.0.0",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-authority-discovery 34.0.0",
 "sp-block-builder",
 "sp-consensus-aura",
 "sp-consensus-babe 0.40.0",
 "sp-consensus-beefy",
 "sp-consensus-grandpa",
 "sp-consensus-pow",
 "sp-consensus-slots 0.40.1",
 "sp-core 34.0.0",
 "sp-core-hashing 16.0.0",
 "sp-crypto-ec-utils",
 "sp-crypto-hashing",
 "sp-debug-derive 14.0.0",
 "sp-externalities 0.29.0",
 "sp-genesis-builder 0.15.1",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-keyring",
 "sp-keystore 0.40.0",
 "sp-metadata-ir 0.7.0",
 "sp-mixnet",
 "sp-mmr-primitives",
 "sp-npos-elections 34.0.0",
 "sp-offchain",
 "sp-runtime 39.0.5",
 "sp-runtime-interface 28.0.0",
 "sp-session 36.0.0",
 "sp-staking 36.0.0",
 "sp-state-machine 0.43.0",
 "sp-statement-store",
 "sp-std 14.0.0",
 "sp-storage 21.0.0",
 "sp-timestamp 34.0.0",
 "sp-tracing 17.0.1",
 "sp-transaction-pool",
 "sp-transaction-storage-proof",
 "sp-trie 37.0.0",
 "sp-version 37.0.0",
 "sp-wasm-interface 21.0.1",
 "sp-weights 31.0.0",
 "staging-parachain-info",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "substrate-bip39 0.6.0",
 "testnet-parachains-constants",
 "xcm-runtime-apis",
]

[[package]]
name = "polkadot-sdk-frame"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbdeb15ce08142082461afe1a62c15f7ce10a731d91b203ad6a8dc8d2e4a6a54"
dependencies = [
 "docify",
 "frame-benchmarking 38.0.0",
 "frame-executive",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "frame-system-benchmarking",
 "frame-system-rpc-runtime-api",
 "frame-try-runtime",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-arithmetic 26.0.0",
 "sp-block-builder",
 "sp-consensus-aura",
 "sp-consensus-grandpa",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-io 38.0.0",
 "sp-offchain",
 "sp-runtime 39.0.5",
 "sp-session 36.0.0",
 "sp-storage 21.0.0",
 "sp-transaction-pool",
 "sp-version 37.0.0",
]

[[package]]
name = "polkavm"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a3693e5efdb2bf74e449cd25fd777a28bd7ed87e41f5d5da75eb31b4de48b94"
dependencies = [
 "libc",
 "log",
 "polkavm-assembler 0.9.0",
 "polkavm-common 0.9.0",
 "polkavm-linux-raw 0.9.0",
]

[[package]]
name = "polkavm"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7ec0c5935f2eff23cfc4653002f4f8d12b37f87a720e0631282d188c32089d6"
dependencies = [
 "libc",
 "log",
 "polkavm-assembler 0.10.0",
 "polkavm-common 0.10.0",
 "polkavm-linux-raw 0.10.0",
]

[[package]]
name = "polkavm-assembler"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fa96d6d868243acc12de813dd48e756cbadcc8e13964c70d272753266deadc1"
dependencies = [
 "log",
]

[[package]]
name = "polkavm-assembler"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8e4fd5a43100bf1afe9727b8130d01f966f5cfc9144d5604b21e795c2bcd80e"
dependencies = [
 "log",
]

[[package]]
name = "polkavm-common"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92c99f7eee94e7be43ba37eef65ad0ee8cbaf89b7c00001c3f6d2be985cb1817"

[[package]]
name = "polkavm-common"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d9428a5cfcc85c5d7b9fc4b6a18c4b802d0173d768182a51cc7751640f08b92"
dependencies = [
 "log",
]

[[package]]
name = "polkavm-common"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0097b48bc0bedf9f3f537ce8f37e8f1202d8d83f9b621bdb21ff2c59b9097c50"
dependencies = [
 "log",
 "polkavm-assembler 0.10.0",
]

[[package]]
name = "polkavm-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79fa916f7962348bd1bb1a65a83401675e6fc86c51a0fdbcf92a3108e58e6125"
dependencies = [
 "polkavm-derive-impl-macro 0.8.0",
]

[[package]]
name = "polkavm-derive"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae8c4bea6f3e11cd89bb18bcdddac10bd9a24015399bd1c485ad68a985a19606"
dependencies = [
 "polkavm-derive-impl-macro 0.9.0",
]

[[package]]
name = "polkavm-derive"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dcc701385c08c31bdb0569f0c51a290c580d892fa77f1dd88a7352a62679ecf"
dependencies = [
 "polkavm-derive-impl-macro 0.10.0",
]

[[package]]
name = "polkavm-derive-impl"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c10b2654a8a10a83c260bfb93e97b262cf0017494ab94a65d389e0eda6de6c9c"
dependencies = [
 "polkavm-common 0.8.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-derive-impl"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c4fdfc49717fb9a196e74a5d28e0bc764eb394a2c803eb11133a31ac996c60c"
dependencies = [
 "polkavm-common 0.9.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-derive-impl"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7855353a5a783dd5d09e3b915474bddf66575f5a3cf45dec8d1c5e051ba320dc"
dependencies = [
 "polkavm-common 0.10.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-derive-impl-macro"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15e85319a0d5129dc9f021c62607e0804f5fb777a05cdda44d750ac0732def66"
dependencies = [
 "polkavm-derive-impl 0.8.0",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-derive-impl-macro"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ba81f7b5faac81e528eb6158a6f3c9e0bb1008e0ffa19653bc8dea925ecb429"
dependencies = [
 "polkavm-derive-impl 0.9.0",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-derive-impl-macro"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9324fe036de37c17829af233b46ef6b5562d4a0c09bb7fdb9f8378856dee30cf"
dependencies = [
 "polkavm-derive-impl 0.10.0",
 "syn 2.0.98",
]

[[package]]
name = "polkavm-linker"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c7be503e60cf56c0eb785f90aaba4b583b36bff00e93997d93fef97f9553c39"
dependencies = [
 "gimli 0.28.1",
 "hashbrown 0.14.5",
 "log",
 "object 0.32.2",
 "polkavm-common 0.9.0",
 "regalloc2 0.9.3",
 "rustc-demangle",
]

[[package]]
name = "polkavm-linker"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d704edfe7bdcc876784f19436d53d515b65eb07bc9a0fae77085d552c2dbbb5"
dependencies = [
 "gimli 0.28.1",
 "hashbrown 0.14.5",
 "log",
 "object 0.36.7",
 "polkavm-common 0.10.0",
 "regalloc2 0.9.3",
 "rustc-demangle",
]

[[package]]
name = "polkavm-linux-raw"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26e85d3456948e650dff0cfc85603915847faf893ed1e66b020bb82ef4557120"

[[package]]
name = "polkavm-linux-raw"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26e45fa59c7e1bb12ef5289080601e9ec9b31435f6e32800a5c90c132453d126"

[[package]]
name = "polling"
version = "3.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24f040dee2588b4963afb4e420540439d126f73fdacf4a9c486a96d840bac3c9"
dependencies = [
 "cfg-if",
 "concurrent-queue",
 "pin-project-lite",
 "rustix 0.38.31",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "poly1305"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8159bd90725d2df49889a078b54f4f79e87f1f8a8444194cdca81d38f5393abf"
dependencies = [
 "cpufeatures",
 "opaque-debug 0.3.0",
 "universal-hash",
]

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "opaque-debug 0.3.0",
 "universal-hash",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b40af805b3121feab8a3c29f04d8ad262fa8e0561883e7653e024ae4479e6de"

[[package]]
name = "primitive-types"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b34d9fd68ae0b74a41b21c03c2f62847aa0ffea044eee893b4c140b37e244e2"
dependencies = [
 "fixed-hash",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "scale-info",
 "uint 0.9.5",
]

[[package]]
name = "primitive-types"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d15600a7d856470b7d278b3fe0e311fe28c2526348549f8ef2ff7db3299c87f5"
dependencies = [
 "fixed-hash",
 "impl-codec 0.7.1",
 "impl-serde 0.5.0",
 "scale-info",
 "uint 0.10.0",
]

[[package]]
name = "proc-macro-crate"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f4c021e1093a56626774e81216a4ce732a735e5bad4868a03f3ed65ca0c3919"
dependencies = [
 "once_cell",
 "toml_edit 0.19.15",
]

[[package]]
name = "proc-macro-crate"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d37c51ca738a55da99dc0c4a34860fd675453b8b36209178c2249bb13651284"
dependencies = [
 "toml_edit 0.21.1",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "proc-macro-warning"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d1eaa7fa0aa1929ffdf7eeb6eac234dde6268914a14ad44d23521ab6a9b258e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "proc-macro-warning"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "834da187cfe638ae8abb0203f0b33e5ccdb02a28e7199f2f47b3e2754f50edca"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "proc-macro2"
version = "1.0.93"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60946a68e5f9d28b0dc1c21bb8a97ee7d018a8b322fa57838ba31cc878e22d99"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proptest"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31b476131c3c86cb68032fdc5cb6d5a1045e3e42d96b69fa599fd77701e1f5bf"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.8.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_xorshift",
 "regex-syntax 0.8.2",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "psm"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5787f7cda34e3033a72192c018bc5883100330f362ef279a8cbccfce8bb4e874"
dependencies = [
 "cc",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quote"
version = "1.0.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e4dccaaaf89514f546c693ddc140f729f958c247918a13380cccc6078391acc"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.16",
 "libc",
 "rand_chacha 0.2.2",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c8ed856279c9737206bf725bf36935d8666ead7aa69b52be55af369d193402"
dependencies = [
 "ppv-lite86",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.16",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.12",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "rawpointer"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a357793950651c4ed0f3f52338f53b2f809f32d83a07f72909fa13e4c6c1e3"

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "redox_syscall"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4722d768eff46b75989dd134e5c353f0d6296e5aaa3132e776cbdb56be7731aa"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b568323e98e49e2a0899dcee453dd679fae22d69adf9b11dd508d1549b7e2f"
dependencies = [
 "bitflags 2.8.0",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.12",
 "libredox",
 "thiserror",
]

[[package]]
name = "ref-cast"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a0ae411dbe946a674d89546582cea4ba2bb8defac896622d6496f14c23ba5cf"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1165225c21bff1f3bbce98f5a1f889949bc902d3575308cc7b0de30b4f6d27c7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "regalloc2"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80535183cae11b149d618fbd3c37e38d7cda589d82d7769e196ca9a9042d7621"
dependencies = [
 "fxhash",
 "log",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regalloc2"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad156d539c879b7a24a363a2016d77961786e71f48f2e2fc8302a92abd2429a6"
dependencies = [
 "hashbrown 0.13.2",
 "log",
 "rustc-hash 1.1.0",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regex"
version = "1.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62dbe01f0b06f9d8dc7d49e05a0785f153b00b2c227856282f671e0318c9b15"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.5",
 "regex-syntax 0.8.2",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bb987efffd3c6d0d8f5f89510bb458559eab11e4f869acb20bf845e016259cd"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.2",
]

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08c74e62047bb2de4ff487b251e4a92e24f48745648451635cec7d591162d9f"

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac 0.12.1",
 "subtle",
]

[[package]]
name = "ring"
version = "0.17.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c17fa4cb658e3583423e915b9f3acc01cceaee1860e33d59ebae66adc3a2dc0d"
dependencies = [
 "cc",
 "cfg-if",
 "getrandom 0.2.12",
 "libc",
 "spin",
 "untrusted",
 "windows-sys 0.52.0",
]

[[package]]
name = "rlp"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb919243f34364b6bd2fc10ef797edbfa75f33c252e7998527479c6d6b47e1ec"
dependencies = [
 "bytes",
 "rustc-hex",
]

[[package]]
name = "rococo-runtime-constants"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1ec6683a2e52fe3be2eaf942a80619abd99eb36e973c5ab4489a2f3b100db5c"
dependencies = [
 "frame-support 38.2.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-common 17.0.0",
 "smallvec",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
]

[[package]]
name = "ruint"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "608a5726529f2f0ef81b8fde9873c4bb829d6b5b5ca6be4d97345ddf0749c825"
dependencies = [
 "alloy-rlp",
 "ark-ff 0.3.0",
 "ark-ff 0.4.2",
 "bytes",
 "fastrlp",
 "num-bigint",
 "num-traits",
 "parity-scale-codec",
 "primitive-types 0.12.2",
 "proptest",
 "rand 0.8.5",
 "rlp",
 "ruint-macro",
 "serde",
 "valuable",
 "zeroize",
]

[[package]]
name = "ruint-macro"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e666a5496a0b2186dbcd0ff6106e29e093c15591bde62c20d3842007c6978a09"

[[package]]
name = "rustc-demangle"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d626bb9dae77e28219937af045c257c28bfd3f69333c512553507f5f9798cb76"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc-hex"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e75f6a532d0fd9f7f13144f392b6ad56a32696bfcd9c78f797f16bbb6f072d6"

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver 0.9.0",
]

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver 1.0.22",
]

[[package]]
name = "rustix"
version = "0.36.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "305efbd14fde4139eb501df5f136994bb520b033fa9fbdce287507dc23b8c7ed"
dependencies = [
 "bitflags 1.3.2",
 "errno",
 "io-lifetimes",
 "libc",
 "linux-raw-sys 0.1.4",
 "windows-sys 0.45.0",
]

[[package]]
name = "rustix"
version = "0.38.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ea3e1a662af26cd7a3ba09c0297a31af215563ecf42817c98df621387f4e949"
dependencies = [
 "bitflags 2.8.0",
 "errno",
 "libc",
 "linux-raw-sys 0.4.13",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustls"
version = "0.23.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47796c98c480fce5406ef69d1c76378375492c3b0a0de587be0c1d9feb12f395"
dependencies = [
 "log",
 "once_cell",
 "ring",
 "rustls-pki-types",
 "rustls-webpki",
 "subtle",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f1fb85efa936c42c6d5fc28d2629bb51e4b2f4b8a5211e297d599cc5a093792"
dependencies = [
 "openssl-probe",
 "rustls-pemfile",
 "rustls-pki-types",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c333bb734fcdedcea57de1602543590f545f127dc8b533324318fd492c5c70b"
dependencies = [
 "base64 0.21.7",
 "rustls-pki-types",
]

[[package]]
name = "rustls-pki-types"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "917ce264624a4b4db1c364dcc35bfca9ded014d0a958cd47ad3e960e988ea51c"

[[package]]
name = "rustls-platform-verifier"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afbb878bdfdf63a336a5e63561b1835e7a8c91524f51621db870169eac84b490"
dependencies = [
 "core-foundation",
 "core-foundation-sys",
 "jni",
 "log",
 "once_cell",
 "rustls",
 "rustls-native-certs",
 "rustls-platform-verifier-android",
 "rustls-webpki",
 "security-framework",
 "security-framework-sys",
 "webpki-roots",
 "winapi",
]

[[package]]
name = "rustls-platform-verifier-android"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f87165f0995f63a9fbeea62b64d10b4d9d8e78ec6d7d51fb2125fda7bb36788f"

[[package]]
name = "rustls-webpki"
version = "0.102.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ca1bc8749bd4cf37b5ce386cc146580777b4e8572c7b97baf22c83f444bee9"
dependencies = [
 "ring",
 "rustls-pki-types",
 "untrusted",
]

[[package]]
name = "rustversion"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eded382c5f5f786b989652c49544c4877d9f015cc22e145a5ea8ea66c2921cd2"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "ruzstd"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5174a470eeb535a721ae9fdd6e291c2411a906b96592182d05217591d5c5cf7b"
dependencies = [
 "byteorder",
 "derive_more 0.99.17",
]

[[package]]
name = "ryu"
version = "1.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e86697c916019a8588c99b5fac3cead74ec0b4b819707a682fd4d23fa0ce1ba1"

[[package]]
name = "safe-mix"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d3d055a2582e6b00ed7a31c1524040aa391092bf636328350813f3a0605215c"
dependencies = [
 "rustc_version 0.2.3",
]

[[package]]
name = "safe_arch"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f398075ce1e6a179b46f51bd88d0598b92b00d3551f1a2d4ac49e771b56ac354"
dependencies = [
 "bytemuck",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "sc-allocator"
version = "29.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b975ee3a95eaacb611e7b415737a7fa2db4d8ad7b880cc1b97371b04e95c7903"
dependencies = [
 "log",
 "sp-core 34.0.0",
 "sp-wasm-interface 21.0.1",
 "thiserror",
]

[[package]]
name = "sc-executor"
version = "0.40.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f0cc0a3728fd033589183460c5a49b2e7545d09dc89a098216ef9e9aadcd9dc"
dependencies = [
 "parity-scale-codec",
 "parking_lot",
 "sc-executor-common",
 "sc-executor-polkavm",
 "sc-executor-wasmtime",
 "schnellru",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
 "sp-io 38.0.0",
 "sp-panic-handler 13.0.1",
 "sp-runtime-interface 28.0.0",
 "sp-trie 37.0.0",
 "sp-version 37.0.0",
 "sp-wasm-interface 21.0.1",
 "tracing",
]

[[package]]
name = "sc-executor-common"
version = "0.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c3b703a33dcb7cddf19176fdf12294b9a6408125836b0f4afee3e6969e7f190"
dependencies = [
 "polkavm 0.9.3",
 "sc-allocator",
 "sp-maybe-compressed-blob",
 "sp-wasm-interface 21.0.1",
 "thiserror",
 "wasm-instrument",
]

[[package]]
name = "sc-executor-polkavm"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26fe58d9cacfab73e5595fa84b80f7bd03efebe54a0574daaeb221a1d1f7ab80"
dependencies = [
 "log",
 "polkavm 0.9.3",
 "sc-executor-common",
 "sp-wasm-interface 21.0.1",
]

[[package]]
name = "sc-executor-wasmtime"
version = "0.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cd498f2f77ec1f861c30804f5bfd796d4afcc8ce44ea1f11bfbe2847551d161"
dependencies = [
 "anyhow",
 "cfg-if",
 "libc",
 "log",
 "parking_lot",
 "rustix 0.36.17",
 "sc-allocator",
 "sc-executor-common",
 "sp-runtime-interface 28.0.0",
 "sp-wasm-interface 21.0.1",
 "wasmtime",
]

[[package]]
name = "scale-bits"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e57b1e7f6b65ed1f04e79a85a57d755ad56d76fdf1e9bddcc9ae14f71fcdcf54"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "scale-type-resolver",
 "serde",
]

[[package]]
name = "scale-decode"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ae9cc099ae85ff28820210732b00f019546f36f33225f509fe25d5816864a0"
dependencies = [
 "derive_more 1.0.0",
 "parity-scale-codec",
 "primitive-types 0.13.1",
 "scale-bits",
 "scale-decode-derive",
 "scale-type-resolver",
 "smallvec",
]

[[package]]
name = "scale-decode-derive"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ed9401effa946b493f9f84dc03714cca98119b230497df6f3df6b84a2b03648"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "scale-encode"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9271284d05d0749c40771c46180ce89905fd95aa72a2a2fddb4b7c0aa424db"
dependencies = [
 "derive_more 1.0.0",
 "parity-scale-codec",
 "primitive-types 0.13.1",
 "scale-bits",
 "scale-encode-derive",
 "scale-type-resolver",
 "smallvec",
]

[[package]]
name = "scale-encode-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "102fbc6236de6c53906c0b262f12c7aa69c2bdc604862c12728f5f4d370bc137"
dependencies = [
 "darling",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "scale-info"
version = "2.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "346a3b32eba2640d17a9cb5927056b08f3de90f65b72fe09402c2ad07d684d0b"
dependencies = [
 "bitvec",
 "cfg-if",
 "derive_more 1.0.0",
 "parity-scale-codec",
 "scale-info-derive",
 "serde",
]

[[package]]
name = "scale-info-derive"
version = "2.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6630024bf739e2179b91fb424b28898baf819414262c5d376677dbff1fe7ebf"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "scale-type-resolver"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0cded6518aa0bd6c1be2b88ac81bf7044992f0f154bfbabd5ad34f43512abcb"
dependencies = [
 "scale-info",
 "smallvec",
]

[[package]]
name = "scale-typegen"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc4c70c7fea2eef1740f0081d3fe385d8bee1eef11e9272d3bec7dc8e5438e0"
dependencies = [
 "proc-macro2",
 "quote",
 "scale-info",
 "syn 2.0.98",
 "thiserror",
]

[[package]]
name = "scale-value"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5e0ef2a0ee1e02a69ada37feb87ea1616ce9808aca072befe2d3131bf28576e"
dependencies = [
 "base58",
 "blake2",
 "derive_more 1.0.0",
 "either",
 "parity-scale-codec",
 "scale-bits",
 "scale-decode",
 "scale-encode",
 "scale-info",
 "scale-type-resolver",
 "serde",
 "yap",
]

[[package]]
name = "schannel"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbc91545643bcf3a0bbb6569265615222618bdf33ce4ffbbd13c4bbd4c093534"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "schnellru"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "356285bbf17bea63d9e52e96bd18f039672ac92b55b8cb997d6162a2a37d1649"
dependencies = [
 "ahash 0.8.11",
 "cfg-if",
 "hashbrown 0.13.2",
]

[[package]]
name = "schnorrkel"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "021b403afe70d81eea68f6ea12f6b3c9588e5d536a94c3bf80f15e7faa267862"
dependencies = [
 "arrayref",
 "arrayvec 0.5.2",
 "curve25519-dalek 2.1.3",
 "getrandom 0.1.16",
 "merlin 2.0.1",
 "rand 0.7.3",
 "rand_core 0.5.1",
 "sha2 0.8.2",
 "subtle",
 "zeroize",
]

[[package]]
name = "schnorrkel"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de18f6d8ba0aad7045f5feae07ec29899c1112584a38509a84ad7b04451eaa0"
dependencies = [
 "aead",
 "arrayref",
 "arrayvec 0.7.4",
 "curve25519-dalek 4.1.3",
 "getrandom_or_panic",
 "merlin 3.0.0",
 "rand_core 0.6.4",
 "serde_bytes",
 "sha2 0.10.8",
 "subtle",
 "zeroize",
]

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scratch"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3cf7c11c38cb994f3d40e8a8cde3bbd1f72a435e4c49e85d6553d8312306152"

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct",
 "der",
 "generic-array 0.14.7",
 "pkcs8",
 "serdect",
 "subtle",
 "zeroize",
]

[[package]]
name = "secp256k1"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b1629c9c557ef9b293568b338dddfc8208c98a18c59d722a9d53f859d9c9b62"
dependencies = [
 "secp256k1-sys 0.6.1",
]

[[package]]
name = "secp256k1"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d24b59d129cdadea20aea4fb2352fa053712e5d713eee47d700cd4b2bc002f10"
dependencies = [
 "secp256k1-sys 0.9.2",
]

[[package]]
name = "secp256k1-sys"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83080e2c2fc1006e625be82e5d1eb6a43b7fd9578b617fcc55814daf286bba4b"
dependencies = [
 "cc",
]

[[package]]
name = "secp256k1-sys"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d1746aae42c19d583c3c1a8c646bfad910498e2051c551a7f2e3c0c9fbb7eb"
dependencies = [
 "cc",
]

[[package]]
name = "secrecy"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bd1c54ea06cfd2f6b63219704de0b9b4f72dcc2b8fdef820be6cd799780e91e"
dependencies = [
 "zeroize",
]

[[package]]
name = "security-framework"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c627723fd09706bacdb5cf41499e95098555af3c3c29d014dc3c458ef6be11c0"
dependencies = [
 "bitflags 2.8.0",
 "core-foundation",
 "core-foundation-sys",
 "libc",
 "num-bigint",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a3186ec9e65071a2095434b1f5bb24838d4e8e130f584c790f6033c79943537"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser 0.10.2",
]

[[package]]
name = "semver"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92d43fe69e652f3df9bdc2b85b2854a0825b86e4fb76bc44d945137d053639ca"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "semver-parser"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0bef5b7f9e0df16536d3961cfb6e84331c065b4066afb39768d0e319411f7"
dependencies = [
 "pest",
]

[[package]]
name = "serde"
version = "1.0.218"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8dfc9d19bdbf6d17e22319da49161d5d0108e4188e8b680aef6299eed22df60"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-big-array"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd31f59f6fe2b0c055371bb2f16d7f0aa7d8881676c04a55b1596d1a17cd10a4"
dependencies = [
 "serde",
]

[[package]]
name = "serde_bytes"
version = "0.11.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b8497c313fd43ab992087548117643f6fcd935cbf36f176ffda0aacf9591734"
dependencies = [
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.218"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f09503e191f4e797cb8aac08e9a4a4695c5edf6a2e70e376d961ddd5c969f82b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "serde_json"
version = "1.0.139"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44f86c3acccc9c65b153fe1b85a3be07fe5515274ec9f0653b4a0875731c72a6"
dependencies = [
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_spanned"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb3622f419d1296904700073ea6cc23ad690adbd66f13ea683df73298736f0c1"
dependencies = [
 "serde",
]

[[package]]
name = "serdect"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a84f14a19e9a014bb9f4512488d9829a68e04ecabffb0f9904cd1ace94598177"
dependencies = [
 "base16ct",
 "serde",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a256f46ea78a0c0d9ff00077504903ac881a1dafdc20da66545699e7776b3e69"
dependencies = [
 "block-buffer 0.7.3",
 "digest 0.8.1",
 "fake-simd",
 "opaque-debug 0.2.3",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug 0.3.0",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sha3-asm"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bac61da6b35ad76b195eb4771210f947734321a8d81d7738e1580d953bc7a15e"
dependencies = [
 "cc",
 "cfg-if",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook-registry"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8229b473baa5980ac72ef434c4415e70c4b5e71b423043adb4ba059f89c99a1"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "simba"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "061507c94fc6ab4ba1c9a0305018408e312e17c041eb63bef8aa726fa33aceae"
dependencies = [
 "approx",
 "num-complex",
 "num-traits",
 "paste",
 "wide",
]

[[package]]
name = "simple-mermaid"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "620a1d43d70e142b1d46a929af51d44f383db9c7a2ec122de2cd992ccfcf3c18"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "slot-range-helper"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5109eff24021551c130973a924318728720e6c74fc98d206b4f42b7763978fdc"
dependencies = [
 "enumn",
 "parity-scale-codec",
 "paste",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "slot-range-helper"
version = "15.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e34f1146a457a5c554dedeae6c7273aa54c3b031f3e9eb0abd037b5511e2ce9"
dependencies = [
 "enumn",
 "parity-scale-codec",
 "paste",
 "sp-runtime 39.0.5",
]

[[package]]
name = "smallvec"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcf8323ef1faaee30a44a340193b1ac6814fd9b7b4e88e9d4519a3e4abe1cfd"

[[package]]
name = "smol"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e635339259e51ef85ac7aa29a1cd991b957047507288697a690e80ab97d07cad"
dependencies = [
 "async-channel",
 "async-executor",
 "async-fs",
 "async-io",
 "async-lock 3.3.0",
 "async-net",
 "async-process",
 "blocking",
 "futures-lite",
]

[[package]]
name = "smoldot"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "966e72d77a3b2171bb7461d0cb91f43670c63558c62d7cf42809cae6c8b6b818"
dependencies = [
 "arrayvec 0.7.4",
 "async-lock 3.3.0",
 "atomic-take",
 "base64 0.22.1",
 "bip39",
 "blake2-rfc",
 "bs58",
 "chacha20",
 "crossbeam-queue",
 "derive_more 0.99.17",
 "ed25519-zebra 4.0.3",
 "either",
 "event-listener 5.4.0",
 "fnv",
 "futures-lite",
 "futures-util",
 "hashbrown 0.14.5",
 "hex",
 "hmac 0.12.1",
 "itertools 0.13.0",
 "libm",
 "libsecp256k1",
 "merlin 3.0.0",
 "nom",
 "num-bigint",
 "num-rational",
 "num-traits",
 "pbkdf2 0.12.2",
 "pin-project",
 "poly1305",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "ruzstd",
 "schnorrkel 0.11.4",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sha3",
 "siphasher",
 "slab",
 "smallvec",
 "soketto",
 "twox-hash",
 "wasmi",
 "x25519-dalek",
 "zeroize",
]

[[package]]
name = "smoldot-light"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a33b06891f687909632ce6a4e3fd7677b24df930365af3d0bcb078310129f3f"
dependencies = [
 "async-channel",
 "async-lock 3.3.0",
 "base64 0.22.1",
 "blake2-rfc",
 "bs58",
 "derive_more 0.99.17",
 "either",
 "event-listener 5.4.0",
 "fnv",
 "futures-channel",
 "futures-lite",
 "futures-util",
 "hashbrown 0.14.5",
 "hex",
 "itertools 0.13.0",
 "log",
 "lru 0.12.2",
 "parking_lot",
 "pin-project",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "serde",
 "serde_json",
 "siphasher",
 "slab",
 "smol",
 "smoldot",
 "zeroize",
]

[[package]]
name = "snowbridge-amcl"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "460a9ed63cdf03c1b9847e8a12a5f5ba19c4efd5869e4a737e05be25d7c427e5"
dependencies = [
 "parity-scale-codec",
 "scale-info",
]

[[package]]
name = "snowbridge-beacon-primitives"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c35d4d8b5be969d0f824c6aa8380289edf0bec2c45ccef9f758a4d7a7dca96ea"
dependencies = [
 "byte-slice-cast",
 "frame-support 30.0.0",
 "frame-system 30.0.0",
 "hex",
 "parity-scale-codec",
 "rlp",
 "scale-info",
 "serde",
 "snowbridge-ethereum 0.3.0",
 "snowbridge-milagro-bls",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "ssz_rs",
 "ssz_rs_derive",
 "static_assertions",
]

[[package]]
name = "snowbridge-beacon-primitives"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10bd720997e558beb556d354238fa90781deb38241cf31c1b6368738ef21c279"
dependencies = [
 "byte-slice-cast",
 "frame-support 38.2.0",
 "hex",
 "parity-scale-codec",
 "rlp",
 "scale-info",
 "serde",
 "snowbridge-ethereum 0.9.0",
 "snowbridge-milagro-bls",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "ssz_rs",
 "ssz_rs_derive",
]

[[package]]
name = "snowbridge-core"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4ff7e3876b41c41fd76357c60986e78b4fb60c282957b54f0b159f76ef4ea70"
dependencies = [
 "ethabi-decode",
 "frame-support 30.0.0",
 "frame-system 30.0.0",
 "hex-literal",
 "parity-scale-codec",
 "polkadot-parachain-primitives 8.0.0",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.2.0",
 "sp-arithmetic 25.0.0",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "staging-xcm 9.0.0",
 "staging-xcm-builder 9.0.0",
]

[[package]]
name = "snowbridge-core"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6be61e4db95d1e253a1d5e722953b2d2f6605e5f9761f0a919e5d3fbdbff9da9"
dependencies = [
 "ethabi-decode",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "hex-literal",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.10.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
]

[[package]]
name = "snowbridge-ethereum"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a4000b70f42c9adc0247de3893aee803cbf37e9e5f13a4c18a28a86872f648a"
dependencies = [
 "ethabi-decode",
 "ethbloom",
 "ethereum-types",
 "hex-literal",
 "parity-bytes",
 "parity-scale-codec",
 "rlp",
 "rustc-hex",
 "scale-info",
 "serde",
 "serde-big-array",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-ethereum"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc3d6d549c57df27cf89ec852f932fa4008eea877a6911a87e03e8002104eabd"
dependencies = [
 "ethabi-decode",
 "ethbloom",
 "ethereum-types",
 "hex-literal",
 "parity-bytes",
 "parity-scale-codec",
 "rlp",
 "scale-info",
 "serde",
 "serde-big-array",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-milagro-bls"
version = "1.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "026aa8638f690a53e3f7676024b9e913b1cab0111d1b7b92669d40a188f9d7e6"
dependencies = [
 "hex",
 "lazy_static",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "snowbridge-amcl",
 "zeroize",
]

[[package]]
name = "snowbridge-outbound-queue-merkle-tree"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74c6a9b65fa61711b704f0c6afb3663c6288288e8822ddae5cc1146fe3ad9ce8"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "snowbridge-outbound-queue-runtime-api"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38d27b8d9cb8022637a5ce4f52692520fa75874f393e04ef5cd75bd8795087f6"
dependencies = [
 "frame-support 38.2.0",
 "parity-scale-codec",
 "snowbridge-core 0.10.0",
 "snowbridge-outbound-queue-merkle-tree",
 "sp-api 34.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-pallet-ethereum-client"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d53d32d8470c643f9f8c1f508e1e34263f76297e4c9150e10e8f2e0b63992e1"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-timestamp 37.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-core 0.10.0",
 "snowbridge-ethereum 0.9.0",
 "snowbridge-pallet-ethereum-client-fixtures",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "static_assertions",
]

[[package]]
name = "snowbridge-pallet-ethereum-client-fixtures"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3984b98465af1d862d4e87ba783e1731f2a3f851b148d6cb98d526cebd351185"
dependencies = [
 "hex-literal",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-core 0.10.0",
 "sp-core 34.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-pallet-inbound-queue"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2e6a9d00e60e3744e6b6f0c21fea6694b9c6401ac40e41340a96e561dcf1935"
dependencies = [
 "alloy-primitives 0.4.2",
 "alloy-sol-types",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "pallet-balances 39.0.1",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-core 0.10.0",
 "snowbridge-pallet-inbound-queue-fixtures",
 "snowbridge-router-primitives 0.16.1",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "snowbridge-pallet-inbound-queue-fixtures"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f251e579b3d3d93cf833c8e503122808742dee33e7ea53b0f292a76c024d66"
dependencies = [
 "hex-literal",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-core 0.10.0",
 "sp-core 34.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-pallet-outbound-queue"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7d49478041b6512c710d0d4655675d146fe00a8e0c1624e5d8a1d6c161d490f"
dependencies = [
 "bridge-hub-common",
 "ethabi-decode",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-core 0.10.0",
 "snowbridge-outbound-queue-merkle-tree",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
]

[[package]]
name = "snowbridge-pallet-system"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "674db59b3c8013382e5c07243ad9439b64d81d2e8b3c4f08d752b55aa5de697e"
dependencies = [
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "log",
 "parity-scale-codec",
 "scale-info",
 "snowbridge-core 0.10.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "snowbridge-preimage"
version = "0.1.0"
dependencies = [
 "alloy-primitives 0.6.3",
 "asset-hub-kusama-runtime",
 "asset-hub-paseo-runtime",
 "asset-hub-polkadot-runtime",
 "asset-hub-westend-runtime",
 "bridge-hub-kusama-runtime",
 "bridge-hub-paseo-runtime",
 "bridge-hub-polkadot-runtime",
 "bridge-hub-westend-runtime",
 "clap",
 "futures",
 "hex",
 "hex-literal",
 "kusama-runtime",
 "parity-scale-codec",
 "paseo-runtime",
 "polkadot-runtime",
 "polkadot-runtime-constants",
 "scale-info",
 "serde",
 "serde_json",
 "snowbridge-beacon-primitives 0.2.0",
 "snowbridge-preimage-chopsticks",
 "snowbridge-router-primitives 0.2.0",
 "sp-arithmetic 24.0.0",
 "sp-crypto-hashing",
 "subxt",
 "tokio",
 "westend-runtime",
]

[[package]]
name = "snowbridge-preimage-chopsticks"
version = "0.1.0"
dependencies = [
 "handlebars",
 "hex",
 "parity-scale-codec",
 "serde",
 "serde_json",
 "sp-crypto-hashing",
]

[[package]]
name = "snowbridge-preimage-kusama"
version = "0.1.0"
dependencies = [
 "alloy-primitives 0.6.3",
 "asset-hub-kusama-runtime",
 "bridge-hub-kusama-runtime",
 "clap",
 "frame-support 38.2.0",
 "futures",
 "hex",
 "hex-literal",
 "kusama-runtime",
 "parity-scale-codec",
 "polkadot-runtime-constants",
 "scale-info",
 "serde",
 "snowbridge-beacon-primitives 0.10.0",
 "snowbridge-preimage-chopsticks",
 "snowbridge-router-primitives 0.16.1",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-crypto-hashing",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
 "subxt",
 "tokio",
]

[[package]]
name = "snowbridge-router-primitives"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a67b9e0941442818f6ba5e34006dcc6f7f1cfe1f607ca33bbd6c7cd4f6819c14"
dependencies = [
 "ethabi-decode",
 "frame-support 30.0.0",
 "frame-system 30.0.0",
 "hex-literal",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "snowbridge-core 0.2.1",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "staging-xcm 9.0.0",
 "staging-xcm-builder 9.0.0",
 "staging-xcm-executor 9.0.1",
]

[[package]]
name = "snowbridge-router-primitives"
version = "0.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aefe74eafeac92e1d9e46b7bb76ec297f6182b4a023f7e7eb7eb8be193f93bef"
dependencies = [
 "frame-support 38.2.0",
 "hex-literal",
 "log",
 "parity-scale-codec",
 "scale-info",
 "snowbridge-core 0.10.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "snowbridge-runtime-common"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6093f0e73d6cfdd2eea8712155d1d75b5063fc9b1d854d2665b097b4bb29570d"
dependencies = [
 "frame-support 38.2.0",
 "log",
 "parity-scale-codec",
 "snowbridge-core 0.10.0",
 "sp-arithmetic 26.0.0",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "snowbridge-runtime-test-common"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "893480d6cde2489051c65efb5d27fa87efe047b3b61216d8e27bb2f0509b7faf"
dependencies = [
 "cumulus-pallet-parachain-system",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "pallet-balances 39.0.1",
 "pallet-collator-selection",
 "pallet-message-queue 41.0.2",
 "pallet-session 38.0.0",
 "pallet-timestamp 37.0.0",
 "pallet-utility",
 "pallet-xcm",
 "parachains-runtimes-test-utils",
 "parity-scale-codec",
 "snowbridge-core 0.10.0",
 "snowbridge-pallet-ethereum-client",
 "snowbridge-pallet-ethereum-client-fixtures",
 "snowbridge-pallet-outbound-queue",
 "snowbridge-pallet-system",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-keyring",
 "sp-runtime 39.0.5",
 "staging-parachain-info",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "snowbridge-system-runtime-api"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68b8b83b3db781c49844312a23965073e4d93341739a35eafe526c53b578d3b7"
dependencies = [
 "parity-scale-codec",
 "snowbridge-core 0.10.0",
 "sp-api 34.0.0",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
]

[[package]]
name = "socket2"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05ffd9c0a93b7543e062e759284fcf5f5e3b098501104bfbdde4d404db792871"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "soketto"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e859df029d160cb88608f5d7df7fb4753fd20fdfb4de5644f3d8b8440841721"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "futures",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
]

[[package]]
name = "sp-api"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddc5213210472ba2becdc094fbb9d30c4455753b1a608962797e1e971c3e5ec4"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api-proc-macro 11.0.0",
 "sp-core 24.0.0",
 "sp-externalities 0.22.0",
 "sp-metadata-ir 0.3.0",
 "sp-runtime 27.0.0",
 "sp-state-machine 0.31.0",
 "sp-std 11.0.0",
 "sp-trie 25.0.0",
 "sp-version 25.0.0",
 "thiserror",
]

[[package]]
name = "sp-api"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "298331cb47a948244f6fb4921b5cbeece267d72139fb90760993b6ec37b2212c"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api-proc-macro 16.0.0",
 "sp-core 30.0.0",
 "sp-externalities 0.27.0",
 "sp-metadata-ir 0.6.0",
 "sp-runtime 33.0.0",
 "sp-runtime-interface 26.0.0",
 "sp-state-machine 0.37.0",
 "sp-std 14.0.0",
 "sp-trie 31.0.0",
 "sp-version 31.0.0",
 "thiserror",
]

[[package]]
name = "sp-api"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbce492e0482134128b7729ea36f5ef1a9f9b4de2d48ff8dde7b5e464e28ce75"
dependencies = [
 "docify",
 "hash-db",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-api-proc-macro 20.0.0",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
 "sp-metadata-ir 0.7.0",
 "sp-runtime 39.0.5",
 "sp-runtime-interface 28.0.0",
 "sp-state-machine 0.43.0",
 "sp-trie 37.0.0",
 "sp-version 37.0.0",
 "thiserror",
]

[[package]]
name = "sp-api-proc-macro"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20e7f093302d30b9d35436db024376459bdc9da7530abcacf5d87c32a67d94fd"
dependencies = [
 "Inflector",
 "blake2",
 "expander",
 "proc-macro-crate 1.3.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-api-proc-macro"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18cfbb3ae0216e842dfb805ea8e896e85b07a7c34d432a6c7b7d770924431ed2"
dependencies = [
 "Inflector",
 "blake2",
 "expander",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-api-proc-macro"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9aadf9e97e694f0e343978aa632938c5de309cbcc8afed4136cb71596737278"
dependencies = [
 "Inflector",
 "blake2",
 "expander",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-application-crypto"
version = "26.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b74454c936a45ac55c8de95b9fd8b5e38f8b43d97df8f4274dd6777b20d95569"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-application-crypto"
version = "32.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b4b7b12922cb90cf8dff0cab14087ba0ca25c1f04ba060c7294ce42c78d89ab"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-application-crypto"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d8133012faa5f75b2f0b1619d9f720c1424ac477152c143e5f7dbde2fe1a958"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
]

[[package]]
name = "sp-arithmetic"
version = "19.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e41f710a77e9debd1c9b80f862709dce648e50f0904cde4117488e7d11d4796d"
dependencies = [
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-std 11.0.0",
 "static_assertions",
]

[[package]]
name = "sp-arithmetic"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afa823ca5adc490d47dccb41d69ad482bc57a317bd341de275868378f48f131c"
dependencies = [
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-std 14.0.0",
 "static_assertions",
]

[[package]]
name = "sp-arithmetic"
version = "25.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "910c07fa263b20bf7271fdd4adcb5d3217dfdac14270592e0780223542e7e114"
dependencies = [
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-std 14.0.0",
 "static_assertions",
]

[[package]]
name = "sp-arithmetic"
version = "26.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46d0d0a4c591c421d3231ddd5e27d828618c24456d51445d21a1f79fcee97c23"
dependencies = [
 "docify",
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-std 14.0.0",
 "static_assertions",
]

[[package]]
name = "sp-authority-discovery"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3259fe9bf2e48eba37067f464a3db79ef20e25f1297cbad310c11738757c5c6a"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api 22.0.0",
 "sp-application-crypto 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-authority-discovery"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "519c33af0e25ba2dd2eb3790dc404d634b6e4ce0801bcc8fa3574e07c365e734"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-block-builder"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74738809461e3d4bd707b5b94e0e0c064a623a74a6a8fe5c98514417a02858dd"
dependencies = [
 "sp-api 34.0.0",
 "sp-inherents 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-consensus-aura"
version = "0.40.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a8faaa05bbcb9c41f0cc535c4c1315abf6df472b53eae018678d1b4d811ac47"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-consensus-slots 0.40.1",
 "sp-inherents 34.0.0",
 "sp-runtime 39.0.5",
 "sp-timestamp 34.0.0",
]

[[package]]
name = "sp-consensus-babe"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "572374a1260687fa18481ccac58c4a64611df379fb1aa65389ce96c6661b3b05"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api 22.0.0",
 "sp-application-crypto 26.0.0",
 "sp-consensus-slots 0.28.0",
 "sp-core 24.0.0",
 "sp-inherents 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-timestamp 22.0.0",
]

[[package]]
name = "sp-consensus-babe"
version = "0.40.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "***********************************************************47f86"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-consensus-slots 0.40.1",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-runtime 39.0.5",
 "sp-timestamp 34.0.0",
]

[[package]]
name = "sp-consensus-beefy"
version = "22.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1d97e8cd75d85d15cda6f1923cf3834e848f80d5a6de1cf4edbbc5f0ad607eb"
dependencies = [
 "lazy_static",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-core 34.0.0",
 "sp-crypto-hashing",
 "sp-io 38.0.0",
 "sp-keystore 0.40.0",
 "sp-mmr-primitives",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "strum 0.26.3",
]

[[package]]
name = "sp-consensus-grandpa"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "587b791efe6c5f18e09dbbaf1ece0ee7b5fe51602c233e7151a3676b0de0260b"
dependencies = [
 "finality-grandpa",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-core 34.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-consensus-pow"
version = "0.40.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fa6b7d199a1c16cea1b74ee7cee174bf08f2120ab66a87bee7b12353100b47c"
dependencies = [
 "parity-scale-codec",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-consensus-slots"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ebe1c46246a76af1105639c7434c1383d376fd45a8548fc18ed66dbf86f803c"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-std 11.0.0",
 "sp-timestamp 22.0.0",
]

[[package]]
name = "sp-consensus-slots"
version = "0.40.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbafb7ed44f51c22fa277fb39b33dc601fa426133a8e2b53f3f46b10f07fba43"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-timestamp 34.0.0",
]

[[package]]
name = "sp-core"
version = "24.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7921d278ed2aebbb21a644c96e09663dc49a6139d1e2e063c059dc9f866e149b"
dependencies = [
 "array-bytes",
 "bitflags 1.3.2",
 "blake2",
 "bounded-collections 0.1.9",
 "bs58",
 "dyn-clonable",
 "ed25519-zebra 3.1.0",
 "futures",
 "hash-db",
 "hash256-std-hasher",
 "impl-serde 0.4.0",
 "lazy_static",
 "libsecp256k1",
 "log",
 "merlin 2.0.1",
 "parity-scale-codec",
 "parking_lot",
 "paste",
 "primitive-types 0.12.2",
 "rand 0.8.5",
 "regex",
 "scale-info",
 "schnorrkel 0.9.1",
 "secp256k1 0.24.3",
 "secrecy",
 "serde",
 "sp-core-hashing 12.0.0",
 "sp-debug-derive 11.0.0",
 "sp-externalities 0.22.0",
 "sp-runtime-interface 20.0.0",
 "sp-std 11.0.0",
 "sp-storage 16.0.0",
 "ss58-registry",
 "substrate-bip39 0.4.5",
 "thiserror",
 "tiny-bip39",
 "tracing",
 "zeroize",
]

[[package]]
name = "sp-core"
version = "30.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "586e0d5185e4545f465fc9a04fb9c4572d3e294137312496db2b67b0bb579e1f"
dependencies = [
 "array-bytes",
 "bip39",
 "bitflags 1.3.2",
 "blake2",
 "bounded-collections 0.2.4",
 "bs58",
 "dyn-clonable",
 "ed25519-zebra 3.1.0",
 "futures",
 "hash-db",
 "hash256-std-hasher",
 "impl-serde 0.4.0",
 "itertools 0.10.5",
 "libsecp256k1",
 "log",
 "merlin 3.0.0",
 "parity-scale-codec",
 "parking_lot",
 "paste",
 "primitive-types 0.12.2",
 "rand 0.8.5",
 "scale-info",
 "schnorrkel 0.11.4",
 "secp256k1 0.28.2",
 "secrecy",
 "serde",
 "sp-crypto-hashing",
 "sp-debug-derive 14.0.0",
 "sp-externalities 0.27.0",
 "sp-runtime-interface 26.0.0",
 "sp-std 14.0.0",
 "sp-storage 20.0.0",
 "ss58-registry",
 "substrate-bip39 0.4.5",
 "thiserror",
 "tracing",
 "w3f-bls",
 "zeroize",
]

[[package]]
name = "sp-core"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c961a5e33fb2962fa775c044ceba43df9c6f917e2c35d63bfe23738468fa76a7"
dependencies = [
 "array-bytes",
 "bitflags 1.3.2",
 "blake2",
 "bounded-collections 0.2.4",
 "bs58",
 "dyn-clonable",
 "ed25519-zebra 4.0.3",
 "futures",
 "hash-db",
 "hash256-std-hasher",
 "impl-serde 0.4.0",
 "itertools 0.11.0",
 "k256",
 "libsecp256k1",
 "log",
 "merlin 3.0.0",
 "parity-bip39",
 "parity-scale-codec",
 "parking_lot",
 "paste",
 "primitive-types 0.12.2",
 "rand 0.8.5",
 "scale-info",
 "schnorrkel 0.11.4",
 "secp256k1 0.28.2",
 "secrecy",
 "serde",
 "sp-crypto-hashing",
 "sp-debug-derive 14.0.0",
 "sp-externalities 0.29.0",
 "sp-runtime-interface 28.0.0",
 "sp-std 14.0.0",
 "sp-storage 21.0.0",
 "ss58-registry",
 "substrate-bip39 0.6.0",
 "thiserror",
 "tracing",
 "w3f-bls",
 "zeroize",
]

[[package]]
name = "sp-core-hashing"
version = "12.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7cb5c31aa385d6997a5b73fdc9837c1c0145559205198555c3000739a474767"
dependencies = [
 "blake2b_simd",
 "byteorder",
 "digest 0.10.7",
 "sha2 0.10.8",
 "sha3",
 "twox-hash",
]

[[package]]
name = "sp-core-hashing"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f812cb2dff962eb378c507612a50f1c59f52d92eb97b710f35be3c2346a3cd7"
dependencies = [
 "sp-crypto-hashing",
]

[[package]]
name = "sp-core-hashing-proc-macro"
version = "12.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a4327a220777a8d492ed3d0bcd4c769cbb030301e7d4a2d9e09513d690c313b"
dependencies = [
 "quote",
 "sp-core-hashing 12.0.0",
 "syn 2.0.98",
]

[[package]]
name = "sp-crypto-ec-utils"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acb24f8a607a48a87f0ee4c090fc5d577eee49ff39ced6a3c491e06eca03c37"
dependencies = [
 "ark-bls12-377",
 "ark-bls12-377-ext",
 "ark-bls12-381",
 "ark-bls12-381-ext",
 "ark-bw6-761",
 "ark-bw6-761-ext",
 "ark-ec",
 "ark-ed-on-bls12-377",
 "ark-ed-on-bls12-377-ext",
 "ark-ed-on-bls12-381-bandersnatch",
 "ark-ed-on-bls12-381-bandersnatch-ext",
 "ark-scale",
 "sp-runtime-interface 28.0.0",
]

[[package]]
name = "sp-crypto-hashing"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc9927a7f81334ed5b8a98a4a978c81324d12bd9713ec76b5c68fd410174c5eb"
dependencies = [
 "blake2b_simd",
 "byteorder",
 "digest 0.10.7",
 "sha2 0.10.8",
 "sha3",
 "twox-hash",
]

[[package]]
name = "sp-crypto-hashing-proc-macro"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b85d0f1f1e44bd8617eb2a48203ee854981229e3e79e6f468c7175d5fd37489b"
dependencies = [
 "quote",
 "sp-crypto-hashing",
 "syn 2.0.98",
]

[[package]]
name = "sp-debug-derive"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16f7d375610590566e11882bf5b5a4b8d0666a96ba86808b2650bbbd9be50bf8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-debug-derive"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d09fa0a5f7299fb81ee25ae3853d26200f7a348148aed6de76be905c007dbe"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-externalities"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ede074871514ca7c5d2eca9563515d858c6220b47ae815714ed4393a4e99db4a"
dependencies = [
 "environmental",
 "parity-scale-codec",
 "sp-std 11.0.0",
 "sp-storage 16.0.0",
]

[[package]]
name = "sp-externalities"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d6a4572eadd4a63cff92509a210bf425501a0c5e76574b30a366ac77653787"
dependencies = [
 "environmental",
 "parity-scale-codec",
 "sp-std 14.0.0",
 "sp-storage 20.0.0",
]

[[package]]
name = "sp-externalities"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a904407d61cb94228c71b55a9d3708e9d6558991f9e83bd42bd91df37a159d30"
dependencies = [
 "environmental",
 "parity-scale-codec",
 "sp-storage 21.0.0",
]

[[package]]
name = "sp-genesis-builder"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10b9f0251a09b578393f3297abe54a29abdb7e93c17e89a88dc1cabb8e2d5a2d"
dependencies = [
 "serde_json",
 "sp-api 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-genesis-builder"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a862db099e8a799417b63ea79c90079811cdf68fcf3013d81cdceeddcec8f142"
dependencies = [
 "serde_json",
 "sp-api 28.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-genesis-builder"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a646ed222fd86d5680faa4a8967980eb32f644cae6c8523e1c689a6deda3e8"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde_json",
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-inherents"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439882da80e9bcfd1ba53df7ec5070d4d7f2a9a93f988aa3598f99ee5bfc76eb"
dependencies = [
 "async-trait",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "thiserror",
]

[[package]]
name = "sp-inherents"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42eb3c88572c7c80e7ecb6365601a490350b09d11000fcc7839efd304e172177"
dependencies = [
 "async-trait",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "thiserror",
]

[[package]]
name = "sp-inherents"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afffbddc380d99a90c459ba1554bbbc01d62e892de9f1485af6940b89c4c0d57"
dependencies = [
 "async-trait",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
 "thiserror",
]

[[package]]
name = "sp-io"
version = "26.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88fb6e281de5054565f07a9f79504d21133e115db549993c99f1b21236c677a5"
dependencies = [
 "bytes",
 "ed25519-dalek",
 "libsecp256k1",
 "log",
 "parity-scale-codec",
 "rustversion",
 "secp256k1 0.24.3",
 "sp-core 24.0.0",
 "sp-externalities 0.22.0",
 "sp-keystore 0.30.0",
 "sp-runtime-interface 20.0.0",
 "sp-state-machine 0.31.0",
 "sp-std 11.0.0",
 "sp-tracing 13.0.0",
 "sp-trie 25.0.0",
 "tracing",
 "tracing-core",
]

[[package]]
name = "sp-io"
version = "32.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ca29e042628cb94cbcaefa935e624a9b48f9230dbce6324908e9b4f768317ef"
dependencies = [
 "bytes",
 "ed25519-dalek",
 "libsecp256k1",
 "log",
 "parity-scale-codec",
 "rustversion",
 "secp256k1 0.28.2",
 "sp-core 30.0.0",
 "sp-crypto-hashing",
 "sp-externalities 0.27.0",
 "sp-keystore 0.36.0",
 "sp-runtime-interface 26.0.0",
 "sp-state-machine 0.37.0",
 "sp-std 14.0.0",
 "sp-tracing 16.0.0",
 "sp-trie 31.0.0",
 "tracing",
 "tracing-core",
]

[[package]]
name = "sp-io"
version = "38.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ef7eb561bb4839cc8424ce58c5ea236cbcca83f26fcc0426d8decfe8aa97d4"
dependencies = [
 "bytes",
 "docify",
 "ed25519-dalek",
 "libsecp256k1",
 "log",
 "parity-scale-codec",
 "polkavm-derive 0.9.1",
 "rustversion",
 "secp256k1 0.28.2",
 "sp-core 34.0.0",
 "sp-crypto-hashing",
 "sp-externalities 0.29.0",
 "sp-keystore 0.40.0",
 "sp-runtime-interface 28.0.0",
 "sp-state-machine 0.43.0",
 "sp-tracing 17.0.1",
 "sp-trie 37.0.0",
 "tracing",
 "tracing-core",
]

[[package]]
name = "sp-keyring"
version = "39.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c0e20624277f578b27f44ecfbe2ebc2e908488511ee2c900c5281599f700ab3"
dependencies = [
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "strum 0.26.3",
]

[[package]]
name = "sp-keystore"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9f19e773319d96223ce8dba960267e6cb977907537a8f738746ceb86592413"
dependencies = [
 "parity-scale-codec",
 "parking_lot",
 "sp-core 24.0.0",
 "sp-externalities 0.22.0",
 "thiserror",
]

[[package]]
name = "sp-keystore"
version = "0.36.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd4bf9e5fa486416c92c2bb497b7ce2c43eac80cbdc407ffe2d34b365694ac29"
dependencies = [
 "parity-scale-codec",
 "parking_lot",
 "sp-core 30.0.0",
 "sp-externalities 0.27.0",
]

[[package]]
name = "sp-keystore"
version = "0.40.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0248b4d784cb4a01472276928977121fa39d977a5bb24793b6b15e64b046df42"
dependencies = [
 "parity-scale-codec",
 "parking_lot",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
]

[[package]]
name = "sp-maybe-compressed-blob"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0c768c11afbe698a090386876911da4236af199cd38a5866748df4d8628aeff"
dependencies = [
 "thiserror",
 "zstd 0.12.4",
]

[[package]]
name = "sp-metadata-ir"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb0dec8af38c68358600da59cf14424e1230fe9ae1d4b4f64a098288145c0775"
dependencies = [
 "frame-metadata 16.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-metadata-ir"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa0b5e87e56c1bb26d9524d48dd127121d630f895bd5914a34f0b017489f7c1d"
dependencies = [
 "frame-metadata 16.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-metadata-ir"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a616fa51350b35326682a472ee8e6ba742fdacb18babac38ecd46b3e05ead869"
dependencies = [
 "frame-metadata 16.0.0",
 "parity-scale-codec",
 "scale-info",
]

[[package]]
name = "sp-mixnet"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b0b017dd54823b6e62f9f7171a1df350972e5c6d0bf17e0c2f78680b5c31942"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
]

[[package]]
name = "sp-mmr-primitives"
version = "34.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a12dd76e368f1e48144a84b4735218b712f84b3f976970e2f25a29b30440e10"
dependencies = [
 "log",
 "parity-scale-codec",
 "polkadot-ckb-merkle-mountain-range",
 "scale-info",
 "serde",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-debug-derive 14.0.0",
 "sp-runtime 39.0.5",
 "thiserror",
]

[[package]]
name = "sp-npos-elections"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec60b253bf9097e8ffe103563c515ef80550556ab3b2ec5513ed17a4ad7de520"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-npos-elections"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af922f112c7c1ed199eabe14f12a82ceb75e1adf0804870eccfbcf3399492847"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-offchain"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d9de237d72ecffd07f90826eef18360208b16d8de939d54e61591fac0fcbf99"
dependencies = [
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-panic-handler"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd099ba2d6c1bfe5d0c79aa56e440fa3c9257eadfc0c782c09cdc2122b1e60ed"
dependencies = [
 "backtrace",
 "lazy_static",
 "regex",
]

[[package]]
name = "sp-panic-handler"
version = "13.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81478b3740b357fa0ea10fcdc1ee02ebae7734e50f80342c4743476d9f78eeea"
dependencies = [
 "backtrace",
 "regex",
]

[[package]]
name = "sp-runtime"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46c0641e1a9d340960b562bcceea1457680fd0e109fc1040f8f5364fd7bc2506"
dependencies = [
 "either",
 "hash256-std-hasher",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "paste",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "sp-application-crypto 26.0.0",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-std 11.0.0",
 "sp-weights 23.0.0",
]

[[package]]
name = "sp-runtime"
version = "33.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b28fcf8f53d917e420e783dd27d06fd276f55160301c5bc977cc5898c4130f6f"
dependencies = [
 "docify",
 "either",
 "hash256-std-hasher",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "paste",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "simple-mermaid",
 "sp-application-crypto 32.0.0",
 "sp-arithmetic 25.0.0",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-std 14.0.0",
 "sp-weights 29.0.0",
]

[[package]]
name = "sp-runtime"
version = "39.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1e00503b83cf48fffe48746b91b9b832d6785d4e2eeb0941558371eac6baac6"
dependencies = [
 "docify",
 "either",
 "hash256-std-hasher",
 "impl-trait-for-tuples",
 "log",
 "num-traits",
 "parity-scale-codec",
 "paste",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "simple-mermaid",
 "sp-application-crypto 38.0.0",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-std 14.0.0",
 "sp-weights 31.0.0",
 "tracing",
]

[[package]]
name = "sp-runtime-interface"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17a4030ad93f05c93f2cc294c74bc5fea227f90fb3d1426d4a6f165e017fb7ea"
dependencies = [
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "primitive-types 0.12.2",
 "sp-externalities 0.22.0",
 "sp-runtime-interface-proc-macro 14.0.0",
 "sp-std 11.0.0",
 "sp-storage 16.0.0",
 "sp-tracing 13.0.0",
 "sp-wasm-interface 17.0.0",
 "static_assertions",
]

[[package]]
name = "sp-runtime-interface"
version = "26.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48a675ea4858333d4d755899ed5ed780174aa34fec15953428d516af5452295"
dependencies = [
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "polkavm-derive 0.8.0",
 "primitive-types 0.12.2",
 "sp-externalities 0.27.0",
 "sp-runtime-interface-proc-macro 18.0.0",
 "sp-std 14.0.0",
 "sp-storage 20.0.0",
 "sp-tracing 16.0.0",
 "sp-wasm-interface 20.0.0",
 "static_assertions",
]

[[package]]
name = "sp-runtime-interface"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "985eb981f40c689c6a0012c937b68ed58dabb4341d06f2dfe4dfd5ed72fa4017"
dependencies = [
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "polkavm-derive 0.9.1",
 "primitive-types 0.12.2",
 "sp-externalities 0.29.0",
 "sp-runtime-interface-proc-macro 18.0.0",
 "sp-std 14.0.0",
 "sp-storage 21.0.0",
 "sp-tracing 17.0.1",
 "sp-wasm-interface 21.0.1",
 "static_assertions",
]

[[package]]
name = "sp-runtime-interface-proc-macro"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b232943ee7ca83a6d56face33b8af12e9fb470a15a53835f4e12a6e452a41c1c"
dependencies = [
 "Inflector",
 "proc-macro-crate 1.3.1",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-runtime-interface-proc-macro"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0195f32c628fee3ce1dfbbf2e7e52a30ea85f3589da9fe62a8b816d70fc06294"
dependencies = [
 "Inflector",
 "expander",
 "proc-macro-crate 3.1.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-session"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfd062688577cc54493ba6f58383bfed89c66d5ef7b7c3747293b0da06c7f795"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api 22.0.0",
 "sp-core 24.0.0",
 "sp-keystore 0.30.0",
 "sp-runtime 27.0.0",
 "sp-staking 22.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-session"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00a3a307fedc423fb8cd2a7726a3bbb99014f1b4b52f26153993e2aae3338fe6"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-core 34.0.0",
 "sp-keystore 0.40.0",
 "sp-runtime 39.0.5",
 "sp-staking 36.0.0",
]

[[package]]
name = "sp-staking"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d3b2a4a7aa67a9adb2a8f49ed516f6694b5fa70792ab9b0125934b1c8cdc2e3"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 24.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-staking"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48b92f4f66b40cbf7cf00d7808d8eec16e25cb420a29ec4060a74c0e9f7c2938"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 30.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-staking"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "143a764cacbab58347d8b2fd4c8909031fb0888d7b02a0ec9fa44f81f780d732"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-staking"
version = "36.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a73eedb4b85f4cd420d31764827546aa22f82ce1646d0fd258993d051de7a90"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-state-machine"
version = "0.31.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bf4c76bea1a9e4a2e79afe70f42f1d368a8a45308e58f19bfd755c5ddb2b4a3"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "parking_lot",
 "rand 0.8.5",
 "smallvec",
 "sp-core 24.0.0",
 "sp-externalities 0.22.0",
 "sp-panic-handler 11.0.0",
 "sp-std 11.0.0",
 "sp-trie 25.0.0",
 "thiserror",
 "tracing",
 "trie-db 0.28.0",
]

[[package]]
name = "sp-state-machine"
version = "0.37.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23ae47765916d342b53d07be012a71efc4c1377d875ade31340cc4fb784b9921"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "parking_lot",
 "rand 0.8.5",
 "smallvec",
 "sp-core 30.0.0",
 "sp-externalities 0.27.0",
 "sp-panic-handler 13.0.1",
 "sp-std 14.0.0",
 "sp-trie 31.0.0",
 "thiserror",
 "tracing",
 "trie-db 0.28.0",
]

[[package]]
name = "sp-state-machine"
version = "0.43.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "930104d6ae882626e8880d9b1578da9300655d337a3ffb45e130c608b6c89660"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec",
 "parking_lot",
 "rand 0.8.5",
 "smallvec",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
 "sp-panic-handler 13.0.1",
 "sp-trie 37.0.0",
 "thiserror",
 "tracing",
 "trie-db 0.29.1",
]

[[package]]
name = "sp-statement-store"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c219bc34ef4d1f9835f3ed881f965643c32034fcc030eb33b759dadbc802c1c2"
dependencies = [
 "aes-gcm",
 "curve25519-dalek 4.1.3",
 "ed25519-dalek",
 "hkdf",
 "parity-scale-codec",
 "rand 0.8.5",
 "scale-info",
 "sha2 0.10.8",
 "sp-api 34.0.0",
 "sp-application-crypto 38.0.0",
 "sp-core 34.0.0",
 "sp-crypto-hashing",
 "sp-externalities 0.29.0",
 "sp-runtime 39.0.5",
 "sp-runtime-interface 28.0.0",
 "thiserror",
 "x25519-dalek",
]

[[package]]
name = "sp-std"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c91d32e165d08a14098ce5ec923eaec59d1d0583758a18a770beec1b780b0d0"

[[package]]
name = "sp-std"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12f8ee986414b0a9ad741776762f4083cd3a5128449b982a3919c4df36874834"

[[package]]
name = "sp-storage"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac9660ecd48314443e73ad0f44d58b76426666a1343d72f6f65664e174da9244"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "ref-cast",
 "serde",
 "sp-debug-derive 11.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-storage"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dba5791cb3978e95daf99dad919ecb3ec35565604e88cd38d805d9d4981e8bd"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "ref-cast",
 "serde",
 "sp-debug-derive 14.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-storage"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99c82989b3a4979a7e1ad848aad9f5d0b4388f1f454cc131766526601ab9e8f8"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "ref-cast",
 "serde",
 "sp-debug-derive 14.0.0",
]

[[package]]
name = "sp-timestamp"
version = "22.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b0ab4b6b2d31db93e7da68894ccb7c5a305524cea051109820b958361d162be"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "sp-inherents 22.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "thiserror",
]

[[package]]
name = "sp-timestamp"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72a1cb4df653d62ccc0dbce1db45d1c9443ec60247ee9576962d24da4c9c6f07"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "sp-inherents 34.0.0",
 "sp-runtime 39.0.5",
 "thiserror",
]

[[package]]
name = "sp-tracing"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69a61948986d2a9f8d67d60884ff0277d910df09ebe08d0e1f309da777516453"
dependencies = [
 "parity-scale-codec",
 "sp-std 11.0.0",
 "tracing",
 "tracing-core",
 "tracing-subscriber 0.2.25",
]

[[package]]
name = "sp-tracing"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0351810b9d074df71c4514c5228ed05c250607cba131c1c9d1526760ab69c05c"
dependencies = [
 "parity-scale-codec",
 "sp-std 14.0.0",
 "tracing",
 "tracing-core",
 "tracing-subscriber 0.2.25",
]

[[package]]
name = "sp-tracing"
version = "17.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf641a1d17268c8fcfdb8e0fa51a79c2d4222f4cfda5f3944dbdbc384dced8d5"
dependencies = [
 "parity-scale-codec",
 "tracing",
 "tracing-core",
 "tracing-subscriber 0.3.18",
]

[[package]]
name = "sp-transaction-pool"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc4bf251059485a7dd38fe4afeda8792983511cc47f342ff4695e2dcae6b5247"
dependencies = [
 "sp-api 34.0.0",
 "sp-runtime 39.0.5",
]

[[package]]
name = "sp-transaction-storage-proof"
version = "34.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c765c2e9817d95f13d42a9f2295c60723464669765c6e5acbacebd2f54932f67"
dependencies = [
 "async-trait",
 "parity-scale-codec",
 "scale-info",
 "sp-core 34.0.0",
 "sp-inherents 34.0.0",
 "sp-runtime 39.0.5",
 "sp-trie 37.0.0",
]

[[package]]
name = "sp-trie"
version = "25.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bb2d292eb90452dcb0909fb44e74bf04395e3ffa37a66c0f1635a00600382a4"
dependencies = [
 "ahash 0.8.11",
 "hash-db",
 "hashbrown 0.13.2",
 "lazy_static",
 "memory-db",
 "nohash-hasher",
 "parity-scale-codec",
 "parking_lot",
 "scale-info",
 "schnellru",
 "sp-core 24.0.0",
 "sp-std 11.0.0",
 "thiserror",
 "tracing",
 "trie-db 0.28.0",
 "trie-root",
]

[[package]]
name = "sp-trie"
version = "31.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5791e2e310cf88abedbd5f60ff3d9c9a09d95b182b4a7510f3648a2170ace593"
dependencies = [
 "ahash 0.8.11",
 "hash-db",
 "lazy_static",
 "memory-db",
 "nohash-hasher",
 "parity-scale-codec",
 "parking_lot",
 "rand 0.8.5",
 "scale-info",
 "schnellru",
 "sp-core 30.0.0",
 "sp-externalities 0.27.0",
 "sp-std 14.0.0",
 "thiserror",
 "tracing",
 "trie-db 0.28.0",
 "trie-root",
]

[[package]]
name = "sp-trie"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6282aef9f4b6ecd95a67a45bcdb67a71f4a4155c09a53c10add4ffe823db18cd"
dependencies = [
 "ahash 0.8.11",
 "hash-db",
 "lazy_static",
 "memory-db",
 "nohash-hasher",
 "parity-scale-codec",
 "parking_lot",
 "rand 0.8.5",
 "scale-info",
 "schnellru",
 "sp-core 34.0.0",
 "sp-externalities 0.29.0",
 "thiserror",
 "tracing",
 "trie-db 0.29.1",
 "trie-root",
]

[[package]]
name = "sp-version"
version = "25.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "125da59ea46ecb23860e7d895f6f2882f596b71ffca0ae4887558aac541f4342"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "parity-wasm",
 "scale-info",
 "serde",
 "sp-core-hashing-proc-macro",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-version-proc-macro 11.0.0",
 "thiserror",
]

[[package]]
name = "sp-version"
version = "31.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "973478ac076be7cb8e0a7968ee43cd7c46fb26e323d36020a9f3bb229e033cd2"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "parity-wasm",
 "scale-info",
 "serde",
 "sp-crypto-hashing-proc-macro",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "sp-version-proc-macro 13.0.0",
 "thiserror",
]

[[package]]
name = "sp-version"
version = "37.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d521a405707b5be561367cd3d442ff67588993de24062ce3adefcf8437ee9fe1"
dependencies = [
 "impl-serde 0.4.0",
 "parity-scale-codec",
 "parity-wasm",
 "scale-info",
 "serde",
 "sp-crypto-hashing-proc-macro",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "sp-version-proc-macro 14.0.0",
 "thiserror",
]

[[package]]
name = "sp-version-proc-macro"
version = "11.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92897ffa04436cbd100c49ea1f8b637cb68e2a9fe144115f4b545b5ace2f47e2"
dependencies = [
 "parity-scale-codec",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-version-proc-macro"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9bc3fed32d6dacbbbfb28dd1fe0224affbb737cb6cbfca1d9149351c2b69a7d"
dependencies = [
 "parity-scale-codec",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-version-proc-macro"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5aee8f6730641a65fcf0c8f9b1e448af4b3bb083d08058b47528188bccc7b7a7"
dependencies = [
 "parity-scale-codec",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sp-wasm-interface"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf43bb0c8eb76dc41057ce0fb6b744b94c9aec28b31dff53a1efc4f04ef25384"
dependencies = [
 "anyhow",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "sp-std 11.0.0",
 "wasmtime",
]

[[package]]
name = "sp-wasm-interface"
version = "20.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ef97172c42eb4c6c26506f325f48463e9bc29b2034a587f1b9e48c751229bee"
dependencies = [
 "anyhow",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "sp-std 14.0.0",
 "wasmtime",
]

[[package]]
name = "sp-wasm-interface"
version = "21.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b066baa6d57951600b14ffe1243f54c47f9c23dd89c262e17ca00ae8dca58be9"
dependencies = [
 "anyhow",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "wasmtime",
]

[[package]]
name = "sp-weights"
version = "23.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e1cef0aad13ed8a8522a6e86ace16fb97ab220c16d2357e628352b528582693"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "smallvec",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-debug-derive 11.0.0",
 "sp-std 11.0.0",
]

[[package]]
name = "sp-weights"
version = "29.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab8a9c7a1b64fa7dba38622ad1de26f0b2e595727c0e42c7b109ecb8e7120688"
dependencies = [
 "bounded-collections 0.2.4",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "smallvec",
 "sp-arithmetic 25.0.0",
 "sp-debug-derive 14.0.0",
 "sp-std 14.0.0",
]

[[package]]
name = "sp-weights"
version = "31.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93cdaf72a1dad537bbb130ba4d47307ebe5170405280ed1aa31fa712718a400e"
dependencies = [
 "bounded-collections 0.2.4",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "smallvec",
 "sp-arithmetic 26.0.0",
 "sp-debug-derive 14.0.0",
]

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "ss58-registry"
version = "1.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1114ee5900b8569bbc8b1a014a942f937b752af4b44f4607430b5f86cedaac0"
dependencies = [
 "Inflector",
 "num-format",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "unicode-xid",
]

[[package]]
name = "ssz_rs"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "057291e5631f280978fa9c8009390663ca4613359fc1318e36a8c24c392f6d1f"
dependencies = [
 "bitvec",
 "num-bigint",
 "sha2 0.9.9",
 "ssz_rs_derive",
]

[[package]]
name = "ssz_rs_derive"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f07d54c4d01a1713eb363b55ba51595da15f6f1211435b71466460da022aa140"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "staging-parachain-info"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d28266dfddbfff721d70ad2f873380845b569adfab32f257cf97d9cedd894b68"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "scale-info",
 "sp-runtime 39.0.5",
]

[[package]]
name = "staging-xcm"
version = "3.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b199be791bd630ec9edc78b3896826d802f59202739a73cca5d0cd4d95b6088"
dependencies = [
 "bounded-collections 0.1.9",
 "derivative",
 "environmental",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-weights 23.0.0",
 "xcm-procedural 3.0.0",
]

[[package]]
name = "staging-xcm"
version = "9.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3028e3a4ee8493767ee66266571f5cf1fc3edc546bba650b2040c5418b318340"
dependencies = [
 "array-bytes",
 "bounded-collections 0.2.4",
 "derivative",
 "environmental",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-weights 29.0.0",
 "xcm-procedural 8.0.0",
]

[[package]]
name = "staging-xcm"
version = "14.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96bee7cd999e9cdf10f8db72342070d456e21e82a0f5962ff3b87edbd5f2b20e"
dependencies = [
 "array-bytes",
 "bounded-collections 0.2.4",
 "derivative",
 "environmental",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "serde",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "xcm-procedural 10.1.0",
]

[[package]]
name = "staging-xcm-builder"
version = "9.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ea27e235bcca331e5ba693fd224fcc16c17b53f53fca875c8dc54b733dba3c6"
dependencies = [
 "frame-support 30.0.0",
 "frame-system 30.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-transaction-payment 30.0.0",
 "parity-scale-codec",
 "polkadot-parachain-primitives 8.0.0",
 "scale-info",
 "sp-arithmetic 25.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "sp-weights 29.0.0",
 "staging-xcm 9.0.0",
 "staging-xcm-executor 9.0.1",
]

[[package]]
name = "staging-xcm-builder"
version = "17.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1693870a07e3fd8115c02b44e1223ce149b6cfa0b60f59a1c0fbc26637766a5"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "impl-trait-for-tuples",
 "log",
 "pallet-asset-conversion",
 "pallet-transaction-payment 38.0.2",
 "parity-scale-codec",
 "polkadot-parachain-primitives 14.0.0",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "staging-xcm-executor"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a50ece4a1d0651af4ccb78170e8008a44cd119ee4fdf5190d997c78e24e46a03"
dependencies = [
 "environmental",
 "frame-benchmarking 24.0.0",
 "frame-support 24.0.0",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "sp-arithmetic 19.0.0",
 "sp-core 24.0.0",
 "sp-io 26.0.0",
 "sp-runtime 27.0.0",
 "sp-std 11.0.0",
 "sp-weights 23.0.0",
 "staging-xcm 3.0.1",
]

[[package]]
name = "staging-xcm-executor"
version = "9.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe8c62fe1eee71592828a513693106ff301cdafd5ac5bd52e06d9315fd4f4f7a"
dependencies = [
 "environmental",
 "frame-benchmarking 30.0.0",
 "frame-support 30.0.0",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 25.0.0",
 "sp-core 30.0.0",
 "sp-io 32.0.0",
 "sp-runtime 33.0.0",
 "sp-std 14.0.0",
 "sp-weights 29.0.0",
 "staging-xcm 9.0.0",
]

[[package]]
name = "staging-xcm-executor"
version = "17.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c89045f495097293ce29df1f3f459e9ccc991ff2ee88a4a91e8110a6886d2c8"
dependencies = [
 "environmental",
 "frame-benchmarking 38.0.0",
 "frame-support 38.2.0",
 "impl-trait-for-tuples",
 "parity-scale-codec",
 "scale-info",
 "sp-arithmetic 26.0.0",
 "sp-core 34.0.0",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "staging-xcm 14.2.0",
 "tracing",
]

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "string-interner"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c6a0d765f5807e98a091107bae0a56ea3799f66a5de47b2c84c94a39c09974e"
dependencies = [
 "cfg-if",
 "hashbrown 0.14.5",
 "serde",
]

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.3",
]

[[package]]
name = "strum"
version = "0.26.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fec0f0aef304996cf250b31b5a10dee7980c85da9d759361292b8bca5a18f06"
dependencies = [
 "strum_macros 0.26.4",
]

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c6bee85a5a24955dc440386795aa378cd9cf82acd5f764469152d2270e581be"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.98",
]

[[package]]
name = "substrate-bip39"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e620c7098893ba667438b47169c00aacdd9e7c10e042250ce2b60b087ec97328"
dependencies = [
 "hmac 0.11.0",
 "pbkdf2 0.8.0",
 "schnorrkel 0.9.1",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "substrate-bip39"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca58ffd742f693dc13d69bdbb2e642ae239e0053f6aab3b104252892f856700a"
dependencies = [
 "hmac 0.12.1",
 "pbkdf2 0.12.2",
 "schnorrkel 0.11.4",
 "sha2 0.10.8",
 "zeroize",
]

[[package]]
name = "substrate-wasm-builder"
version = "24.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf035ffe7335fb24053edfe4d0a5780250eda772082a1b80ae25835dd4c09265"
dependencies = [
 "build-helper",
 "cargo_metadata",
 "console",
 "filetime",
 "jobserver",
 "parity-wasm",
 "polkavm-linker 0.9.2",
 "sp-maybe-compressed-blob",
 "strum 0.26.3",
 "tempfile",
 "toml 0.8.12",
 "walkdir",
 "wasm-opt",
]

[[package]]
name = "subtle"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81cdd64d312baedb58e21336b31bc043b77e01cc99033ce76ef539f78e965ebc"

[[package]]
name = "subxt"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c17d7ec2359d33133b63c97e28c8b7cd3f0a5bc6ce567ae3aef9d9e85be3433"
dependencies = [
 "async-trait",
 "derive-where",
 "either",
 "frame-metadata 17.0.0",
 "futures",
 "hex",
 "impl-serde 0.5.0",
 "jsonrpsee",
 "parity-scale-codec",
 "polkadot-sdk",
 "primitive-types 0.13.1",
 "scale-bits",
 "scale-decode",
 "scale-encode",
 "scale-info",
 "scale-value",
 "serde",
 "serde_json",
 "subxt-core",
 "subxt-lightclient",
 "subxt-macro",
 "subxt-metadata",
 "thiserror",
 "tokio",
 "tokio-util",
 "tracing",
 "url",
 "wasm-bindgen-futures",
 "web-time",
]

[[package]]
name = "subxt-codegen"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6550ef451c77db6e3bc7c56fb6fe1dca9398a2c8fc774b127f6a396a769b9c5b"
dependencies = [
 "heck 0.5.0",
 "parity-scale-codec",
 "proc-macro2",
 "quote",
 "scale-info",
 "scale-typegen",
 "subxt-metadata",
 "syn 2.0.98",
 "thiserror",
]

[[package]]
name = "subxt-core"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7a1bc6c9c1724971636a66e3225a7253cdb35bb6efb81524a6c71c04f08c59"
dependencies = [
 "base58",
 "blake2",
 "derive-where",
 "frame-decode",
 "frame-metadata 17.0.0",
 "hashbrown 0.14.5",
 "hex",
 "impl-serde 0.5.0",
 "keccak-hash",
 "parity-scale-codec",
 "polkadot-sdk",
 "primitive-types 0.13.1",
 "scale-bits",
 "scale-decode",
 "scale-encode",
 "scale-info",
 "scale-value",
 "serde",
 "serde_json",
 "subxt-metadata",
 "tracing",
]

[[package]]
name = "subxt-lightclient"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89ebc9131da4d0ba1f7814495b8cc79698798ccd52cacd7bcefe451e415bd945"
dependencies = [
 "futures",
 "futures-util",
 "serde",
 "serde_json",
 "smoldot-light",
 "thiserror",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "subxt-macro"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7819c5e09aae0319981ee853869f2fcd1fac4db8babd0d004c17161297aadc05"
dependencies = [
 "darling",
 "parity-scale-codec",
 "proc-macro-error2",
 "quote",
 "scale-typegen",
 "subxt-codegen",
 "subxt-utils-fetchmetadata",
 "syn 2.0.98",
]

[[package]]
name = "subxt-metadata"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aacd4e7484fef58deaa2dcb32d94753a864b208a668c0dd0c28be1d8abeeadb2"
dependencies = [
 "frame-decode",
 "frame-metadata 17.0.0",
 "hashbrown 0.14.5",
 "parity-scale-codec",
 "polkadot-sdk",
 "scale-info",
]

[[package]]
name = "subxt-utils-fetchmetadata"
version = "0.38.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3c53bc3eeaacc143a2f29ace4082edd2edaccab37b69ad20befba9fb00fdb3d"
dependencies = [
 "hex",
 "parity-scale-codec",
 "thiserror",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36147f1a48ae0ec2b5b3bc5b537d267457555a10dc06f3dbc8cb11ba3006d3b1"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn-solidity"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b837ef12ab88835251726eb12237655e61ec8dc8a280085d1961cdc3dfd047"
dependencies = [
 "paste",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "synstructure"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36bdaa60a83aca3921b5259d5400cbf5e90fc51931376a9bd4a0eb79aa7210f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "unicode-xid",
]

[[package]]
name = "synstructure"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8af7666ab7b6390ab78131fb5b0fce11d6b7a6951602017c35fa82800708971"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1fc403891a21bcfb7c37834ba66a547a8f402146eba7265b5a6d88059c9ff2f"

[[package]]
name = "tempfile"
version = "3.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a365e8cd18e44762ef95d87f284f4b5cd04107fec2ff3052bd6a3e6069669e67"
dependencies = [
 "cfg-if",
 "fastrand",
 "rustix 0.38.31",
 "windows-sys 0.52.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "testnet-parachains-constants"
version = "10.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94bceae6f7c89d47daff6c7e05f712551a01379f61b07d494661941144878589"
dependencies = [
 "cumulus-primitives-core",
 "frame-support 38.2.0",
 "polkadot-core-primitives 15.0.0",
 "rococo-runtime-constants",
 "smallvec",
 "sp-runtime 39.0.5",
 "staging-xcm 14.2.0",
 "westend-runtime-constants",
]

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "time"
version = "0.3.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35e7868883861bd0e56d9ac6efcaaca0d6d5d82a2a7ec8209ff492c07cf37b21"
dependencies = [
 "deranged",
 "itoa",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef927ca75afb808a4d64dd374f00a2adf8d0fcff8e7b184af886c3c87ec4a3f3"

[[package]]
name = "time-macros"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2834e6017e3e5e4b9834939793b282bc03b37a3336245fa820e35e233e2a85de"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-bip39"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62cc94d358b5a1e84a5cb9109f559aa3c4d634d2b1b4de3d0fa4adc7c78e2861"
dependencies = [
 "anyhow",
 "hmac 0.12.1",
 "once_cell",
 "pbkdf2 0.11.0",
 "rand 0.8.5",
 "rustc-hash 1.1.0",
 "sha2 0.10.8",
 "thiserror",
 "unicode-normalization",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinystr"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9117f5d4db391c1cf6927e7bea3db74b9a1c1add8f7eda9ffd5364f40f57b82f"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinyvec"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87cc5ceb3875bb20c2890005a4e226a4651264a5c75edb2421b52861a0a0cb50"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.43.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d61fa4ffa3de412bfea335c6ecff681de2b609ba3c77ef3e00e521813a9ed9e"
dependencies = [
 "backtrace",
 "libc",
 "mio",
 "pin-project-lite",
 "socket2",
 "tokio-macros",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "tokio-rustls"
version = "0.26.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f6d0975eaace0cf0fcadee4e4aaa5da15b5c079146f2cffb67c113be122bf37"
dependencies = [
 "rustls",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-util"
version = "0.7.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7fcaa8d55a2bdd6b83ace262b016eca0d79ee02818c5c1bcdf0305114081078"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "toml"
version = "0.8.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9dd1545e8208b4a5af1aa9bbd0b4cf7e9ea08fabc5d0a5c67fcaafa17433aa3"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit 0.22.12",
]

[[package]]
name = "toml_datetime"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3550f4e9685620ac18a50ed434eb3aec30db8ba93b0287467bca5826ea25baf1"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.19.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b5bb770da30e5cbfde35a2d7b9b8a2c4b8ef89548a7a6aeab5c9a576e3e7421"
dependencies = [
 "indexmap 2.2.3",
 "toml_datetime",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8534fd7f78b5405e860340ad6575217ce99f38d4d5c8f2442cb5ecb50090e1"
dependencies = [
 "indexmap 2.2.3",
 "toml_datetime",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.22.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3328d4f68a705b2a4498da1d580585d39a6510f98318a2cec3018a7ec61ddef"
dependencies = [
 "indexmap 2.2.3",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "winnow 0.6.2",
]

[[package]]
name = "tracing"
version = "0.1.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3523ab5a71916ccf420eebdf5521fcef02141234bbc0b8a49f2fdc4544364ef"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34704c8d6ebcbc939824180af020566b01a7c01f80641264eba0999f6c2b6be7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "tracing-core"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06d3da6113f116aaee68e4d601191614c9053067f9ab7f6edbcb161237daa54"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-log"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f751112709b4e791d8ce53e32c4ed2d353565a795ce84da2285393f41557bdf2"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-serde"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6b213177105856957181934e4920de57730fc69bf42c37ee5bb664d406d9e1"
dependencies = [
 "serde",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e0d2eaa99c3c2e41547cfa109e910a68ea03823cccad4a0525dcbc9b01e8c71"
dependencies = [
 "ansi_term",
 "chrono",
 "lazy_static",
 "matchers 0.0.1",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "tracing",
 "tracing-core",
 "tracing-log 0.1.4",
 "tracing-serde",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad0f048c97dbd9faa9b7df56362b8ebcaa52adb06b498c050d2f4e32f90a7a8b"
dependencies = [
 "matchers 0.1.0",
 "nu-ansi-term",
 "once_cell",
 "regex",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "time",
 "tracing",
 "tracing-core",
 "tracing-log 0.2.0",
]

[[package]]
name = "trie-db"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff28e0f815c2fea41ebddf148e008b077d2faddb026c9555b29696114d602642"
dependencies = [
 "hash-db",
 "hashbrown 0.13.2",
 "log",
 "rustc-hex",
 "smallvec",
]

[[package]]
name = "trie-db"
version = "0.29.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c992b4f40c234a074d48a757efeabb1a6be88af84c0c23f7ca158950cb0ae7f"
dependencies = [
 "hash-db",
 "log",
 "rustc-hex",
 "smallvec",
]

[[package]]
name = "trie-root"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4ed310ef5ab98f5fa467900ed906cb9232dd5376597e00fd4cba2a449d06c0b"
dependencies = [
 "hash-db",
]

[[package]]
name = "tt-call"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f195fd851901624eee5a58c4bb2b4f06399148fcd0ed336e6f1cb60a9881df"

[[package]]
name = "tuplex"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "676ac81d5454c4dcf37955d34fa8626ede3490f744b86ca14a7b90168d2a08aa"

[[package]]
name = "twox-hash"
version = "1.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fee6b57c6a41524a810daee9286c02d7752c4253064d0b05472833a438f675"
dependencies = [
 "cfg-if",
 "digest 0.10.7",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "typenum"
version = "1.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42ff0bf0c66b8238c6f3b578df37d0b7848e55df8577b3f74f92a69acceeb825"

[[package]]
name = "ucd-trie"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed646292ffc8188ef8ea4d1e0e0150fb15a5c2e12ad9b8fc191ae7a8a7f3c4b9"

[[package]]
name = "uint"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f64bba2c53b04fcab63c01a7d7427eadc821e3bc48c34dc9ba29c501164b52"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "uint"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "909988d098b2f738727b161a106cfc7cab00c539c2687a8836f8e565976fb53e"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unicode-ident"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3354b9ac3fae1ff6755cb6db53683adb661634f67557942dea4facebec0fee4b"

[[package]]
name = "unicode-normalization"
version = "0.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c5713f0fc4b5db668a2ac63cdb7bb4469d8c9fed047b1d0292cc7b0ce2ba921"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-width"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd6e30e90baa6f72411720665d41d89b9a3d039dc45b8faea1ddd07f617f6af"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f962df74c8c05a667b5ee8bcf162993134c104e96440b663c8daa176dc772d8c"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
]

[[package]]
name = "utf16_iter"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8232dd3cdaed5356e0f716d285e4b40b932ac434100fe9b7e0e8e935b9e6246"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "711b9620af191e0cdc7468a8d14e709c3dcdb115b36f838e601583af800a370a"

[[package]]
name = "valuable"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b7e5d4d90034032940e4ace0d9a9a057e7a45cd94e6c007832e39edb82f6d"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "w3f-bls"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7335e4c132c28cc43caef6adb339789e599e39adbe78da0c4d547fad48cbc331"
dependencies = [
 "ark-bls12-377",
 "ark-bls12-381",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-serialize-derive",
 "arrayref",
 "constcat",
 "digest 0.10.7",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "sha2 0.10.8",
 "sha3",
 "thiserror",
 "zeroize",
]

[[package]]
name = "wait-timeout"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f200f5b12eb75f8c1ed65abd4b2db8a6e1b138a20de009dacee265a2498f3f6"
dependencies = [
 "libc",
]

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "wasi"
version = "0.9.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cccddf32554fecc6acb585f82a32a72e28b48f8c4c1883ddfeeeaa96f7d8e519"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "wasm-instrument"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a47ecb37b9734d1085eaa5ae1a81e60801fd8c28d4cabdd8aedb982021918bc"
dependencies = [
 "parity-wasm",
]

[[package]]
name = "wasm-opt"
version = "0.116.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fd87a4c135535ffed86123b6fb0f0a5a0bc89e50416c942c5f0662c645f679c"
dependencies = [
 "anyhow",
 "libc",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "tempfile",
 "thiserror",
 "wasm-opt-cxx-sys",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-cxx-sys"
version = "0.116.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c57b28207aa724318fcec6575fe74803c23f6f266fce10cbc9f3f116762f12e"
dependencies = [
 "anyhow",
 "cxx",
 "cxx-build",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-sys"
version = "0.116.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a1cce564dc768dacbdb718fc29df2dba80bd21cb47d8f77ae7e3d95ceb98cbe"
dependencies = [
 "anyhow",
 "cc",
 "cxx",
 "cxx-build",
]

[[package]]
name = "wasmi"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50386c99b9c32bd2ed71a55b6dd4040af2580530fae8bdb9a6576571a80d0cca"
dependencies = [
 "arrayvec 0.7.4",
 "multi-stash",
 "num-derive",
 "num-traits",
 "smallvec",
 "spin",
 "wasmi_collections",
 "wasmi_core",
 "wasmparser-nostd",
]

[[package]]
name = "wasmi_collections"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c128c039340ffd50d4195c3f8ce31aac357f06804cfc494c8b9508d4b30dca4"
dependencies = [
 "ahash 0.8.11",
 "hashbrown 0.14.5",
 "string-interner",
]

[[package]]
name = "wasmi_core"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a23b3a7f6c8c3ceeec6b83531ee61f0013c56e51cbf2b14b0f213548b23a4b41"
dependencies = [
 "downcast-rs",
 "libm",
 "num-traits",
 "paste",
]

[[package]]
name = "wasmparser"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48134de3d7598219ab9eaf6b91b15d8e50d31da76b8519fe4ecfcec2cf35104b"
dependencies = [
 "indexmap 1.9.3",
 "url",
]

[[package]]
name = "wasmparser-nostd"
version = "0.100.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5a015fe95f3504a94bb1462c717aae75253e39b9dd6c3fb1062c934535c64aa"
dependencies = [
 "indexmap-nostd",
]

[[package]]
name = "wasmtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f907fdead3153cb9bfb7a93bbd5b62629472dc06dee83605358c64c52ed3dda9"
dependencies = [
 "anyhow",
 "bincode",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "object 0.30.4",
 "once_cell",
 "paste",
 "psm",
 "rayon",
 "serde",
 "target-lexicon",
 "wasmparser",
 "wasmtime-cache",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "wasmtime-jit",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-asm-macros"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3b9daa7c14cd4fa3edbf69de994408d5f4b7b0959ac13fa69d465f6597f810d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "wasmtime-cache"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c86437fa68626fe896e5afc69234bb2b5894949083586535f200385adfd71213"
dependencies = [
 "anyhow",
 "base64 0.21.7",
 "bincode",
 "directories-next",
 "file-per-thread-logger",
 "log",
 "rustix 0.36.17",
 "serde",
 "sha2 0.10.8",
 "toml 0.5.11",
 "windows-sys 0.45.0",
 "zstd 0.11.2+zstd.1.5.2",
]

[[package]]
name = "wasmtime-cranelift"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1cefde0cce8cb700b1b21b6298a3837dba46521affd7b8c38a9ee2c869eee04"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "cranelift-native",
 "cranelift-wasm",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "target-lexicon",
 "thiserror",
 "wasmparser",
 "wasmtime-cranelift-shared",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-cranelift-shared"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd041e382ef5aea1b9fc78442394f1a4f6d676ce457e7076ca4cb3f397882f8b"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-native",
 "gimli 0.27.3",
 "object 0.30.4",
 "target-lexicon",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-environ"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a990198cee4197423045235bf89d3359e69bd2ea031005f4c2d901125955c949"
dependencies = [
 "anyhow",
 "cranelift-entity",
 "gimli 0.27.3",
 "indexmap 1.9.3",
 "log",
 "object 0.30.4",
 "serde",
 "target-lexicon",
 "thiserror",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "wasmtime-jit"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0de48df552cfca1c9b750002d3e07b45772dd033b0b206d5c0968496abf31244"
dependencies = [
 "addr2line 0.19.0",
 "anyhow",
 "bincode",
 "cfg-if",
 "cpp_demangle",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "rustc-demangle",
 "serde",
 "target-lexicon",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "wasmtime-jit-icache-coherence",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-jit-debug"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e0554b84c15a27d76281d06838aed94e13a77d7bf604bbbaf548aa20eb93846"
dependencies = [
 "object 0.30.4",
 "once_cell",
 "rustix 0.36.17",
]

[[package]]
name = "wasmtime-jit-icache-coherence"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aecae978b13f7f67efb23bd827373ace4578f2137ec110bbf6a4a7cde4121bbd"
dependencies = [
 "cfg-if",
 "libc",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-runtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658cf6f325232b6760e202e5255d823da5e348fdea827eff0a2a22319000b441"
dependencies = [
 "anyhow",
 "cc",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "mach",
 "memfd",
 "memoffset",
 "paste",
 "rand 0.8.5",
 "rustix 0.36.17",
 "wasmtime-asm-macros",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-types"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4f6fffd2a1011887d57f07654dd112791e872e3ff4a2e626aee8059ee17f06f"
dependencies = [
 "cranelift-entity",
 "serde",
 "thiserror",
 "wasmparser",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki-roots"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2210b291f7ea53617fbafcc4939f10914214ec15aace5ba62293a668f322c5c9"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "westend-runtime"
version = "0.1.0"
dependencies = [
 "parity-scale-codec",
 "scale-info",
 "serde",
 "subxt",
]

[[package]]
name = "westend-runtime-constants"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06861bf945aadac59f4be23b44c85573029520ea9bd3d6c9ab21c8b306e81cdc"
dependencies = [
 "frame-support 38.2.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-common 17.0.0",
 "smallvec",
 "sp-core 34.0.0",
 "sp-runtime 39.0.5",
 "sp-weights 31.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
]

[[package]]
name = "wide"
version = "0.7.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a040b111774ab63a19ef46bbc149398ab372b4ccdcfd719e9814dbd7dfd76c8"
dependencies = [
 "bytemuck",
 "safe_arch",
]

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f29e6f9198ba0d26b4c9f07dbe6f9ed633e1f3d5b8b414090084349e46a52596"
dependencies = [
 "winapi",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows-core"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33ab640c8d7e35bf8ba19b884ba838ceb4fba93a4e8c65a9059d08afcfc683d9"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "winnow"
version = "0.5.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f593a95398737aeed53e489c785df13f3618e41dbcd6718c6addbf1395aa6876"
dependencies = [
 "memchr",
]

[[package]]
name = "winnow"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a4191c47f15cc3ec71fcb4913cb83d58def65dd3787610213c649283b5ce178"
dependencies = [
 "memchr",
]

[[package]]
name = "write16"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1890f4022759daae28ed4fe62859b1236caebfc61ede2f63ed4e695f3f6d936"

[[package]]
name = "writeable"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9df38ee2d2c3c5948ea468a8406ff0db0b29ae1ffde1bcf20ef305bcc95c51"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "x25519-dalek"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7e468321c81fb07fa7f4c636c3972b9100f0346e5b6a9f2bd0603a52f7ed277"
dependencies = [
 "curve25519-dalek 4.1.3",
 "rand_core 0.6.4",
 "serde",
 "zeroize",
]

[[package]]
name = "xcm-procedural"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d69991e802386307acc13e9d67004da522d083fe338111fe79c4317878445e4"
dependencies = [
 "Inflector",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "xcm-procedural"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4717a97970a9cda70d7db53cf50d2615c2f6f6b7c857445325b4a39ea7aa2cd"
dependencies = [
 "Inflector",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "xcm-procedural"
version = "10.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87fb4f14094d65c500a59bcf540cf42b99ee82c706edd6226a92e769ad60563e"
dependencies = [
 "Inflector",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "xcm-runtime-apis"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f3d96bd7362d9e6884ef6762f08737d89205a358d059a0451353f3e91985ca5"
dependencies = [
 "frame-support 38.2.0",
 "parity-scale-codec",
 "scale-info",
 "sp-api 34.0.0",
 "sp-weights 31.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "xcm-simulator"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "058e21bfc3e1180bbd83cad3690d0e63f34f43ab309e338afe988160aa776fcf"
dependencies = [
 "frame-support 38.2.0",
 "frame-system 38.0.0",
 "parity-scale-codec",
 "paste",
 "polkadot-core-primitives 15.0.0",
 "polkadot-parachain-primitives 14.0.0",
 "polkadot-primitives 16.0.0",
 "polkadot-runtime-parachains 17.0.2",
 "scale-info",
 "sp-io 38.0.0",
 "sp-runtime 39.0.5",
 "sp-std 14.0.0",
 "staging-xcm 14.2.0",
 "staging-xcm-builder 17.0.4",
 "staging-xcm-executor 17.0.1",
]

[[package]]
name = "yap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff4524214bc4629eba08d78ceb1d6507070cc0bcbbed23af74e19e6e924a24cf"

[[package]]
name = "yoke"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "120e6aef9aa629e3d4f52dc8cc43a015c7724194c97dfaf45180d2daf2b77f40"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2380878cad4ac9aac1e2435f3eb4020e8374b5f13c296cb75b4620ff8e229154"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "synstructure 0.13.1",
]

[[package]]
name = "zerocopy"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74d4d3961e53fa4c9a25a8637fc2bfaf2595b3d3ae34875568a5cf64787716be"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce1b18ccd8e73a9321186f97e46f9f04b778851177567b1975109d26a08d2a6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "synstructure 0.13.1",
]

[[package]]
name = "zeroize"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "525b4ec142c6b68a2d10f01f7bbf6755599ca3f81ea53b8431b7dd348f5fdb2d"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zerovec"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2b893d79df23bfb12d5461018d408ea19dfafe76c2c7ef6d4eba614f8ff079"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eafa6dfb17584ea3e2bd6e76e0cc15ad7af12b09abdd1ca55961bed9b1063c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a27595e173641171fc74a1232b7b1c7a7cb6e18222c11e9dfb9888fa424c53c"
dependencies = [
 "zstd-safe 6.0.6",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "6.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee98ffd0b48ee95e6c5168188e44a54550b1564d9d530ee21d5f0eaed1069581"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.14+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fb060d4926e4ac3a3ad15d864e99ceb5f343c6b34f5bd6d81ae6ed417311be5"
dependencies = [
 "cc",
 "pkg-config",
]
